package com.fshows.gosh.web.domain.request.operation.bloc;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class EditBlocStatusRequest {

    /**
     * 集团 id
     */
    @NotBlank(message = "blocId不能为空")
    private String blocId;

    /**
     * 1正常 2禁用
     */
    @NotNull(message = "blocStatus不能为空")
    private Integer blocStatus;
}
