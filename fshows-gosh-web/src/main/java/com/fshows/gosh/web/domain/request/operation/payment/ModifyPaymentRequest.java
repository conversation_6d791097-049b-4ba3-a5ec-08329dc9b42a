/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.web.domain.request.operation.payment;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version PageAccountListRequest.java, v 0.1 2024-08-26 11:48 AM ruanzy
 */
@Data
public class ModifyPaymentRequest {

    /**
     * 结算单号
     */
    @NotBlank
    private String serialNo;

    /**
     * 普通券金额
     */
    private BigDecimal nonBusinessAmount;

    /**
     * 母券金额
     */
    private BigDecimal businessMotherCardAmount;

    /**
     * 掉单金额金额
     */
    private BigDecimal diffAmount;

    /**
     * 差异佣金金额
     */
    private BigDecimal diffCommission;

    /**
     * 普通券正向差异金额
     */
    private BigDecimal incomeDiffAmount;

    /**
     * 普通券逆向差异金额
     */
    private BigDecimal deductDiffAmount;

    /**
     * 母券正向差异金额
     */
    private BigDecimal motherIncomeDiffAmount;

    /**
     * 母券逆向差异金额
     */
    private BigDecimal motherDeductDiffAmount;

    /**
     * 外部商品出款金额
     */
    private BigDecimal outProductSettleAmount;

    /**
     * 备注说明
     */
    private String remark;
}