package com.fshows.gosh.web.domain.request.operation.bloc;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AddBlocRequest {


    /* ---------------------------------基本信息------------------------------  */

    /**
     * 集团名称
     */
    @NotBlank(message = "blocName不能为空")
    private String blocName;

    /**
     * 集团应用appId
     */
    @NotBlank(message = "ailikeAppId不能为空")
    private String ailikeAppId;

    /**
     * 集团主账号
     */
    @NotBlank(message = "accountName不能为空")
    private String accountName;

    /**
     * 集团联系人
     */
    private String accountContact;

    /**
     * 集团电话
     */
    private String blocPhoneNumber;


    /* ---------------------------------付呗信息------------------------------  */

    /**
     * 付呗集团ID
     */
    @NotBlank(message = "fsBlocId不能为空")
    private String fsBlocId;

    /**
     * 付呗代理商ID
     */
    private String fsAgentId;

    /**
     * 付呗组织名称
     */
    @NotBlank(message = "fsTopOrgName不能为空")
    private String fsTopOrgName;

    /**
     * 付呗组织id
     */
    @NotBlank(message = "fsTopOrgId不能为空")
    private String fsTopOrgId;

    /* ---------------------------------中台信息------------------------------  */

    /**
     * 中台钱包ID
     */
    @NotBlank(message = "acctAppid不能为空")
    private String acctAppid;

    /**
     * 平台钱包列表
     */
    @NotEmpty(message = "platformWalletList不能为空")
    private List<PlatformWalletRequest> platformWalletList;

    /**
     * 平台钱包信息
     */
    @Data
    public static class PlatformWalletRequest {

        /**
         * 平台类型
         */
        @NotBlank(message = "channelType不能为空")
        private String channelType;

        /**
         * 平台钱包ID
         */
        @NotBlank(message = "platformWalletId不能为空")
        private String platformWalletId;

        /**
         * 关联平台ID
         */
        @NotBlank(message = "relationBalanceId不能为空")
        private String relationBalanceId;

    }

}
