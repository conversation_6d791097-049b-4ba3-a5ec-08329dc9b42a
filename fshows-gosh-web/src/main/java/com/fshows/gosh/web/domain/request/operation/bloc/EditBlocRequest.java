package com.fshows.gosh.web.domain.request.operation.bloc;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class EditBlocRequest {

    /* ---------------------------------基本信息------------------------------  */

    /**
     * 集团id
     */
    @NotBlank(message = "blocId不能为空")
    private String blocId;

    /**
     * 集团名称
     */
    @NotBlank(message = "blocName不能为空")
    private String blocName;

    /**
     * 集团主账号
     */
    private String accountName;

    /**
     * 集团联系人
     */
    private String accountContact;

    /**
     * 集团电话
     */
    private String blocPhoneNumber;

    /**
     * 平台通道钱包列表
     */
    private List<PlatformWallet> platformWalletList;


    /**
     * 平台钱包
     */
    @Data
    public static class PlatformWallet {

        // {channelType: "TIKTOK", platformWalletId: "V20240626172004373467", relationBalanceId: ""}

        /**
         * 平台类型
         */
        private String channelType;

        /**
         * 平台钱包
         */
        private String platformWalletId;

        /**
         * 关联余额id
         */
        private String relationBalanceId;

        /**
         * 是否新增
         */
        private boolean isAdd;
    }
}
