/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.gosh.web.domain.response.mina.merchant.verifycoupon;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version ScanCodeCouponResponse.java, v 0.1 2022-11-08 9:45 AM ruanzy
 */
@Data
public class ScanCodeCouponResponse {

    /**
     * 验券的标识
     */
    private String verifyToken;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 子商品 id
     */
    private String subOrderId;

    /**
     * 券码有效期
     */
    private String expireTime;

    /**
     * 券码类型
     */
    private Integer couponType;

    /**
     * 可用券列表
     */
    private List<CertificatesResponse> certificates;
}