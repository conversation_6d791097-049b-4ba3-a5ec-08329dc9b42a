/**
 * <AUTHOR>
 * @date 2024/11/25 17:23
 * @version 1.0 SearchOperationRoleListRequest
 */
package com.fshows.gosh.web.domain.request.operation.admin;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version SearchOperationRoleListRequest.java, v 0.1 2024-11-25 17:23 tuyuwei
 */
@Data
public class SearchOperationRoleListRequest {

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称")
    private String roleName;


    /**
     * 角色状态:1正常,2禁用
     */
    @NotBlank(message = "角色状态")
    private Integer roleStatus;

    /**
     * 账号类型:1-全集团账号；2-部分集团权限账号
     */
    @NotNull(message = "管理范围类型不能为空")
    private Integer accountType;
}