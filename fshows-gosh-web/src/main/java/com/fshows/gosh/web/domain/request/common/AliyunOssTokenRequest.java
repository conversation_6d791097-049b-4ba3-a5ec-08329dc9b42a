/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.gosh.web.domain.request.common;

import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * <AUTHOR>
 * @version AliyunOssTokenRequest.java, v 0.1 2022-09-06 zhangling
 */
@Data
public class AliyunOssTokenRequest {

    /**
     * 目录
     */
    private String dir;
    /**
     * 是否开启 https
     */
    private boolean enableSsl;
    /**
     * 是否私有
     */
    private boolean enablePrivate;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.reflectionToString(this,
                ToStringStyle.SHORT_PREFIX_STYLE);
    }

}