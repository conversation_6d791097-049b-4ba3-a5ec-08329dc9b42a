/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.web.domain.response.bloc.login;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version WebBlocLoginResponse.java, v 0.1 2024-06-20 9:58 AM ruanzy
 */
@Data
public class WebBlocLoginResponse {

    /**
     * 授权token
     */
    private String accessToken;

    /**
     * 集团id
     */
    private String blocId;

    /**
     * 集团名称
     */
    private String blocName;

    /**
     * 账号id
     */
    private String accountId;

    /**
     * 账号
     */
    private String account;

    /**
     * 当前登录的组织id
     */
    private String orgId;

    /**
     * 当前登录的组织名称
     */
    private String orgName;

    /**
     * 当前登录的组织层级
     */
    private Integer orgHierarchy;

    /**
     * 功能权限列表
     */
    private List<String> funcList;

    /**
     * 菜单权限列表
     */
    private List<String> menuList;

    /**
     * 服务商ID
     */
    private String appId;

    /**
     * 商家Id
     */
    private String merchantAgentId;

    /**
     * 抖音来客Id
     */
    private String tiktokLifeId;

    /**
     * 是否是管理员
     */
    private boolean isAdmin;

    /**
     * 绑定的组织
     */
    private List<LoginOrgInfoResponse> orgInfoList;
}