package com.fshows.gosh.web.domain.request.operation.refund;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * SubVoucherRequest
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/9
 */
@Data
public class RefundFinishRequest {

    /**
     * 售后订单id
     */
    @NotBlank(message = "售后订单id不能为空")
    private String afterSaleId;


    /**
     * 退款处理状态：2-来客已同意；3-来客已拒绝
     */
    @NotNull(message = "退款处理状态不能为空")
    private Integer refundDealStatus;
}