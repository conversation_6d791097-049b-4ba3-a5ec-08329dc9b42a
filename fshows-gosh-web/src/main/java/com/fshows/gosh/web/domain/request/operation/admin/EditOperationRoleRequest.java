/**
 * <AUTHOR>
 * @date 2024/11/25 16:15
 * @version 1.0 EditOperationRoleRequest
 */
package com.fshows.gosh.web.domain.request.operation.admin;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version EditOperationRoleRequest.java, v 0.1 2024-11-25 16:15 tuyuwei
 */
@Data
public class EditOperationRoleRequest {

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称")
    private String roleName;

    /**
     * 角色id
     */
    @NotBlank(message = "角色id")
    private String roleId;

    /**
     * 角色备注
     */
    @NotBlank(message = "角色备注")
    private String remark;

    /**
     * 账号类型:1-全集团；2-部分集团
     */
    private Integer accountType;

    /**
     * 权限id集合
     */
    @NotEmpty(message = "权限id集合")
    private List<String> grantIdList;
}