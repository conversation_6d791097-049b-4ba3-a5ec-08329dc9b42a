package com.fshows.gosh.web.domain.request.operation.refund;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.util.StringUtils;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * PaymentAuditRequest
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/9
 */
@Data
public class OfflinePaymentApplyRequest {

    /**
     * 退款申请类型：0-线下（默认） 1-线上
     */
    private Integer applyRefundType;

    /**
     * 打款id
     */
    private String paymentId;

    /**
     * 订单编号
     */
    @NotBlank(message = "订单编号不能为空")
    private String orderId;

    /**
     * 商圈卡券码
     */
    @NotEmpty(message = "商圈卡券码不能为空")
    private List<String> couponCodeList;

    /**
     * 售卖金额
     */
    @NotNull(message = "售卖金额不能为空")
    private BigDecimal couponSaleAmount;

    /**
     * 用户实付
     */
    @NotNull(message = "用户实付不能为空")
    private BigDecimal cash;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 剩余金额
     */
    private BigDecimal remainAmount;

    /**
     * 子券结算金额
     */
    @NotNull
    private BigDecimal subSettledAmount;

    /**
     * 支付宝账号
     */
    private String alipayAccount;

    /**
     * 支付宝姓名
     */
    private String alipayName;

    /**
     * 退款原因
     */
    @NotBlank
    private String refundReason;

    /**
     * 退款截图
     */
    private List<String> refundImg;

    /**
     * 备注说明
     */
    private String remark;

    public void validate() {
        // 线下退款
        if (Objects.equals(ObjectUtil.defaultIfNull(applyRefundType, CommonConstant.ZERO), CommonConstant.ZERO)) {
            if (refundAmount == null) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("退款金额不能为空");
            }
            if (remainAmount == null) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("剩余金额不能为空");
            }
            if (alipayAccount == null) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("支付宝账号不能为空");
            }
            if (alipayName == null) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("支付宝姓名不能为空");
            }
        }
    }
}