/**
 * <AUTHOR>
 * @date 2024/12/26 17:54
 * @version 1.0 GetEffectSettleRequest
 */
package com.fshows.gosh.web.domain.request.operation.account;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 *
 *
 * <AUTHOR>
 * @version GetEffectSettleRequest.java, v 0.1 2024-12-26 17:54 tuyuwei
 */
@Data
public class GetEffectSettleRequest {

    /**
     * 商铺id
     */
    @NotBlank(message = "商铺id不可为空")
    private String shopId;
}