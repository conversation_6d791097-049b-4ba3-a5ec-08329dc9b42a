package com.fshows.gosh.web.domain.request.operation.refund;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * PaymentAuditRequest
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/9
 */
@Data
public class PaymentAuditRequest {
    /**
     * 打款id列表
     */
    @NotEmpty(message = "打款id列表不能为空")
    private List<String> paymentIdList;

    /**
     * 审核结果:1-未打款; 2-已打款; 3-打款失败
     */
    @NotNull
    private Integer auditStatus;

    /**
     * 备注说明
     */
    private String remark;
}