package com.fshows.gosh.web.domain.request.operation.bloc;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class EditBlocGrantRequest {

    /**
     * 集团后台绑定的grantId列表
     */
    @NotEmpty(message = "blocBindGrantIdList不能为空")
    private List<String> blocBindGrantIdList;

    /**
     * 小程序后台绑定的grantId列表
     */
    @NotEmpty(message = "blocMinaBindGrantIdList不能为空")
    private List<String> blocMinaBindGrantIdList;

    /**
     * 集团 id
     */
    @NotBlank(message = "blocId不能为空")
    private String blocId;

}
