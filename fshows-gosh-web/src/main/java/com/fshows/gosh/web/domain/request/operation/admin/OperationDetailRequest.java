/**
 * <AUTHOR>
 * @date 2024/12/12 16:04
 * @version 1.0 OperationDetailRequest
 */
package com.fshows.gosh.web.domain.request.operation.admin;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 *
 *
 * <AUTHOR>
 * @version OperationDetailRequest.java, v 0.1 2024-12-12 16:04 tuyuwei
 */
@Data
public class OperationDetailRequest {

    /**
     * 运营id
     */
    @NotBlank(message = "运营id不能为空")
    private String operatorId;
}