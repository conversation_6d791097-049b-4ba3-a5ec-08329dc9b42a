package com.fshows.gosh.web.domain.request.operation.refund;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * SaveOperationRecordRequest
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/14
 */
@Data
public class SaveOperationRecordRequest {

    /**
     * 打款 id
     */
    @NotBlank(message = "打款id不能为空")
    private String paymentId;

    /**
     * 操作类型 1 发起团款申请 2 重新发起退款申请 3 打款完成 4 打款失败
     */
    @NotNull(message = "操作类型不能为空")
    private Integer operationType;
}
