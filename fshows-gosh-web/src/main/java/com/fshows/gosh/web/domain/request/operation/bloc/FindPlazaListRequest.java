package com.fshows.gosh.web.domain.request.operation.bloc;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class FindPlazaListRequest {

    /**
     * 组织 id
     */
    private String orgId;

    /**
     * 集团 id
     */
    private String blocId;


    /**
     * 查询来源 1配置 poi 2配置服务台 3下拉筛选项
     */
    @NotNull(message = "querySource不能为空")
    private Integer querySource;

}
