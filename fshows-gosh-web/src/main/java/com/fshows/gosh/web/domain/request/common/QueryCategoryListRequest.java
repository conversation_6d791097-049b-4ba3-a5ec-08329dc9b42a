/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.fshows.gosh.web.domain.request.common;

import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class QueryCategoryListRequest {

    /**
     * 商户：product 门店：store
     */
    private String categoryType;

    /**
     * 类目级别：不传获取全部类目   当categoryType为store且categoryLevel为2的时候   查询门店类目树状结构 1，2级别
     */
    private Integer categoryLevel;
}