package com.fshows.gosh.web.domain.request.operation.productOrder;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ProductOrderCountRequest {

    /**
     * 广场id列表
     */
    private List<String> orgIdList;

    /**
     * 统计时间-开始时间
     */
    private String startDate;

    /**
     * 统计时间-结束时间
     */
    private String endDate;

    /**
     * 订单编号
     */
    private String outOrderSn;

    /**
     * 搜索关键词（商品名称/商品ID）
     */
    private String keyword;

    /**
     * tab栏类型（调用“商品订单数统计”接口忽略该字段）
     * 0 全部
     * 1 未核销
     * 2 已核销
     * 3 已退款
     */
    private Integer tabFlag;

    /**
     * 带货角色：商家 / 达人
     */
    private String goodsRole;

    /**
     * 成交渠道：短视频 / 直播 / 其他
     */
    private String tradeChannel;

    /**
     * 发布平台
     */
    private String channelType;

    /**
     * 券类型 1-团购券  11-代金券
     */
    private String productType;

    /**
     * 商圈卡商品类型0非商圈卡  1商圈母卡 2商圈子卡
     */
    private Integer orgProductType;

    /**
     * 顾客电话
     */
    private String customerPhoneNumber;

    /**
     * 查询来源  1 运营后台
     */
    private Integer querySource;

    /**
     * 集团 id
     */
    private String blocId;
}
