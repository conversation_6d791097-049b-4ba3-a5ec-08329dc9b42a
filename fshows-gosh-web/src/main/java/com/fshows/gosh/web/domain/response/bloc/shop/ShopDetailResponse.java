package com.fshows.gosh.web.domain.response.bloc.shop;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年06月05日 20:18
 */
@Data
public class ShopDetailResponse {

    /**
     * 商铺id
     */
    private String shopId;

    /**
     * 商铺名称
     */
    private String shopName;

    /**
     * 类目名称（多级）
     */
    private String categoryName;

    /**
     * 广场id
     */
    private String orgId;

    /**
     * 广场名称
     */
    private String orgName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 确认状态  1未确认 2已确认
     */
    private Integer confirmStatus;

    /**
     * 运营人员姓名
     */
    private String adminName;

    /**
     * 运营人员手机号
     */
    private String adminPhone;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 一级类目Id
     */
    private String oneCategoryId;

    /**
     * 二级类目Id
     */
    private String twoCategoryId;

    /**
     * 三级类目id
     */
    private String threeCategoryId;


    /**
     * 开户状态
     * 枚举值：0未开户、1审核失败、2审核中、3审核通过
     */
    private Integer applyStatus;

    /**
     * 门店隐藏状态:1- 隐藏； 0-正常展示
     */
    private Integer hideStatus;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 银行账号类型 1-法人对私卡 2-企业对公户
     */
    private Integer settleAccountType;

    /**
     * 法人姓名
     */
    private String legalName;

    /**
     * 结算银行卡号
     */
    private String settleAccountNo;

    /**
     * 银行总行名称
     */
    private String settleBankName;

    /**
     * 开户支行联行号
     */
    private String settleBankBranchCode;

    /**
     * 广场下商铺poiId
     */
    private String mallPoiStr;
}
