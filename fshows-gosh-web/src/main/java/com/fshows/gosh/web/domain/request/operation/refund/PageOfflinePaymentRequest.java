package com.fshows.gosh.web.domain.request.operation.refund;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * PageOfflinePaymentRequest
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/9
 */
@Data
public class PageOfflinePaymentRequest {

    /**
     * 打款状态：1-申请打款; 2-完成打款；3-线上退款
     */
    @NotNull
    private Integer paymentStatus;

    /**
     * 申请开始时间
     */
    private String startTime;

    /**
     * 申请结束时间
     */
    private String endTime;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 集团id
     */
    private String blocId;

    /**
     * 组织Id
     */
    private String orgId;

    /**
     * 商圈卡关键字
     */
    private String productKeyWord;

    /**
     * 商圈卡券码
     */
    private String couponCode;

    /**
     * 商家券状态 1-已使用;2-未使用'
     */
    private Integer merchantCouponStatus;

    /**
     * 提交人
     */
    private String submit;

    /**
     * 顾客手机号
     */
    private String customerPhone;

    /**
     * 支付宝账号
     */
    private String alipayAccount;

    /**
     * 支付宝姓名
     */
    private String alipayName;
}