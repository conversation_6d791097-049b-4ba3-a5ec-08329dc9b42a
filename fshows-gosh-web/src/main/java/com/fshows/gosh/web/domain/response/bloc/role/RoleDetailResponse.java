package com.fshows.gosh.web.domain.response.bloc.role;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年06月20日 14:37
 */
@Data
public class RoleDetailResponse {

    /**
     * 角色id
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色备注
     */
    private String roleRemark;

    /**
     * 生效组织id
     */
    private String orgId;

    /**
     * 生效组织名称
     */
    private String orgName;

    /**
     * 角色员工数量
     */
    private Integer userCount;

    /**
     * 角色状态
     */
    private Integer roleStatus;

    /**
     * 创建人员id
     */
    private String createAccountId;

    /**
     * 创建人员名称
     */
    private String createAccountName;

    /**
     * 权限idList
     */
    private List<String> grantIdList;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 修改人
     */
    private String updateAccount;


}
