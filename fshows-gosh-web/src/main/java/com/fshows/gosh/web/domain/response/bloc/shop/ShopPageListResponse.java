package com.fshows.gosh.web.domain.response.bloc.shop;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年06月05日 18:07
 */
@Data
public class ShopPageListResponse {

    /**
     * 商铺id
     */
    private String shopId;

    /**
     * 商铺名称
     */
    private String shopName;

    /**
     * 类目名称（多级）
     */
    private String categoryName;

    /**
     * 广场id
     */
    private String orgId;

    /**
     * 广场名称
     */
    private String orgName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 确认状态  1未确认 2已确认
     */
    private Integer confirmStatus;

    /**
     * 门店Id
     */
    private Integer storeId;

    /**
     * 开户状态
     * 枚举值：0未开户、1审核失败、2审核中、3审核通过 4待激活
     */
    private Integer applyStatus;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 门店隐藏状态:1- 隐藏； 0-正常展示
     */
    private Integer hideStatus;

    /**
     * 结算状态：1-可结算；2-冻结结算
     */
    private Integer settleStatus;

    /**
     * 抖音场内户的POI
     */
    private String mallPoi;

}
