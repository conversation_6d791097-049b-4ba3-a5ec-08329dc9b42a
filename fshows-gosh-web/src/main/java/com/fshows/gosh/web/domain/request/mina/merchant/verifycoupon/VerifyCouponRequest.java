/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.web.domain.request.mina.merchant.verifycoupon;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version VerifyCouponRequest.java, v 0.1 2024-06-06 5:32 PM ruanzy
 */
@Data
public class VerifyCouponRequest {

    /**
     * 验券的标识
     */
    private String verifyToken;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderId;

    /**
     * 子商品 id
     */
    private String subOrderId;

    /**
     * 可用券列表
     */
    @NotEmpty(message = "可用券列表不能为空")
    private List<VerifyCouponCertificatesRequest> certificates;
}