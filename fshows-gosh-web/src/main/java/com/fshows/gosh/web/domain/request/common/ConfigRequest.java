package com.fshows.gosh.web.domain.request.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0 ConfigParam
 * @date 2022/10/21 18:22
 */
@Data
public class ConfigRequest {

    /**
     * 通道
     */
    private String  channelType;

    /**
     * 类型
     */
    @NotBlank(message = "类型不能为空")
    private String type;
}
