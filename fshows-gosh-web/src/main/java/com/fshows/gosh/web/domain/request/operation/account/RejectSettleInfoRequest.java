/**
 * <AUTHOR>
 * @date 2024/12/26 18:04
 * @version 1.0 rejectSettleInfoRequest
 */
package com.fshows.gosh.web.domain.request.operation.account;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @version rejectSettleInfoRequest.java, v 0.1 2024-12-26 18:04 tuyuwei
 */
@Data
public class RejectSettleInfoRequest {


    /**
     * 商铺id
     */
    @NotBlank(message = "商铺id不可为空")
    private String shopId;


    /**
     * 驳回原因
     */
    @NotBlank(message = "驳回原因不可为空")
    private String rejectedReason;

    /**
     * 截图说明
     */
    @NotEmpty(message = "截图说明不可为空")
    private List<String> rejectedDescribePicture;


}