package com.fshows.gosh.web.domain.request.operation.storeReconciliation;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class StoreStatisticsRequest {

    /**
     * 计算日期 格式 20241011
     */
    @NotBlank(message = "calculateDay不能为空")
    private String calculateDay;

    /**
     * 统计平台
     */
    private String channelType;

    /**
     * 门店 id 或门店名称
     */
    private String shopContent;

    /**
     * 集团Id
     */
    private String blocId;

    /**
     * 广场 id
     */
    private String orgId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 商品名称 or 商品 id
     */
    private String productContent;

    /**
     * 手机号
     */
    private String phoneNumber;

}
