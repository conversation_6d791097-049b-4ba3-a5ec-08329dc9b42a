/**
 * <AUTHOR>
 * @date 2024/12/26 18:23
 * @version 1.0 ExamineRecordsRequest
 */
package com.fshows.gosh.web.domain.request.operation.account;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 *
 *
 * <AUTHOR>
 * @version ExamineRecordsRequest.java, v 0.1 2024-12-26 18:23 tuyuwei
 */
@Data
public class ExamineRecordsRequest {

    /**
     * 商铺id
     */
    @NotBlank(message = "商铺id不可为空")
    private String shopId;


}