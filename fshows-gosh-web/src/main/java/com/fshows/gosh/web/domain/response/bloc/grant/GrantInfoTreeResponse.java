package com.fshows.gosh.web.domain.response.bloc.grant;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GrantInfoTreeResponse {

    /**
     * 权限ID
     */
    private String grantId;
    /**
     * 权限名称
     */
    private String grantName;

    /**
     * 权限编码
     */
    private String grantCode;

    /**
     * 权限类型(1菜单,2功能)
     */
    private String grantType;

    /**
     * 子权限
     */
    private List<GrantInfoTreeResponse> children;
}
