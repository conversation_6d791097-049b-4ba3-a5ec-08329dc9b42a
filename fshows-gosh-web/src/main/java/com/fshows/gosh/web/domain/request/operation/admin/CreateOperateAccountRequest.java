/**
 * <AUTHOR>
 * @date 2024/11/1 11:30
 * @version 1.0 CreateOperateAccountRequest
 */
package com.fshows.gosh.web.domain.request.operation.admin;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version CreateOperateAccountRequest.java, v 0.1 2024-11-01 11:30 tuyuwei
 */
@Data
public class CreateOperateAccountRequest {

    /**
     * 账号id
     */
    @NotBlank(message = "账号不能为空")
    private String account;

    /**
     * 密码
     */
    private String password;

    /**
     * 角色id
     */
    @NotEmpty(message = "角色id")
    private List<String> roleIdList;


    /**
     * 1-新增 2-编辑
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 姓名
     */
    @NotNull(message = "姓名不能为空")
    private String personName;

    /**
     * 手机号
     */
    @NotNull(message = "手机号不能为空")
    private String phone;

    /**
     * 明文权限 0-关闭 1-开启
     */
    @NotNull(message = "明文权限开关不能为空")
    private Integer plaintext;

    /**
     * 账号类型:1-全集团账号；2-部分集团权限账号
     */
    @NotNull(message = "管理范围不能为空")
    private Integer accountType;

    /**
     * 集团id列表
     */
    private List<String> blocIdList;
}