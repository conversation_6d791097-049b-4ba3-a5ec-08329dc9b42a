/**
 * <AUTHOR>
 * @date 2024/11/27 10:45
 * @version 1.0 SearchOperationRoleDetailRequest
 */
package com.fshows.gosh.web.domain.request.operation.admin;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 *
 *
 * <AUTHOR>
 * @version SearchOperationRoleDetailRequest.java, v 0.1 2024-11-27 10:45 tuyuwei
 */
@Data
public class SearchOperationRoleDetailRequest {

    /**
     * 角色id
     */
    @NotBlank(message = "角色id")
    private String roleId;
}