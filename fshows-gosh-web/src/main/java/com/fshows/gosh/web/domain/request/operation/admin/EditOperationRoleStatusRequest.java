/**
 * <AUTHOR>
 * @date 2024/11/25 16:55
 * @version 1.0 EditOperationRoleStatusRequest
 */
package com.fshows.gosh.web.domain.request.operation.admin;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *
 *
 * <AUTHOR>
 * @version EditOperationRoleStatusRequest.java, v 0.1 2024-11-25 16:55 tuyuwei
 */
@Data
public class EditOperationRoleStatusRequest {

    /**
     * 角色id
     */
    @NotBlank(message = "角色id")
    private String roleId;


    /**
     * 角色状态:1正常,2禁用
     */
    @NotNull(message = "角色状态")
    private Integer roleStatus;


}