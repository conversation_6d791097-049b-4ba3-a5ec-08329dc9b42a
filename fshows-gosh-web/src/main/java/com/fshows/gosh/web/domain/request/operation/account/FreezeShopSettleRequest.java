/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.web.domain.request.operation.account;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version FreezeShopSettleRequest.java, v 0.1 2025-07-1 9:55 AM json
 */
@Data
public class FreezeShopSettleRequest {

    /**
     * 商铺id
     */
    @NotBlank(message = "商铺id不能为空")
    private String shopId;

    /**
     * 操作类型:1-冻结，2-解冻
     */
    @NotNull(message = "操作类型不能为空")
    private Integer operatorType;

    /**
     * 原因
     */
    @NotBlank(message = "原因不能为空")
    private String reason;
}