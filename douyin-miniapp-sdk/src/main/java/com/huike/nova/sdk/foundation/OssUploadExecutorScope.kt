/*
 *
 *  * Hangzhou Huike Technology co.,ltd.
 *  * Copyright (C) 2013-2023 All Rights Reserved.
 *
 *
 */

package com.huike.nova.sdk.foundation

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.asCoroutineDispatcher
import java.util.concurrent.Executors

/**
 * OSS 上传异步执行器
 *
 * <AUTHOR> (<EMAIL>)
 * @version OssUploadExecutorScope.java, v1.0 02/28/2023 19:24 John Exp$
 */
internal object OssUploadExecutorScope {
    /** 协程分发器 */
    private val coroutineDispatcher by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Executors.newCachedThreadPool().asCoroutineDispatcher()
    }

    /** 协程作用域 */
    private val coroutineScope: CoroutineScope = CoroutineScope(SupervisorJob() + coroutineDispatcher)

    /**
     * 获得协程作用域
     * 重载()操作符
     */
    operator fun invoke(): CoroutineScope = coroutineScope

}