package com.huike.nova.sdk.exception

/**
 * SDK错误枚举
 *
 * <AUTHOR> (<EMAIL>)
 * @version SdkCommonErrorEnum.java, v1.0 11/20/2023 15:10 John Exp$
 */
enum class SdkCommonErrorEnum(val code: String, val message: String) {

    NLS_TOKEN_INVALID("InvalidNlsToken", "错误的NLS Token"),

    NLS_SYNTHESIS_FAILED("SynthesisFailed", "阿里云NLS合成失败"),

    DASH_SCOPE_SYNTHESIS_FAILED("SynthesisFailed", "阿里云灵积模型SambertTTS合成失败"),

    PARAM_ERROR("ParameterError", "参数错误"),

    BCE_AIGC_GENERATION_ERROR("BceAigcGenerationError", "百度AIGC错误"),

    OPENAI_SDK_IS_NOT_INITIALIZED("OpenAiSdkIsNotInitialized", "OpenAI Sdk未进行初始化"),

    OPENAI_SDK_API_KEYS_CANNOT_BE_EMPTY("OpenAiSdkApiKeysCannotBeEmpty", "OpenAI Sdk ApiKey不能为空"),

    DASH_SCOPE_GENERATION_ERROR("DashScopeGenerationError", "DashScope生成错误"),

    CHAT_GPT_GENERATION_ERROR("ChatGPTGenerationError", "ChatGPT生成错误"),

    VOLCANO_ARK_PARAMETER_ERROR("VolcanoArkParamError","火山方舟参数错误"),

    VOLCANO_ARK_SERVER_ERROR("VolcanoArkServerError","火山方舟数据错误"),

    VOLCANO_ENGINE_SERVER_ERROR("VolcEngineServerError", "火山引擎服务器返回错误"),

    NOT_INITIALIZED("Not Initialized", "未调用init()方法")
}