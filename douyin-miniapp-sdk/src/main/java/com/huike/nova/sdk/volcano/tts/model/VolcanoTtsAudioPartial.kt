package com.huike.nova.sdk.volcano.tts.model

import com.alibaba.fastjson.annotation.JSONField
import com.huike.nova.sdk.azure.tts.constants.AzureTtsConstants
import com.huike.nova.sdk.volcano.tts.constant.VolcanoTtsConstants

/**
 * 火山引擎音频配置, [文档链接](https://www.volcengine.com/docs/6561/79823)
 *
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoTtsAudioParam.java, v1.0 11/23/2023 14:20 John Exp$
 */
class VolcanoTtsAudioPartial {
    /**
     * 语音类型
     */
    @JSONField(name = "voice_type")
    lateinit var voiceType: String

    /**
     * 音频采样率
     */
    @JSONField(name = "rate")
    var rate: Int = VolcanoTtsConstants.DEFAULT_SAMPLE_RATE

    /**
     * 音频采样点位数
     */
    @JSONField(name = "bits")
    var bits: Int = VolcanoTtsConstants.DEFAULT_SAMPLE_BITS

    /**
     * 音频采样点位数
     */
    @JSONField(name = "bitrate")
    var bitrate: Int = VolcanoTtsConstants.DEFAULT_MP3_BITRATE

    /**
     * 音频编码方式
     */
    @JSONField(name = "encoding")
    var encoding: String = AzureTtsConstants.EXTENSION_MPEG3

    /**
     * opus格式时编码压缩比
     * [1, 20]，默认为 1
     */
    @JSONField(name = "compression_rate")
    var compressionRate: Int = 1

    /**
     * 语速：[0.2,3]，默认为1，通常保留一位小数即可
     */
    @JSONField(name = "speed_ratio")
    var speedRatio: Float = 1F

    /**
     * 音量：[0.1, 3]，默认为1，通常保留一位小数即可
     */
    @JSONField(name = "volume_ratio")
    var volumeRatio: Float = 1F

    /**
     * 音高: [0.1, 3]，默认为1，通常保留一位小数即可
     */
    @JSONField(name = "pitch_ratio")
    var pitchRatio: Float = 1F

    /**
     * 情感/风格 [发音人参数列表](https://www.volcengine.com/docs/6561/97465)
     */
    @JSONField(name = "emotion")
    var emotion: String? = null

    /**
     * 语言类型 [发音人参数列表](https://www.volcengine.com/docs/6561/97465)
     */
    @JSONField(name = "language")
    var language: String = VolcanoTtsConstants.CN
}