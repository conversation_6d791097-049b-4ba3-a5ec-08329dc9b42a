package com.huike.nova.sdk.payment.fubei.domain.request

import com.alibaba.fastjson.annotation.JSONField
import lombok.Builder
import lombok.Data

/**
 * 付呗微信配置
 *
 * <AUTHOR> (<EMAIL>)
 * @version FbPayWxConfig.java, v1.0 12/08/2023 18:58 John Exp$
 */
data class FbPayWxConfigRequest(
    /**
     * 付呗商户号，以服务商级接入时必传，以商户级接入时不传
     */
    @JSONField(name = "merchant_id")
    var merchantId: Int? = null,

    /**
     * 付呗系统的门店id
     */
    @JSONField(name = "store_id")
    var storeId: Int? = null,

    /**
     * 支付所使用的公众号appid， 支持使用小程序appid
     */
    @JSONField(name = "sub_appid")
    var subAppId: String? = null,

    /**
     * sub_appid类型<br></br>
     *  * 00 - 公众号（默认）
     *  * 01 - 小程序
     */
    @JSONField(name = "account_type")
    var accountType: String = "01",

    /**
     * 支付授权目录
     */
    @JSONField(name = "jsapi_path")
    var jsapiPath: String? = null
)
