package com.huike.nova.sdk.payment.fubei.domain.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 微信配置响应
 *
 * <AUTHOR> (<EMAIL>)
 * @version FbPayWxConfigResponse.java, v1.0 12/08/2023 19:01 John Exp$
 */
@Data
public class FbPayWxConfigResponse {
    /**
     * 付呗商户号
     */
    @JSONField(name = "merchant_id")
    private Integer merchantId;

    /**
     * 付呗系统的门店id
     */
    @JSONField(name = "store_id")
    private Integer storeId;

    /**
     * 支付APPID配置结果：1 成功 2 失败
     */
    @JSONField(name = "sub_appid_code")
    private String subAppIdCode;

    /**
     * 支付APPID响应描述
     */
    @JSONField(name = "sub_appid_msg")
    private String subAppIdMsg;

    /**
     * 支付授权目录配置结果：1 成功、2 失败
     */
    @JSONField(name = "jsapi_code")
    private Integer jsapiCode;

    /**
     * 支付授权目录响应描述
     */
    @JSONField(name = "jsapi_msg")
    private String jsapiMsg;
}
