package com.huike.nova.sdk.volcano.domain.muse;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR> (<EMAIL>)
 * @version ICCloudBelongEntityEntity.java, v1.0 02/29/2024 16:27 John Exp$
 */
@Data
public class MuseMediaResourceOwnerEntity {
    /**
     * 类型，取值有：
     * - PERSON：个人
     */
    @JSONField(name = "Type")
    private String type = "PERSON";

    /**
     * Id，当 Type=PERSON，取值为用户 Id（可通ListUsers接口获取）
     */
    @JSONField(name = "Id")
    private Integer id;
}
