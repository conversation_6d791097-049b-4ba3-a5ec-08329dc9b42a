package com.huike.nova.sdk.volcano.domain.muse;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 图片转视频
 *
 * <AUTHOR> (<EMAIL>)
 * @version MuseSubmitText2VideoTaskAsyncParam.java, v1.0 03/02/2024 20:46 John Exp$
 */
@Data
@Accessors(chain = true)
public class MuseSubmitText2VideoTaskAsyncParam {

    public static final String ACTION = "SubmitText2VideoTaskAsync";

    public static final String VERSION = "2022-08-01";

    /**
     * 生成视频的方式
     * 0-表示传入文章链接
     * 1-表示传入文章标题和文章内容
     * 2-表示传入word文档
     */
    @JSONField(name = "Type")
    private Integer type;

    /**
     * 当type=0时必填，表示文章链接，目前支持头条文章域名
     */
    @JSONField(name = "ArticleUrl")
    private String articleUrl;

    /**
     * 当type=1时必填，表示文章标题
     */
    @JSONField(name = "ArticleTitle")
    private String articleTitle;

    /**
     * 当type=1时必填，表示文章内容，字数≤3000
     */
    @JSONField(name = "ArticleContent")
    private String articleContent;

    /**
     * 当type=2时必填，表示word文档链接，仅支持.docx格式，大小≤15MB
     */
    @JSONField(name = "FileUrl")
    private String fileUrl;

    /**
     * 替换模板中固定文案，不替换不传，无需文案传空字符串
     */
    @JSONField(name = "FixedText")
    private String fixedText;

    /**
     * 视频时长的种类，目前有三种，0表示短（约15s），1表示中等（约30s），2表示长（约60s）。不传该字段表示全文生成。
     */
    @JSONField(name = "TargetDuration")
    private Integer targetDuration;

    /**
     * 屏幕比例的种类，
     * 0表示9:16（默认），1表示16:9，2表示1：1
     */
    @JSONField(name = "ScreenRatio")
    private Integer screenRatio;

    /**
     * 视频分辨率，0为720P，1为1080P
     */
    @JSONField(name = "Resolution")
    private Integer resolution;

    /**
     * 模板ID，不传该字段为自动匹配
     */
    @JSONField(name = "TemplateId")
    private Integer templateId;

    /**
     * 素材来源（可选择多个），0：链接/word中的素材，1：素材库中的素材，2：商用版权素材，4：无版权素材，5：免费版权素材
     */
    @JSONField(name = "MaterialSource")
    private List<Integer> materialSource;

    /**
     * 封面样式，0（默认）：非黑首帧，1：在封面模板库中随机选取
     */
    @JSONField(name = "CoverType")
    private Integer coverType;

    /**
     * 语音播报音色ID，不传该字段为无需播报
     */
    @JSONField(name = "ToneId")
    private Integer toneId;

    /**
     * 语音播报速度，100为原速度，区间50-200
     */
    @JSONField(name = "TtsSpeed")
    private Integer ttsSpeed;

    /**
     * 语音播报音量，100为原音量，区间0-200
     */
    @JSONField(name = "TtsVolume")
    private Integer ttsVolume;

    /**
     * 语音播报音调，100为原音调，区间50-200
     */
    @JSONField(name = "TtsPitch")
    private Integer ttsPitch;

    /**
     * 背景音乐类型，0：智能匹配（默认），1：手动选择，2：传入自定义Url
     */
    @JSONField(name = "BgmType")
    private Integer bgmType;

    /**
     * BgmType=0时，此值可不填
     * BgmType=1时，请传入背景音乐ID
     * BgmType=2时，请传入自定义的http链接
     */
    @JSONField(name = "BgmSource")
    private String bgmSource;

    /**
     * 背景音乐音量，默认为10（10%音量），区间0-100
     */
    @JSONField(name = "BgmVolume")
    private Integer bgmVolume;
}
