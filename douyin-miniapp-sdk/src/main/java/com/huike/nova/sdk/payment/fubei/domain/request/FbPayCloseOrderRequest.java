package com.huike.nova.sdk.payment.fubei.domain.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 付呗订单关闭
 *
 * <AUTHOR> (<EMAIL>)
 * @version FubeiPayCloseOrderRequest.java, v1.0 12/21/2023 19:37 John Exp$
 */
@Data
public class FbPayCloseOrderRequest {

    /**
     * 付呗商户号，以服务商级接入时必传，以商户级接入时不传
     */
    @JSONField(name = "merchant_id")
    private String merchantId;

    /**
     * 付呗订单号，和外部系统订单号不能同时为空（二选一），如果同时存在优先取order_sn
     */
    @JSONField(name = "order_sn")
    private String orderSn;

    /**
     * 外部系统订单号，和付呗订单号不能同时为空（二选一）
     */
    @JSONField(name = "merchant_order_sn")
    private String merchantOrderSn;

}
