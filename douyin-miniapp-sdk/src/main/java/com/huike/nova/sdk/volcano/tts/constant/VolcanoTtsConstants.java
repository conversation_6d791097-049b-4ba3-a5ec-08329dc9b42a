package com.huike.nova.sdk.volcano.tts.constant;

/**
 * 火山引擎使用的常量
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoConstant.java, v1.0 11/23/2023 14:11 John Exp$
 */
public interface VolcanoTtsConstants {
    /**
     * 默认的采样率
     */
    int DEFAULT_SAMPLE_RATE = 24_000;

    /**
     * 默认的音频采样点位数
     */
    int DEFAULT_SAMPLE_BITS = 16;

    /**
     * mp3格式时对应的码率
     */
    int DEFAULT_MP3_BITRATE = 192;

    /**
     * 默认的Token
     */
    String DEFAULT_TOKEN = "default_token";

    /**
     * 火山引擎-语音合成业务集群
     */
    String TTS_CLUSTER = "volcano_tts";

    /**
     * 中文语言
     */
    String CN = "cn";

    /**
     * 语音合成接口
     */
    String TTS_SYNTHESIS_API = "https://openspeech.bytedance.com/api/v1/tts";

    /**
     * 纯文本
     */
    String TEXT_TYPE_PLAIN = "plain";

    /**
     * SSML
     */
    String TEXT_TYPE_SSML = "ssml";
    
    /**
     * query（非流式，http只能query） / submit（流式）
     */
    String OPERATION_QUERY = "query";

    /**
     * SSML Speak TAG
     */
    String SSML_SPEAK_NODE_TAG = "<speak>";

    /**
     * 请求参数
     */
    String HEADER_AUTHORIZATION = "Authorization";

    /**
     * 请求Bearer TOKEN
     */
    String BEARER = "Bearer;{}";

    /**
     * 成功代码
     */
    int RESULT_CODE_OK = 3000;
}
