package com.huike.nova.sdk.volcano.domain.muse;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * TTS播报
 *
 * <AUTHOR> (<EMAIL>)
 * @version ICCloudTtsInfoEntity.java, v1.0 02/29/2024 16:15 John Exp$
 */
@Data
public class MuseTtsInfoEntity {

    /**
     * 音色Id，见【物料】部分
     */
    @JSONField(name = "ToneId")
    private String toneId;


    /**
     * 全局播报文本，不超过200字
     */
    @JSONField(name = "Text")
    private String text;

    /**
     * 槽位播报文本列表，长度等于槽位个数
     */
    @JSONField(name = "TextList")
    private List<String> textList;

    /**
     * TTS播报速度；50~200
     */
    @JSONField(name = "Speed")
    private Integer speed = 100;

    /**
     * 播报音调;50~200
     */
    @JSONField(name = "Pitch")
    private Integer pitch;

    /**
     * 播报音量;0~200
     */
    @JSONField(name = "Volume")
    private Integer volume;

    /**
     * 背景音乐音量；0~200
     */
    @JSONField(name = "BgmVolume")
    private Integer bgmVolume;

    /**
     * 字幕字体Id
     */
    @JSONField(name = "FontType")
    private Integer fontType;

    /**
     * 字母字号，默认10
     */
    @JSONField(name = "FontSize")
    private Integer fontSize;

    /**
     * 字幕颜色信息，长度=8，传16进制字符串。例如："FFFFFFFF"，前两位是透明度，FF为不透明，00为全透明，后六位为RGB颜色。默认不透明白色字幕，即"FFFFFFFF"。
     */
    @JSONField(name = "FontColor")
    private String fontColor;

    /**
     * 字幕距离屏幕下方的距离，单位：像素。
     */
    @JSONField(name = "MarginV")
    private Integer marginV;
}
