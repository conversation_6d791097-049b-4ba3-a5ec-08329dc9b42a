package com.huike.nova.sdk.volcano.tts.model

import com.alibaba.fastjson.annotation.JSONField
import com.huike.nova.sdk.volcano.tts.constant.VolcanoTtsConstants

/**
 * 火山请求的应用信息
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoAppInfoModel.java, v1.0 11/23/2023 14:14 John Exp$
 */
data class VolcanoAppInfoPartial (
    /**
     * appId 应用Id
     */
    @JSONField(name = "appid")
    val appId: String,

    /**
     * cluster 业务集群
     */
    @JSONField(name = "cluster")
    val cluster: String = VolcanoTtsConstants.TTS_CLUSTER,

    /**
     * 应用令牌: 目前未生效，填写默认值：default_token
     */
    @JSONField(name = "token")
    val token: String = VolcanoTtsConstants.DEFAULT_TOKEN
)


/**
 * 火山的用户配置，固定即可
 *
 * @constructor Create empty Volcano user param
 */
data class VolcanoUserPartial(
    /**
     * 用户Id:建议填写真实的uid，非必须，可以填写一个默认值
     */
    @JSONField(name = "uid")
    val userId: String = "LTB-NOVA-USER"
)