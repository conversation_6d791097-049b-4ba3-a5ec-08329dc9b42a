/*
 * Hangzhou Fshows Technology co.,ltd.
 * Copyright (C) 2013-2022 All Rights Reserved.
 */

package com.huike.nova.sdk.foundation.ext

import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alibaba.fastjson.TypeReference
import com.alibaba.fastjson.serializer.SerializerFeature
import com.google.common.collect.Maps
import com.huike.nova.common.constant.StringPool
import jodd.util.StringUtil
import org.springframework.util.StringUtils

/**
 * 序列扩展函数
 *
 * <AUTHOR> (<EMAIL>)
 * @version ObjectExt.java, v1.0 2022/10/14 16:38 John Exp$
 */
fun Any?.toJsonString(): String {
    return JSON.toJSONString(this ?: Any(), SerializerFeature.WriteBigDecimalAsPlain)
}

fun Any?.toJson(): String {
    return JSON.toJSONString(this?: Any(), SerializerFeature.WriteBigDecimalAsPlain)
}

inline fun<reified T> String?.toObject(): T? {
    return if (this == null) {
        null
    } else {
        if (T::class.java.typeParameters.isEmpty()) {
            JSON.parseObject(this, T::class.java)
        } else {
            JSON.parseObject(this, object : TypeReference<T>() {}.type)
        }
    }
}

fun Any?.convertToMap(): Map<String, Any> {
    if (this == null) {
        return mapOf()
    }
    return JSONObject.parseObject(this.toJsonString())
}

/**
 * 分割为Map
 *
 * @receiver 字符串
 * @param delimiter 分隔符
 * @return Map对象
 */
fun String?.splitToMap(delimiter: String = StringPool.PIPE): Map<String, String> {
    if (this.isNullOrBlank()) {
        return mapOf()
    } else {
        val splitStringList = StrUtil.split(this, delimiter)
        val result = Maps.newHashMap<String, String>()
        splitStringList.forEachIndexed { index, s ->
            // 如果是第一个元素，并且不包含等号
            if (index == 0 && !StrUtil.contains(s, StringPool.EQUALS_CHAR)) {
                result["name"] = s
            } else {
                val arr = StrUtil.split(s, StringPool.EQUALS_CHAR)
                if (arr.size >= 2) {
                    result[arr[0]] = arr[1]
                }
            }
        }
        return result
    }
}

/**
 * 将QueryString转为Map
 *
 * @return KV MAP对象
 */
fun String?.queryStringToMap(): Map<String, String> {
    return if (this.isNullOrBlank()) {
        mapOf()
    } else {
        val result = hashMapOf<String, String>()
        val splitStringList = StrUtil.split(this.trimStart(*StringPool.QUESTION_MARK.toCharArray()), StringPool.AMPERSAND)
        // 对字符串进行二次分割
        splitStringList.forEach {
            // 根据空格拆分
            val kvList = it.split(StringPool.EQUALS_CHAR)
            if (kvList.isEmpty()) {
                return@forEach
            }
            result[kvList[0].urlDecode()] = if (kvList.size > 1) {
                kvList[1].urlDecode()
            } else {
                StringPool.EMPTY
            }
        }
        result
    }
}

/**
 * 将Http查询字符串转为实体类
 *
 * @param T
 * @return 实体类
 */
inline fun<reified T> String?.queryStringToEntity(): T {
    return JSONObject.parseObject(this.toJsonString(), T::class.java)
}