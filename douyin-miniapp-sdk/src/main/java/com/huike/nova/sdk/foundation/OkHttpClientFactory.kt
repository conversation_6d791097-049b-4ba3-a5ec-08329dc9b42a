package com.huike.nova.sdk.foundation

import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.util.concurrent.TimeUnit

/**
 * OKHttp连接工厂
 *
 * <AUTHOR> (<EMAIL>)
 * @version OkHttpClientFactory.java, v1.0 11/29/2023 14:26 John Exp$
 */
/**
 * 是否启用调试日志
 */
@JvmField
@Volatile
var enableGptDebugLog: Boolean = false

/**
 * gpt使用的Http客户端
 */
val gptHttpClient: OkHttpClient by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
    val logLevel = if (enableGptDebugLog) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE
    OkHttpClient.Builder()
        .addInterceptor(HttpLoggingInterceptor().setLevel(logLevel))
        // 连接时间: 30秒
        .connectTimeout(30, TimeUnit.SECONDS)
        // 发送请求时间: 60秒
        .writeTimeout(60, TimeUnit.SECONDS)
        // 请求等待时间: 60秒
        .readTimeout(60, TimeUnit.SECONDS)
        .build()
}