package com.huike.nova.sdk.volcano.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR> (<EMAIL>)
 * @version VolcEngineMetaData.java, v1.0 02/28/2024 15:49 John Exp$
 */
@Data
public class VolcEngineMetaData {
    /**
     * 接口名称
     */
    @JSONField(name = "Action")
    private String action;

    /**
     * 区域
     */
    @JSONField(name = "Region")
    private String region;

    /**
     * 请求Id
     */
    @JSONField(name = "RequestId")
    private String requestId;

    /**
     * 服务名
     */
    @JSONField(name = "Service")
    private String service;

    /**
     * 接口版本号
     */
    @JSONField(name = "Version")
    private String version;

    @JSONField(name = "Code")
    private String code;

    @JSONField(name = "Domain")
    private String domain;

    @JSONField(name = "Message")
    private String message;
}
