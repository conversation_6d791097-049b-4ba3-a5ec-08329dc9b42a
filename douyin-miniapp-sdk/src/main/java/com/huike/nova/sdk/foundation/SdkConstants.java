package com.huike.nova.sdk.foundation;

import okhttp3.MediaType;

/**
 * SDK常用的常量
 *
 * <AUTHOR> (<EMAIL>)
 * @version SdkConstants.java, v1.0 11/29/2023 18:35 John Exp$
 */
public class SdkConstants {
    /**
     * MIME类型: JSON
     */
    public static final String APPLICATION_JSON = "application/json";

    /**
     * 大模型使用
     */
    public static final String PROTOBUF_CONTENT_TYPE = "application/x-protobuf";

    /**
     * 表单类型
     */
    public static final String X_WWW_FORM_URLENCODED_CONTENT_TYPE = "application/x-www-form-urlencoded";

    /**
     * MIME类型: JSON
     */
    public static final MediaType MEDIA_TYPE_APPLICATION_JSON = MediaType.parse(APPLICATION_JSON);

    /**
     * HTTPs头
     */
    public static final String HTTPS = "https://";

    /**
     * HTTP头
     */
    @SuppressWarnings("HttpUrlsUsage")
    public static final String HTTP = "http://";

    /**
     * GET
     */
    public static final String GET = "GET";

    /**
     * POST
     */
    public static final String POST = "POST";

    /**
     * 空对象
     */
    public static final Object EMPTY_OBJ = new Object();

    /**
     * 阿里云域名
     */
    public static final String FMT_ALIYUN_URL = "https://{}.{}.aliyuncs.com";
}
