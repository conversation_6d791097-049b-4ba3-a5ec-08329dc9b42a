package com.huike.nova.sdk.volcano;

import java.time.format.DateTimeFormatter;

/**
 * 火山引擎常量
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcEngineConstants.java, v1.0 02/19/2024 14:47 John Exp$
 */
public class VolcengineConstants {

    public static class Api {
        public static final String MAAS_CHAT_API = "https://maas-api.ml-platform-cn-beijing.volces.com/api/v1/chat";

        public static final String ICP_API = "https://icp.volcengineapi.com";
    }

    public static class Region {
        public static final String CN_BEIJING = "cn-beijing";

        public static final String CN_SHANGHAI = "cn-shanghai";

        public static final String CN_HANGZHOU = "cn-hangzhou";

        public static final String CN_NORTH_1 = "cn-north-1";
    }
    
    public static class Service {
        /**
         * 火山方舟
         */
        public static final String ML_MAAS = "ml_maas";

        /**
         * 智能创作服务
         */
        public static final String IC_CLOUD_MUSE = "iccloud_muse";
    }

    /**
     * 加签哈希算法
     */
    public static final String HMAC_SHA256 = "HMAC-SHA256";

    /**
     * 请求
     */
    public static final String REQUEST = "request";
    /**
     * 时间格式
     */
    public static final DateTimeFormatter GMT_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'");

    /**
     * 签名头
     */
    public static final String SIGN_HEADERS = "content-type;host;x-content-sha256;x-date";

    /**
     * 认证域
     */
    public static final String FMT_CREDENTIAL_SCOPE = "{}/{}/{}/request";

    /**
     * 签名字符串
     */
    public static final String FMT_SIGN_STRING = "HMAC-SHA256\n{}\n{}\n{}";

    /**
     * 返回结果：成功
     */
    public static final Integer RESULT_CODE_SUCCESS = 0;

    public static final String R_16_TO_9 = "16:9";

    public static final String R_9_TO_16 = "9:16";
}
