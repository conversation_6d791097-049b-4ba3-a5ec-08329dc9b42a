package com.huike.nova.sdk.volcano.gpt;

import com.huike.nova.sdk.volcano.gpt.model.VolcanoArkModelRequest;
import okhttp3.sse.EventSourceListener;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

/**
 * 火山方舟
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoArkGptClient.java, v1.0 02/20/2024 09:24 John Exp$
 */
public interface VolcanoArkGptClient {
    /**
     * 进行对话补齐
     *
     * @param model 模型名称
     * @param content 对话内容
     * @param request 请求参数
     * @return 请求返回
     */
    String chatCompletionStream(String model, String content, @Nonnull VolcanoArkModelRequest request);


    /**
     * 单行文本生成 (SSE方式)
     *
     * @param model 模型名称
     * @param content 对话内容
     * @param request 请求参数
     * @param listener 事件监听
     */
    void textGenerationSse(@Nonnull String model, @Nonnull String content, @Nullable VolcanoArkModelRequest request, @Nonnull EventSourceListener listener);
}
