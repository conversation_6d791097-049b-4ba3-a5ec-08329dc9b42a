package com.huike.nova.sdk.volcano.tts.model

import com.alibaba.fastjson.annotation.JSONField
import com.huike.nova.sdk.volcano.tts.constant.VolcanoTtsConstants

/**
 * 火山引擎TTS接口返回
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoTtsResponse.java, v1.0 11/23/2023 15:22 John Exp$
 */
data class VolcanoTtsResponse(
    /**
     * 请求Id
     */
    @JSONField(name = "reqid")
    val requestId: String,

    /**
     * 错误码
     */
    @JSONField(name = "code")
    val code: Int,

    /**
     * 操作
     */
    @JSONField(name = "operation")
    val operation: String,

    /**
     * 错误信息
     */
    @JSONField(name = "message")
    val message: String,

    /**
     * 音频段序号
     * 负数表示合成完毕
     */
    @JSONField(name = "sequence")
    val sequence: Int,

    /**
     * 返回的音频数据，base64 编码
     */
    @JSONField(name = "data")
    val data: String? = null,

    /**
     * 额外信息
     */
    @JSONField(name = "addition")
    val addition: VolcanoTtsAdditionalPartial? = null
) {
    /**
     * 是否请求成功
     *
     * @return true-错误码3000 false-错误
     */
    fun isSuccess(): Boolean = (this.code == VolcanoTtsConstants.RESULT_CODE_OK)
}
