package com.huike.nova.sdk.exception;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * SDK异常
 *
 * <AUTHOR> (<EMAIL>)
 * @version SdkException.java, v1.0 11/19/2023 17:13 John Exp$
 */
@Getter
public class SdkHttpException extends Exception {

    /**
     * Http状态码
     */
    private final Integer statusCode;

    /**
     * 从SDK返回的错误码
     */
    private final String errorCode;

    /**
     * 错误码
     */
    private final String errorMsg;

    /**
     * 设置额外数据
     */
    @Getter
    private String extra;

    public SdkHttpException(Integer statusCode, String errorCode, String errMsg) {
        super(String.format("http status code:%s, errorCode:%s, errMsg:%s", statusCode, errorCode, errMsg));
        this.statusCode = statusCode;
        this.errorCode = errorCode;
        this.errorMsg = errMsg;
    }

    public SdkHttpException(String errCode, String errMsg) {
        super(String.format("errorCode:%s, errMsg:%s", errCode, errMsg));
        this.statusCode = 200;
        this.errorCode = errCode;
        this.errorMsg = errMsg;
    }

    public SdkHttpException setExtra(String extra) {
        this.extra = StringUtils.defaultString(extra);
        return this;
    }

}
