package com.huike.nova.sdk.foundation.ext

import cn.hutool.core.lang.UUID
import com.alibaba.fastjson.JSONObject
import com.huike.nova.common.constant.CommonConstant
import com.huike.nova.common.constant.StringPool
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.apache.commons.lang3.RandomStringUtils
import org.slf4j.MDC
import java.io.File

/**
 * 工具类
 *
 * <AUTHOR> (<EMAIL>)
 * @version UtilsExt.java, v1.0 11/18/2023 15:41 John Exp$
 */

/**
 * 获得Uuid
 *
 * @return uuid
 */
fun uuid(isSimple: Boolean = false): String {
    val uuid = UUID.randomUUID().toString()
    return if (isSimple) {
        uuid.replace(StringPool.DASH, StringPool.EMPTY)
    } else {
        uuid
    }
}

/**
 * 获得临时文件名, 如果临时目录不存在则先创建临时目录
 *
 * @param prefix 临时文件的前缀
 * @param suffix 临时文件的后缀，如果需要指定扩展名则增加"."
 * @param subFolder 临时文件夹中存放的子目录
 * @return 创建目录,返回临时文件名
 */
fun getTemporaryFile(prefix: String, suffix: String, subFolder: String? = null): File {
    return if (subFolder.isNullOrEmpty()) {
        File.createTempFile(prefix, suffix)
    } else {
        val directory = File(System.getProperty("java.io.tmpdir"), subFolder)
        // 未创建目录，需要先创建目录才可以进行文件操作
        if (!directory.exists()) {
            directory.mkdirs()
        }
        File.createTempFile(prefix, suffix, directory)
    }
}

/**
 * 获得随机数Int
 *
 * @return 随机数
 */
fun randomIntSeed(): Int {
    return RandomStringUtils.randomNumeric(10).toLong().coerceAtMost(Int.MAX_VALUE.toLong()).toInt()
}

/**
 * 获得TraceId
 *
 * @return 获得TraceId
 */
fun currentTraceId(): String {
    return runCatching {
        MDC.get(CommonConstant.TRACE_ID)
    }.getOrNull().nullToEmpty()
}

/**
 * 设置
 *
 * @param traceId 日期Id
 */
fun setTraceId(traceId: String?) {
    if (traceId.isNullOrBlank()) {
        return
    }
    runCatching {
        MDC.put(CommonConstant.TRACE_ID, traceId)
    }
}

/**
 * 对象转为Map
 *
 * @return Map对象
 */
fun Any?.objectToMap(): Map<String, Any> {
    if (this == null) {
        return mapOf()
    }
    return JSONObject.parseObject(JSONObject.toJSONString(this))
}

fun sleep(millis: Long) {
    runBlocking {
        delay(millis)
    }
}