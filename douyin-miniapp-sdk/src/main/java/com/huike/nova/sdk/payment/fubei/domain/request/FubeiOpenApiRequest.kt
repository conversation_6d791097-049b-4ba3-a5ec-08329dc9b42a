package com.huike.nova.sdk.payment.fubei.domain.request

import com.alibaba.fastjson.annotation.JSONField

/**
 * 付呗OpenApi请求公共参数
 *
 * <AUTHOR> (<EMAIL>)
 * @version PublicParameter.java, v1.0 12/08/2023 09:52 John Exp$
 */
internal class FubeiOpenApiRequest {

    /**
     * 服务商开放平台id
     */
    @JSONField(name = "vendor_sn")
    var vendorSn: String? = null

    /**
     * 商户开放平台id
     */
    @JSONField(name = "app_id")
    var appId: String? = null

    /**
     * 接口名称
     */
    @JSONField(name = "method")
    var method: String? = ""

    /**
     * 接口格式: 固定json
     */
    @JSONField(name = "format")
    val format: String = "json"

    /**
     * 签名方式，固定md5
     */
    @JSONField(name = "sign_method")
    val signMethod: String = "md5"

    /**
     * 签名
     */
    @JSONField(name = "sign")
    var sign: String = ""

    /**
     * 请求端随机生成数
     */
    @JSONField(name = "nonce")
    var nonce: String = ""

    /**
     * 版本，默认1.0
     */
    @JSONField(name = "version")
    val version: String = "1.0"

    /**
     * 付呗业务参数
     */
    @JSONField(name = "biz_content")
    var bizContent: Any? = null
}