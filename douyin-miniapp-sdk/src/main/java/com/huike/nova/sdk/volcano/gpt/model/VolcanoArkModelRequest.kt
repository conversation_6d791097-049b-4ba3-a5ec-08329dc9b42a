package com.huike.nova.sdk.volcano.gpt.model

import com.huike.nova.sdk.exception.SdkCommonErrorEnum
import com.huike.nova.sdk.exception.SdkException
import com.huike.nova.sdk.openai.OpenAiConstants

/**
 * 火山方舟GPT请求
 *
 * <AUTHOR> (<EMAIL>)
 * @version ArkModelRequest.java, v1.0 02/20/2024 09:27 John Exp$
 */
class VolcanoArkModelRequest(
    /**
     * 访问Key
     */
    var accessKey: String = "",

    /**
     * 密钥Key
     */
    var secretKey: String = "",

    /**
     * 最多生成Token数
     */
    var maxTokens: Int? = null,

    /**
     * 最多生成 token 数（包含 prompt 的 token 数目）
     */
    var maxNewTokens: Int? = 1000,

    /**
     * 最少新生成 tokens 数
     */
    var minNewTokens: Int? = 1,

    /**
     * 最大输入模型的 token 数，如果输入文本的 token 数超过该长度，将取最后 `max_prompt_tokens` 个 token 输入模型
     */
    var maxPromptTokens: Int? = null,

    /**
     * 采样温度，取值范围为(0, 2]
     */
    var temperature: Float? = 0.7F,

    /**
     * 核采样，取值范围为[0, 1.0]
     */
    var topP: Float? = 0.9F,

    /**
     * top-k-filtering 算法保留多少个 最高概率的词 作为候选，正整数
     */
    var topK: Int? = null,

    /**
     * 是否采样
     */
    var doSample: Int? = null,

    /**
     * 存在惩罚，如果为正，值越大，模型谈论到新话题的概率越大，取值范围为 [-2.0, 2.0]
     */
    var presencePenalty: Float? = null,

    /**
     * 频率惩罚，如果为正，值越大，模型逐字重复同一行的概率越小，取值范围为 [-2.0, 2.0]
     */
    var frequencyPenalty: Float? = null,

    /**
     * 重复惩罚，取值范围为 [1.0, 2.0]
     */
    var repetitionPenalty: Float? = null,

    /**
     * 返回概率最高的候选集，取值范围[0, 10]
     */
    var logProbs: Int? = null
) {
    companion object {
        @JvmStatic
        fun create(accessKey: String, secretKey: String): VolcanoArkModelRequest = VolcanoArkModelRequest(accessKey, secretKey)
    }

    fun checkValid() {
        if (secretKey.isBlank() || accessKey.isBlank()) {
            throw SdkException(SdkCommonErrorEnum.VOLCANO_ARK_PARAMETER_ERROR).setDetailMessage("AccessKey或SecretKey不能为空")
        }
    }
}

/**
 * Volcano ark model message
 *
 * @property role
 * @property content
 * @constructor Create empty Volcano ark model message
 */
class VolcanoArkModelMessage(
    /**
     * 角色
     */
    val role: String,
    
    /**
     * 文本内容
     */
    val content: String
) {
    companion object {
        fun system(content: String): VolcanoArkModelMessage {
            return VolcanoArkModelMessage(OpenAiConstants.ROLE_SYSTEM, content)
        }

        fun user(content: String): VolcanoArkModelMessage {
            return VolcanoArkModelMessage(OpenAiConstants.ROLE_USER, content)
        }

        fun assistant(content: String): VolcanoArkModelMessage {
            return VolcanoArkModelMessage(OpenAiConstants.ROLE_ASSISTANT, content)
        }
    }
}