package com.huike.nova.sdk.volcano;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.util.UrlEncodeUtil;
import com.huike.nova.sdk.exception.SdkCommonErrorEnum;
import com.huike.nova.sdk.exception.SdkException;
import com.huike.nova.sdk.foundation.SdkConstants;
import com.huike.nova.sdk.net.SdkHttpClient;
import com.huike.nova.sdk.volcano.domain.VolcEngineApiResponse;
import com.huike.nova.sdk.volcano.sign.VolcEngineAuthSignModel;
import com.huike.nova.sdk.volcano.sign.VolcEngineAuthSignUtil;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Map;

/**
 * 火山引擎Http工具
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcengineHttpUtil.java, v1.0 02/28/2024 19:57 John Exp$
 */
public class VolcengineHttpUtil {

    /**
     * 发送火山引擎POST请求
     *
     * @param params 参数
     * @param authSignModel 签名对象
     * @return 对象
     * @param <T> Resule.Data对象类型
     * @throws SdkException SDK异常
     */
    public static <T> T postJson(@Nullable Object params, @Nonnull VolcEngineAuthSignModel authSignModel) throws SdkException {
        // 将请求参数转为字符串
        String requestParamsStr = JSONObject.toJSONString(ObjectUtil.defaultIfNull(params, SdkConstants.EMPTY_OBJ));
        // 生成签名头
        Map<String, String> reqHeaders = VolcEngineAuthSignUtil.getSignedRequestHeaders(SdkConstants.POST, requestParamsStr, authSignModel);
        // 根据Service名称生成客户端对象
        SdkHttpClient client = new SdkHttpClient(authSignModel.getService());
        String responseBody = client.postJson(authSignModel.getUrl(), params, reqHeaders);
        return getResponse(responseBody);
    }

    public static <T> T postJson(@Nonnull VolcEngineAuthSignModel authSignModel) throws SdkException {
        return postJson(null, authSignModel);
    }

    /**
     * 发送火山引擎GET请求
     *
     * @param queryParams query参数
     * @param authSignModel 签名对象
     * @return 对象
     * @param <T> 对象
     * @throws SdkException SDK异常
     */
    public static <T> T get(@Nullable Map<String, Object> queryParams, @Nonnull VolcEngineAuthSignModel authSignModel) throws SdkException {
        if (CollectionUtil.isNotEmpty(queryParams)) {
            String querySegments = UrlEncodeUtil.toUrlQueryStringParam(queryParams);
            if (StringUtils.isNotBlank(authSignModel.getQuery())) {
                authSignModel.setQuery(authSignModel.getQuery() + StringPool.AMPERSAND + querySegments);
            } else {
                authSignModel.setQuery(querySegments);
            }
        }
        // 生成签名头
        Map<String, String> reqHeaders = VolcEngineAuthSignUtil.getSignedRequestHeaders(SdkConstants.GET, StringPool.EMPTY, authSignModel);
        // 根据Service名称生成客户端对象
        SdkHttpClient client = new SdkHttpClient(authSignModel.getService());
        String responseBody = client.getAsString(authSignModel.getUrl(), null, reqHeaders);
        return getResponse(responseBody);
    }

    private static <T> T getResponse(String responseBody) throws SdkException {
        VolcEngineApiResponse<T> response = JSONObject.parseObject(responseBody, new TypeReference<VolcEngineApiResponse<T>>() {});
        if (ObjectUtil.notEqual(response.getCode(), VolcengineConstants.RESULT_CODE_SUCCESS)) {
            throw new SdkException(SdkCommonErrorEnum.VOLCANO_ENGINE_SERVER_ERROR).setDetailMessage(response.getMessage());
        }
        return response.getResult();
    }
}
