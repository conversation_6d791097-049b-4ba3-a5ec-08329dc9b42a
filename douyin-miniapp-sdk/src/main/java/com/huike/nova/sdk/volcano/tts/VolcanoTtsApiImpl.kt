package com.huike.nova.sdk.volcano.tts

import cn.hutool.core.util.StrUtil
import com.huike.nova.common.util.LogUtil
import com.huike.nova.sdk.douyin.ext.nullToEmpty
import com.huike.nova.sdk.exception.SdkHttpException
import com.huike.nova.sdk.foundation.annotation.Slf4jKt.Companion.log
import com.huike.nova.sdk.foundation.ext.toObject
import com.huike.nova.sdk.model.SdkSpeechSynthesisResult
import com.huike.nova.sdk.net.SdkHttpClient
import com.huike.nova.sdk.tts.internal.TextToSpeechOssClient
import com.huike.nova.sdk.volcano.tts.constant.VolcanoTtsConstants
import com.huike.nova.sdk.volcano.tts.model.VolcanoTtsRequest
import com.huike.nova.sdk.volcano.tts.model.VolcanoTtsResponse
import io.ktor.util.*
import org.apache.commons.lang3.StringUtils

/**
 * 火山引擎
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoTts.java, v1.0 11/23/2023 16:23 John Exp$
 */
class VolcanoTtsApiImpl : VolcanoTtsApi {
    companion object {
        private const val CLIENT_IDENTIFIER = "VolcanoTts.Client"
    }

    private val httpClient: SdkHttpClient by lazy {
        SdkHttpClient(CLIENT_IDENTIFIER)
    }

    override fun speechSynthesis(text: String, voice: String, sampleRate: Int, volumeRatio: Float, emotion: String?): SdkSpeechSynthesisResult {
        if (text.isBlank()) {
            LogUtil.warn(log, "VolcanoTts.synthesisSpeech >> TTS >> TTS 生成内容为空..")
            return SdkSpeechSynthesisResult(nid = voice)
        }
        val request = VolcanoTtsRequest()
        // 判断文字是否是ssml，如果是以Speak开头的内容会自动以SSML方式请求
        request.request.textType = if (StringUtils.startsWithIgnoreCase(text, VolcanoTtsConstants.SSML_SPEAK_NODE_TAG)) {
            VolcanoTtsConstants.TEXT_TYPE_SSML
        } else {
            VolcanoTtsConstants.TEXT_TYPE_PLAIN
        }
        request.request.text = text
        // 音色设置
        request.audio.also {
            it.volumeRatio = volumeRatio
            it.emotion = emotion
            it.voiceType = voice
        }
        val response = ttsSynthesis(request)
        val buffer = response.data.nullToEmpty().decodeBase64Bytes()
        val client = TextToSpeechOssClient(VolcanoTtsConfig.ossStorageConfig)
        val ossUrl = client.upload(buffer)
        return SdkSpeechSynthesisResult(ossUrl, response.addition?.duration?.toLongOrNull() ?: 0L, voice)
    }

    /**
     * 语音合成并且保存文件至本地
     *
     * @param request 请求
     * @return 语音合成响应
     */
    override fun ttsSynthesis(request: VolcanoTtsRequest): VolcanoTtsResponse {
        val responseBody = httpClient.postJson(
            VolcanoTtsConstants.TTS_SYNTHESIS_API, request, mapOf(
            VolcanoTtsConstants.HEADER_AUTHORIZATION to StrUtil.format(
                VolcanoTtsConstants.BEARER, VolcanoTtsConfig.accessToken)
        ))
        val response = responseBody.toObject<VolcanoTtsResponse>()
        // 检查是否错误，抛出异常
        if (response?.isSuccess() != true) {
            throw if (response == null) {
                SdkHttpException("-1", "返回数据错误")
            } else {
                SdkHttpException(response.code.toString(), response.message)
            }
        }
        if (response.data.isNullOrBlank()) {
            throw SdkHttpException("-1", "返回的音频数据不能为空")
        }
        // 获得音频数据的二进制
        return response
    }
}