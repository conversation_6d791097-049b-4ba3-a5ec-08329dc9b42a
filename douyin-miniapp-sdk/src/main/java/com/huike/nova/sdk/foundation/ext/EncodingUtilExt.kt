package com.huike.nova.sdk.foundation.ext

import cn.hutool.core.codec.Base64
import com.huike.nova.common.constant.StringPool
import org.apache.commons.codec.binary.Hex
import org.springframework.web.util.UriUtils
import java.nio.charset.StandardCharsets

/**
 * Encoding工具类
 *
 * <AUTHOR> (<EMAIL>)
 * @version EncodingUtilExt.java, v1.0 11/18/2023 16:57 John Exp$
 */

/**
 * 执行Url encode操作
 *
 * @receiver 字符串
 * @return UrlEncode过的字符串
 */
fun String?.urlEncode(): String {
    return UriUtils.encode(this ?: StringPool.EMPTY, StandardCharsets.UTF_8)
}

/**
 * 执行Url decode操作
 *
 * @receiver 字符串
 * @return UrlDecode过的字符串
 */
fun String?.urlDecode(): String {
    return UriUtils.decode(this ?: StringPool.EMPTY, StandardCharsets.UTF_8)
}

/**
 * Base64encode
 *
 * @return 将ByteArray进行Base64Encoding
 */
fun ByteArray.base64Encode(): String {
    return Base64.encode(this)
}

/**
 * Hex 转为16进制
 *
 * @return 转为16进制字符串
 */
fun ByteArray.hexString(): String {
    return Hex.encodeHexString(this)
}