package com.huike.nova.sdk.volcano.gpt.model

import com.alibaba.fastjson.annotation.JSONField

/**
 * 火山引擎返回
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoArkResponse.java, v1.0 02/20/2024 13:31 John Exp$
 */
class VolcanoArkResponse {
    /**
     * 请求Id
     */
    @JSONField(name = "req_id")
    var reqId: String?  = null

    /**
     * 回答内容
     */
    @JSONField(name = "choice")
    var choice: VolcanoChoicePartial? = null

    /**
     * Tokens用量统计
     */
    @JSONField(name = "usage")
    var usage: VolcanoArkUsagePartial? = null
}

class VolcanoChoicePartial {
    /**
     * 消息部分
     */
    @JSONField(name = "message")
    var message: VolcanoArkMessagePartial? = null

    /**
     * 是否终止: 终止STOP
     */

    @JSONField(name = "finish_reason")
    var finishReason: String? = null
}

/**
 * Tokens用量
 */
class VolcanoArkUsagePartial {
    /**
     * 提示Token
     */
    @JSONField(name = "prompt_tokens")
    var promptTokens: Int? = null

    /**
     * 返回token
     */
    @JSONField(name = "completion_tokens")
    var completionTokens: Int? = null

    /**
     * 总计Token
     */
    @JSONField(name = "total_tokens")
    var totalTokens: Int? = null
}

/**
 * 消息部分
 */
class VolcanoArkMessagePartial {
    /**
     * 角色: assistant/user/system
     */
    @JSONField(name = "role")
    var role: String? = null

    /**
     * 内容
     */
    @JSONField(name = "content")
    var content: String? = null
}