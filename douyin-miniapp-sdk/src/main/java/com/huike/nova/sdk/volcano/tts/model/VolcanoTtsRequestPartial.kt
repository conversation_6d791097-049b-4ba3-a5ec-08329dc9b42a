package com.huike.nova.sdk.volcano.tts.model

import com.alibaba.fastjson.annotation.JSONField
import com.huike.nova.sdk.foundation.ext.uuid
import com.huike.nova.sdk.volcano.tts.constant.VolcanoTtsConstants

/**
 * TTS请求相关配置
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoTtsRequestPartial.java, v1.0 11/23/2023 14:53 John Exp$
 */
class VolcanoTtsRequestPartial {

    /**
     * 请求标识: 建议使用 UUID，每次合成时需要重新设置
     */
    @JSONField(name = "reqid")
    var requestId: String = uuid(true)
    
    /**
     * 合成语音的文本，长度限制 1024 字节
     */
    @JSONField(name = "text")
    lateinit var text: String

    /**
     * 文本类型
     */
    @JSONField(name = "text_type")
    var textType: String = VolcanoTtsConstants.TEXT_TYPE_PLAIN

    /**
     * 句尾静音时长；单位为ms，不设置为125ms
     */
    @JSONField(name = "silence_duration")
    var silenceDuration: Int? = null

    /**
     * 操作: query（非流式，http只能query） / submit（流式）
     */
    @JSONField(name = "operation")
    var operation: String = VolcanoTtsConstants.OPERATION_QUERY

    /**
     * 时间戳相关
     * 当with_frontend为1且frontend_type为unitTson的时候，返回音素级时间戳
     */
    @JSONField(name = "with_frontend")
    var withFrontend: Int? = null

    /**
     * 时间戳相关
     * 当with_frontend为1且frontend_type为unitTson的时候，返回音素级时间戳
     */
    @JSONField(name = "frontend_type")
    var frontendType: String? = null

    /**
     * 英文前端优化
     * 当pure_english_opt为1的时候，对英文的音素识别优化
     */
    @JSONField(name = "pure_english_opt")
    var pureEnglishOpt: Int? = null
}