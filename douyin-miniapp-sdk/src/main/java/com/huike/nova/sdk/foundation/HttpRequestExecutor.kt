/*
 *
 *  * Hangzhou Huike Technology co.,ltd.
 *  * Copyright (C) 2013-2023 All Rights Reserved.
 *
 *
 */

package com.huike.nova.sdk.foundation

import com.huike.nova.sdk.douyin.DouyinOpenApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.asCoroutineDispatcher
import java.util.concurrent.Executors

/**
 * Http请求
 *
 * <AUTHOR> (<EMAIL>)
 * @version HttpRequestExecutor.java, v1.0 2022/10/13 15:41 John Exp$
 */
internal object HttpRequestExecutor {

    /** 协程分发器 */
    private val coroutineDispatcher by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Executors.newFixedThreadPool(DouyinOpenApi.executorThreadCount).asCoroutineDispatcher()
    }

    /** 协程作用域 */
    val coroutineScope: CoroutineScope = CoroutineScope(SupervisorJob() + coroutineDispatcher)


    /**
     * 异步单线程任务
     */
    private val singleWorkerCoroutineDispatcher by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Executors.newSingleThreadExecutor().asCoroutineDispatcher()
    }

    /**
     * 异步单线程协程作用域
     */
    val singleWorkerCoroutineScope =  CoroutineScope(SupervisorJob() + singleWorkerCoroutineDispatcher)
}