package com.huike.nova.sdk.payment.fubei.domain.request

import com.alibaba.fastjson.annotation.JSONField
import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

/**
 * 付呗统一下单请求
 *
 * <AUTHOR> (<EMAIL>)
 * @version FbPayUnifiedOrderRequest.java, v1.0 12/09/2023 11:22 John Exp$
 */
class FbPayUnifiedOrderRequest {
    /**
     * 外部系统订单号
     */
    @JSONField(name = "merchant_order_sn")
    var merchantOrderSn: String? = null

    /**
     * 商家Id
     */
    @JSONField(name = "merchant_id")
    var merchantId: String? = null

    /**
     * 支付方式
     * - wxpay 微信
     * - alipay 支付宝
     * - unionpay 银联云闪付
     */
    @JSONField(name = "pay_type")
    var payType: String = "wxpay"

    /**
     * 支付类型
     * - 02: 公众号
     * - 03: 小程序
     */
    @JSONField(name = "pay_way")
    var payWay: String = "pay_way"

    /**
     * 订单总金额，单位为元，精确到0.01 ~ 10000000
     */
    @JSONField(name = "total_amount")
    var totalAmount: BigDecimal = BigDecimal.ZERO

    /**
     * 门店Id
     */
    @JSONField(name = "store_id")
    var storeId: Int? = null

    /**
     * 收银员Id
     */
    @JSONField(name = "cashier_id")
    var cashierId: String? = null

    /**
     * 公众号/小程序的AppId
     */
    @JSONField(name = "sub_appid")
    var subAppId: String? = null

    /**
     * 用户Id
     * - 微信: openId
     * - 支付宝: userId
     * - 云闪付: upUserId
     */
    @JSONField(name = "user_id")
    var userId: String = ""

    /**
     * 云资金子商户id
     */
    @JSONField(name = "account_id")
    var accountId: String? = null

    /**
     * 分账自定义打标参数
     * - 1: 参与分账（需开白名单）
     * - 0/null: 不传则默认不参与分账
     */
    @JSONField(name = "royalty")
    var royalty: Int? = null

    /**
     * 订单优惠标记，代金券或立减优惠功能的参数（使用单品券时必传）
     */
    @JSONField(name = "goods_tag")
    var goodsTag: String? = null

    /**
     * 订单包含的商品信息
     */
    @JSONField(name = "detail")
    var detail: String? = null

    /**
     * 终端号
     */
    @JSONField(name = "device_no")
    var deviceNo: String? = null

    /**
     * SAAS合作标识
     */
    @JSONField(name = "channel_tag")
    var channelTag: String? = null

    /**
     * 商品描述
     */
    @JSONField(name = "body")
    var body: String? = null

    /**
     * 小程序支付插件场景必填值
     * OPENAPI_PAYMENT_APPLET_PLUGIN
     */
    @JSONField(name = "pay_scene")
    var payScene: String? = null

    /**
     * 订单失效时间，大于1分钟，不能超过60分钟
     * 格式：yyyyMMddHHmmss
     */
    @JSONField(name = "timeout_express")
    var timeoutExpired: String? = null

    /**
     * 支付回调地址
     */
    @JSONField(name = "notify_url")
    var notifyUrl: String? = null

    /**
     * 支付宝业务扩展参数-花呗分期
     */
    @JSONField(name = "alipay_extend_params")
    var alipayExtendParams: String? = null

    /**
     * 平台方门店号
     */
    @JSONField(name = "platform_store_id")
    var platformStoreId: String? = null

    /**
     * 禁止使用优惠券标识
     * - promotion: 支付宝优惠（包含实时优惠+商户优惠）
     * - voucher: 支付宝营销券
     * - 逗号分隔: promotion,voucher
     */
    @JSONField(name = "disable_pay_channels")
    var disablePayChannels: String? = null

    /**
     * 备注
     */
    @JSONField(name = "remark")
    var remark: String? = null
}
