package com.huike.nova.sdk.payment.fubei.domain.request

import com.alibaba.fastjson.annotation.JSONField

/**
 * 付呗订单支持查询
 *
 * <AUTHOR> (<EMAIL>)
 * @version FbPayQueryOrderRequest.java, v1.0 12/09/2023 16:47 John Exp$
 */
data class FbPayQueryOrderRequest(
    /**
     * 付呗订单号
     */
    @JSONField(name = "order_sn")
    var orderSn: String? = null,

    /**
     * 外部订单号
     */
    @JSONField(name = "merchant_order_sn")
    var merchantOrderSn: String? = null,

    /**
     * 机构订单号
     */
    @JSONField(name = "ins_order_sn")
    var insOrderSn: String? = null,

    /**
     * 商家Id，使用服务商维度调用时使用
     */
    @JSONField(name = "merchant_id")
    var merchantId: String? = null
)