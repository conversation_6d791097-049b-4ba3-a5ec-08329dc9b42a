package com.huike.nova.sdk.payment.fubei.domain.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 付呗订单关闭Response
 *
 * <AUTHOR> (<EMAIL>)
 * @version FbPayCloseOrderResponse.java, v1.0 12/21/2023 19:40 John Exp$
 */
@Data
public class FbPayCloseOrderResponse {
    /**
     * 付呗订单号
     */
    @JSONField(name = "order_sn")
    private String orderSn;

    /**
     * 商户订单号
     */
    @JSONField(name = "merchant_order_sn")
    private String merchantOrderSn;

    /**
     * 订单状态
     */
    @JSONField(name = "order_status")
    private String orderStatus;

    /**
     * 硬件设备号
     */
    @JSONField(name = "device_no")
    private String deviceNo;
}
