package com.huike.nova.sdk.volcano.tts

import com.huike.nova.sdk.model.OssStorageConfig

/**
 * 火山引擎TTS 全局配置
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoTtsConfig.java, v1.0 11/23/2023 14:09 John Exp$
 */
object VolcanoTtsConfig {

    /**
     * 应用的AppId
     */
    var appId: String = ""

    /**
     * 应用令牌:目前未生效，填写默认值：default_token
     */
    var accessToken: String = ""

    /**
     * OSS 存储配置
     */
    var ossStorageConfig: OssStorageConfig = OssStorageConfig()
}