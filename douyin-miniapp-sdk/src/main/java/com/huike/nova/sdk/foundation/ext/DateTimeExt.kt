package com.huike.nova.sdk.foundation.ext

import com.huike.nova.common.util.FsDateUtils
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

/**
 * 时间戳工具
 *
 * <AUTHOR> (<EMAIL>)
 * @version DateTimeExt.java, v1.0 11/18/2023 15:48 John Exp$
 */

private val DEFAULT_DATETIME_FORMATTER = DateTimeFormatter.ofPattern(FsDateUtils.SIMPLE_DATETIME_FORMAT)

/**
 * 将LocalDateTime转为指定的格式
 *
 * @receiver 本地时间对象
 */
fun LocalDateTime.utcTimeStampString(): String {
    val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'").withZone(ZoneOffset.UTC)
    return this.atZone(ZoneOffset.ofHours(8)).format(dateTimeFormatter)
}

/**
 * 将LocalDateTime转为yyyy-MM-dd HH:mm:ss (GMT+8)的时间字符串
 *
 * @return 时间
 */
fun LocalDateTime.toDateTimeString(): String {
    return this.atZone(ZoneOffset.ofHours(8)).format(DEFAULT_DATETIME_FORMATTER)
}
