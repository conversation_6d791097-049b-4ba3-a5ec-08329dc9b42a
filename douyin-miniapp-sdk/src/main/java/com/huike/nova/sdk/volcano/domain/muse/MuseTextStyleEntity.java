package com.huike.nova.sdk.volcano.domain.muse;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 本文样式格式
 *
 * <AUTHOR> (<EMAIL>)
 * @version MuseTextStyleEntity.java, v1.0 03/04/2024 10:56 John Exp$
 */
@Data
@Accessors(chain = true)
public class MuseTextStyleEntity {

    /**
     * 文字的字体大小，其实也是字的行高，单位是字号pt  暂定0-72（与编辑器文字大小下拉框对齐）
     */
    @JSONField(name = "FontSize")
    private Integer fontSize;

    /**
     * 花字 id 例子：urs://imuse?id=12312355。通过花字列表API获得
     */
    @JSONField(name = "Flower")
    private String flower;

    /**
     * 文本对齐方式
     * 0      左对齐
     * 1      居中
     * 2      右
     */
    @JSONField(name = "AlignType")
    private Integer alignType;

    /**
     * 归一化x坐标位置 -1 ～ 1 ，（0，0） 为屏幕中心
     */
    @JSONField(name = "PositionX")
    private Float positionX;

    /**
     * 归一化y坐标位置 -1 ～ 1 ，（0，0） 为屏幕中心
     */
    @JSONField(name = "PositionY")
    private Float positionY;

    /**
     * 字体资源ID，而非目录；例子：urs://imuse?id=12312355。通过字体列表API获得
     */
    @JSONField(name = "FontPath")
    private String fontPath;

    /**
     * 字体颜色，用16进制表示，例如：#f8dd4aff，格式是RGBA
     */
    @JSONField(name = "TextColor")
    private String textColor;
}
