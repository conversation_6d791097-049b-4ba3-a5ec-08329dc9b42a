package com.huike.nova.sdk.volcano.domain.muse;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * TTS对象
 *
 * <AUTHOR> (<EMAIL>)
 * @version MuseTtsEntity.java, v1.0 03/04/2024 10:54 John Exp$
 */
@Data
@Accessors(chain = true)
public class MuseTtsEntity {

    /**
     * TTS 文本内容
     */
    @JSONField(name = "Text")
    private String text;

    /**
     * 配音音色Id
     */
    @JSONField(name = "Tone")
    private Integer tone;
}
