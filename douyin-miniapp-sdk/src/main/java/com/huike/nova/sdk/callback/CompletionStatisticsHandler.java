package com.huike.nova.sdk.callback;

/**
 * 统计函数
 *
 * <AUTHOR> (<EMAIL>)
 * @version CompletionStatisticsHandler.java, v1.0 12/05/2023 18:44 John Exp$
 */
@FunctionalInterface
public interface CompletionStatisticsHandler {
    /**
     * 统计 GPT 调用
     *
     * @param providerName 服务名称
     * @param modelName 模型名称
     * @param isSuccess 是否成功
     * @param inputTokens 输入Token数
     * @param outputTokens 输出Token数
     */
    void statistics(String providerName, String modelName, boolean isSuccess, int inputTokens, int outputTokens);
}
