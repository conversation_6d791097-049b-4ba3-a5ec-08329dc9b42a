package com.huike.nova.sdk.volcano.sign

import cn.hutool.core.util.StrUtil
import com.google.common.base.Joiner
import com.huike.nova.common.constant.StringPool
import com.huike.nova.sdk.foundation.SdkConstants
import com.huike.nova.sdk.foundation.ext.defaultIfBlank
import com.huike.nova.sdk.foundation.ext.hexString
import com.huike.nova.sdk.foundation.ext.hmacSha256
import com.huike.nova.sdk.foundation.ext.sha256
import com.huike.nova.sdk.volcano.VolcengineConstants
import org.springframework.web.util.UriComponentsBuilder
import org.springframework.web.util.UriUtils
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.*

/**
 * 火山引擎接口签名工具
 * [Api文档](https://www.volcengine.com/docs/6369/67270)
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcEngineSignUtil.java, v1.0 02/18/2024 17:02 John Exp$
 */
object VolcEngineAuthSignUtil {
    /**
     * 密钥签名，获得带认证信息的请求头
     *
     * @param httpMethod 请求方法
     * @param body 请求体
     * @param signModel 签名数据
     * @return 请求头
     */
    @JvmStatic
    fun getSignedRequestHeaders(httpMethod: String, body: String?, signModel: VolcEngineAuthSignModel): Map<String, String> {
        return getSignedRequestHeaders(httpMethod, (body ?: StringPool.EMPTY).toByteArray(), signModel)
    }


    /**
     * 密钥签名，获得带认证信息的请求头
     *
     * @param httpMethod 请求方法
     * @param bodyBytes 请求体
     * @param signModel 签名数据
     * @return 请求头
     */
    @JvmStatic
    fun getSignedRequestHeaders(httpMethod: String, bodyBytes: ByteArray?, signModel: VolcEngineAuthSignModel): Map<String, String> {
        val url = signModel.getUrl()
        // 组合URL
        val uriComponents = UriComponentsBuilder.fromHttpUrl(url).build()
        // 对body进行SHA256 HASH
        val xContentSha256 = bodyBytes.sha256()

        // 有序的映射
        val queryList = TreeMap<String, String>()
        uriComponents.queryParams.toSingleValueMap().forEach { (t, u) ->
            queryList[t] = u
        }
        val xDate = VolcengineConstants.GMT_FORMATTER.format(LocalDateTime.now(ZoneOffset.UTC))
        val shortXDate = xDate.substring(0, 8)
        val queryString = Joiner.on(StringPool.AMPERSAND).withKeyValueSeparator(StringPool.EQUALS_CHAR).join(queryList)
        val canonicalString = httpMethod + StringPool.NEWLINE +
                uriComponents.path.defaultIfBlank(StringPool.SLASH) + StringPool.NEWLINE +
                queryString + StringPool.NEWLINE +
                "content-type:" + signModel.contentType + StringPool.NEWLINE +
                "host:" + uriComponents.host + StringPool.NEWLINE +
                "x-content-sha256:" + xContentSha256 + StringPool.NEWLINE +
                "x-date:" + xDate + StringPool.NEWLINE +
                StringPool.NEWLINE +
                VolcengineConstants.SIGN_HEADERS + StringPool.NEWLINE +
                xContentSha256
        val hashCanonicalString = canonicalString.sha256()
        val credentialScope = StrUtil.format(VolcengineConstants.FMT_CREDENTIAL_SCOPE, shortXDate, signModel.region, signModel.service)
        val signString = StrUtil.format(VolcengineConstants.FMT_SIGN_STRING, xDate, credentialScope, hashCanonicalString)
        val signKey = genSigningSecretKeyV4(signModel.sk, shortXDate, signModel.region, signModel.service)
        // 进行签名
        val signature = signString.hmacSha256(signKey).hexString()
        return mapOf(
            "Accept" to SdkConstants.APPLICATION_JSON,
            "Content-Type" to signModel.contentType,
            "X-Date" to xDate,
            "X-Content-Sha256" to xContentSha256,
            "Authorization" to getAuthorization(signModel.ak,credentialScope, signature)
        )
    }

    /**
     * 生成最终签名串
     *
     * @param secretKey SK密钥
     * @param date 日期
     * @param region 区域
     * @param service 服务名
     * @return 签名Key
     */
    private fun genSigningSecretKeyV4(secretKey: String, date: String, region: String, service: String): ByteArray {
        val keyDate = date.hmacSha256(secretKey)
        val keyRegion = region.hmacSha256(keyDate)
        val keyService = service.hmacSha256(keyRegion)
        return VolcengineConstants.REQUEST.hmacSha256(keyService)
    }

    /**
     * 获得签名头
     *
     * @param accessKey 请求AK
     * @param credentialScope 认证域
     * @param signature 签名
     * @return 认证请求头的值
     */
    private fun getAuthorization(accessKey: String, credentialScope: String, signature: String): String {
        val sb = StringBuilder()
        sb.append(VolcengineConstants.HMAC_SHA256).append(StringPool.SPACE)
            .append("Credential=").append("${accessKey}/${credentialScope}").append(StringPool.COMMA).append(StringPool.SPACE)
            .append("SignedHeaders=").append(VolcengineConstants.SIGN_HEADERS).append(StringPool.COMMA).append(StringPool.SPACE)
            .append("Signature=").append(signature)
        return sb.toString()
    }
}


/**
 * CanonicalRequest = HTTPRequestMethod + '\n' + CanonicalURI + '\n' + CanonicalQueryString + '\n' + CanonicalHeaders + '\n' + SignedHeaders + '\n' + HexEncode(Hash(RequestPayload))
 */