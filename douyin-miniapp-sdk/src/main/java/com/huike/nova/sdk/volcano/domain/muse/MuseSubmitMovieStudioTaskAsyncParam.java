package com.huike.nova.sdk.volcano.domain.muse;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 高级成片功能
 *
 * <AUTHOR> (<EMAIL>)
 * @version MuseSubmitMovieStudioTaskAsyncParam.java, v1.0 03/04/2024 10:33 John Exp$
 */
@Data
@Accessors(chain = true)
public class MuseSubmitMovieStudioTaskAsyncParam {

    public static final String ACTION = "SubmitMovieStudioTaskAsync";

    public static final String VERSION = "2022-08-01";

    /**
     * 任务名称，默认：智能照片电影_时间戳，长度不超过200
     */
    @JSONField(name = "TaskName")
    private String taskName;

    /**
     * 素材列表，支持urs和http，demo见示例
     */
    @JSONField(name = "MediaList")
    private List<String> mediaList;

    /**
     * 每个素材的变速。
     * 选填，列表长度等于MediaList长度，取值范围0.1~8.0，不变速填空列表或者不填
     */
    @JSONField(name = "VideoSpeedList")
    private List<Float> videoSpeedList;
    
    /**
     * 预期最终成片时长，单位为ms。如果不设置填-1或者不填。
     */
    @JSONField(name = "TotalDuration")
    private Integer totalDuration;
    
    /**
     * 预期每个素材的时长，单位为ms。如果不设置填为-1或者不填。
     */
    @JSONField(name = "SegmentDuration")
    private Integer segmentDuration;

    /**
     * 自定义文本。map 的 key 为 素材的下标，map 的 Text 为文字的信息。
     */
    @JSONField(name = "Texts")
    private Map<String, MuseTextEntity> texts;
    
    /**
     * 配音，选填，列表长度等于MediaList长度,不需要tts的素材，需要手动把对应tts的下标中的text内容置""
     */
    @JSONField(name = "TTS")
    private List<MuseTtsEntity> tts;

    /**
     * 字幕样式
     */
    @JSONField(name = "CaptionStyle")
    private MuseTextStyleEntity captionStyle;

    /**
     * 自定义背景音乐，支持urs和http，大小不超过30m
     */
    @JSONField(name = "MusicId")
    private String musicId;

    /**
     * 视频比例及数量，每个比例数量范围[1, 10]，每次最多支持两种比例，
     * 默认：[["16:9",1],["9:16",1]]。
     * 支持9:16,16:9,18:9,21:9,1:1,1:2.048,2.048:1以及自定义即自由设定宽高比。
     */
    @JSONField(name = "RatioNumList")
    private List<Object[]> ratioNumList;

    /**
     * 生成视频的分辨率，目前支持1080p、720p（p大小写都可以）。默认"1080p"。
     */
    @JSONField(name = "Resolution")
    private String resolution;

    /**
     * 推荐特效是否开启。列表长度固定为9。分别对应：
     * 1. 智能音乐推荐
     * 2. 智能音乐卡点
     * 3. 智能转场推荐
     * 4. 智能运镜推荐
     * 5. 素材智能裁剪
     * 6. 智能视频动画推荐
     * 7. 智能特效推荐
     * 8. 智能文案推荐
     * 9. 保持原有素材顺序
     * 开启第几个，列表的第几项就写true，否则写false。例如开启智能音乐推荐，列表第一项就填true。
     * 默认全部开启
     */
    @JSONField(name = "EffectList")
    private Boolean[] effectList;

    /**
     * 帧率，默认25，可选[24, 25, 30, 50, 60]
     */
    @JSONField(name = "Fps")
    private Integer fps;

    /**
     * 是否保存至成片库，默认：否（24h清理）
     */
    @JSONField(name = "SaveFilm")
    private Boolean saveFilm;
}
