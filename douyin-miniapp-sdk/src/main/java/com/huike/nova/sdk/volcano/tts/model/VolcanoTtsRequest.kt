package com.huike.nova.sdk.volcano.tts.model

import com.alibaba.fastjson.annotation.JSONField
import com.huike.nova.sdk.volcano.tts.VolcanoTtsConfig

/**
 * 火山引擎TTS请求
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoTtsRequest.java, v1.0 11/23/2023 14:13 John Exp$
 */
class VolcanoTtsRequest {
    /**
     * 应用信息
     */
    @JSONField(name = "app")
    val app: VolcanoAppInfoPartial = VolcanoAppInfoPartial(VolcanoTtsConfig.appId)

    /**
     * 用户信息
     */
    @JSONField(name = "user")
    val user: VolcanoUserPartial = VolcanoUserPartial()

    /**
     * 音频相关配置
     */
    @JSONField(name = "audio")
    val audio: VolcanoTtsAudioPartial = VolcanoTtsAudioPartial()

    /**
     * 请求相关配置
     */
    @JSONField(name = "request")
    val request: VolcanoTtsRequestPartial = VolcanoTtsRequestPartial()
}