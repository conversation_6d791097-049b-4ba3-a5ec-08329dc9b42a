package com.huike.nova.sdk.foundation.ext

import com.huike.nova.common.constant.StringPool
import org.apache.commons.lang3.StringUtils

/**
 * 字符串工具类
 *
 * <AUTHOR> (<EMAIL>)
 * @version StringUtilExt.java, v1.0 11/29/2023 16:47 John Exp$
 */
fun String?.defaultIfBlank(defaultStr: String): String {
    return StringUtils.defaultIfBlank(this, defaultStr)
}

fun String?.nullToEmpty(): String {
    return StringUtils.defaultString(this)
}

fun String?.emptyToNull(): String? {
    return if (this == StringPool.EMPTY) {
        null
    } else {
        this
    }
}

private val REGEX_CRLF_TAB = Regex("[\r\n\t]")

/**
 * 移除换行符
 *
 * @param str 原始字符串
 * @return 处理后的字符串
 */
fun removeCRLFAndTab(str: String?): String? {
    return if (str.isNullOrBlank()) {
        null
    } else {
        str.replace(REGEX_CRLF_TAB, StringPool.SPACE).replace(StringPool.QUOTE, StringPool.EMPTY)
    }
}