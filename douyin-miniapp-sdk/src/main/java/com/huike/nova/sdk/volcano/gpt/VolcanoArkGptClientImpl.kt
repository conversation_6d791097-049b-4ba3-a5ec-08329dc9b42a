package com.huike.nova.sdk.volcano.gpt

import cn.hutool.http.HttpStatus
import com.huike.nova.common.constant.ChatGptConstant
import com.huike.nova.common.util.LogUtil
import com.huike.nova.sdk.baidu.BceConstants
import com.huike.nova.sdk.baidu.model.BceWenXinGenericError
import com.huike.nova.sdk.exception.SdkCommonErrorEnum
import com.huike.nova.sdk.exception.SdkException
import com.huike.nova.sdk.foundation.KtorClientFactory
import com.huike.nova.sdk.foundation.SdkConstants
import com.huike.nova.sdk.foundation.annotation.Slf4jKt.Companion.log
import com.huike.nova.sdk.foundation.ext.defaultIfBlank
import com.huike.nova.sdk.foundation.ext.gptDispatchers
import com.huike.nova.sdk.foundation.ext.removeCRLFAndTab
import com.huike.nova.sdk.foundation.ext.toObject
import com.huike.nova.sdk.foundation.gptHttpClient
import com.huike.nova.sdk.utils.SdkStatisticsUtil
import com.huike.nova.sdk.volcano.VolcengineConstants
import com.huike.nova.sdk.volcano.gpt.model.VolcanoArkModelRequest
import com.huike.nova.sdk.volcano.gpt.model.VolcanoArkResponse
import com.huike.nova.sdk.volcano.sign.VolcEngineAuthSignModel.Companion.createMaasApiSignModel
import com.huike.nova.sdk.volcano.sign.VolcEngineAuthSignUtil.getSignedRequestHeaders
import com.volcengine.helper.Const
import com.volcengine.model.maas.api.Api
import io.ktor.client.*
import io.reactivex.BackpressureStrategy
import io.reactivex.Flowable
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okhttp3.sse.EventSource
import okhttp3.sse.EventSourceListener
import okhttp3.sse.EventSources
import java.util.*

/**
 * 火山GPT客户端
 * 文档可直接点击访问 [火山方舟文档](https://www.volcengine.com/docs/82379/1133187)
 *
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoArkGptClientImpl.java, v1.0 02/20/2024 10:07 John Exp$
 */
class VolcanoArkGptClientImpl : VolcanoArkGptClient {

    companion object {
        /**
         * 默认的方舟模型
         */
        private const val DEFAULT_ARK_MODEL = "skylark-lite-public"

        /**
         * 火山方舟
         */
        const val SERVICE_PROVIDER_VOLCENGINE_ARK: String = "volcano_ark"

    }

    /**
     * Http请求客户端
     */
    private val httpClient: HttpClient by lazy {
        KtorClientFactory.getClient(VolcanoArkGptClientImpl::class.java.name)
    }

    /**
     * 进行对话补齐
     *
     * @param model 模型名称
     * @param content 对话内容
     * @param request 请求
     * @return 请求返回
     */
    override fun chatCompletionStream(model: String?, content: String?, request: VolcanoArkModelRequest): String {
        if (content.isNullOrBlank()) {
            throw SdkException(SdkCommonErrorEnum.PARAM_ERROR).setDetailMessage("参数错误, message不能为空")
        }
        request.checkValid()
        val modelName = model.defaultIfBlank(DEFAULT_ARK_MODEL)
        val contentBuilder = StringBuilder()
        var error: Throwable? = null
        var inTokens = 0
        var outTokens = 0

        createAsyncChat(modelName, content, request)
            .gptDispatchers().blockingSubscribe({
                it.usage?.let { usage ->
                    inTokens += usage.promptTokens ?: 0
                    outTokens += usage.completionTokens ?: 0
                }
                // 获得第一个Choice中的回答内容
                val firstChoiceContent = removeCRLFAndTab(it.choice?.message?.content)
                if (firstChoiceContent?.isNotBlank() == true) {
                    contentBuilder.append(firstChoiceContent)
                }
            }, {
                error = it
            })
        SdkStatisticsUtil.completionCall(SERVICE_PROVIDER_VOLCENGINE_ARK, modelName, Objects.isNull(error), inTokens, outTokens)
        error?.let {
            throw if (it is SdkException) {
                it
            } else {
                SdkException(SdkCommonErrorEnum.BCE_AIGC_GENERATION_ERROR).setDetailMessage("Ark生成失败: ${it.message}")
            }
        }
        return contentBuilder.toString()
    }

    /**
     * 回答问题：SSE方式
     *
     * @param model 模型名称
     * @param content 提问内容
     * @param request 请求选项参数
     * @param listener SSE事件监听回调
     */
    override fun textGenerationSse(model: String, content: String, request: VolcanoArkModelRequest?, listener: EventSourceListener) {
        if (content.isBlank()) {
            throw SdkException(SdkCommonErrorEnum.PARAM_ERROR).setDetailMessage("参数错误，content不能为空")
        }
        if (request == null) {
            throw SdkException(SdkCommonErrorEnum.PARAM_ERROR).setDetailMessage("参数错误，request不能为空")
        }
        request.checkValid()
        val chatReq = chatReq(model, content, request, true)
        val buffer = chatReq.toByteArray()
        val signRequest = getSignedRequestHeaders(SdkConstants.POST, buffer, createMaasApiSignModel(VolcengineConstants.Api.MAAS_CHAT_API, request.accessKey, request.secretKey))
        val sseRequest = createOKHttpSseRequest(VolcengineConstants.Api.MAAS_CHAT_API, buffer, signRequest)
        EventSources.createFactory(gptHttpClient).newEventSource(sseRequest, listener)
    }

    /**
     * 生成方舟的对话请求，返回ProtoBuf对象
     *
     * @param model 模型名称
     * @param content 对话内容
     * @param req 定制参数
     * @param stream 是否以流的方式进行返回
     * @return [Api.ChatReq]对象
     */
    @Suppress("SameParameterValue")
    private fun chatReq(model: String, content: String, req: VolcanoArkModelRequest, stream: Boolean): Api.ChatReq {
        // PB Builder对象
        val arkReqBuilder = Api.ChatReq.newBuilder()
        // 模型对象
        val apiModel = Api.Model.newBuilder()
            .setName(model).build()
        // 消息对象
        val message = Api.Message.newBuilder()
            .setRole(Const.MaasChatRoleOfUser).setContent(content).build()
        arkReqBuilder
            .setModel(apiModel)
            .addMessages(message)
            .setParameters(getParameterByReq(req))
            .setStream(stream)
        // 生成对象
        return arkReqBuilder.build()
    }

    /**
     * 获得模型对话的参数
     *
     * @param req 定制参数
     * @return [Api.Parameters]参数，用于定制一些参数
     */
    private fun getParameterByReq(req: VolcanoArkModelRequest): Api.Parameters {
        // 参数设置
        val parametersBuilder = Api.Parameters.newBuilder()
        if (req.maxTokens != null) {
            parametersBuilder.setMaxTokens(req.maxTokens!!.toLong())
        }
        if (req.maxNewTokens != null) {
            parametersBuilder.setMaxNewTokens(req.maxNewTokens!!.toLong())
        }
        if (req.minNewTokens != null) {
            parametersBuilder.setMinNewTokens(req.minNewTokens!!.toLong())
        }
        if (req.temperature != null) {
            parametersBuilder.setTemperature(req.temperature!!)
        }
        if (req.topP != null) {
            parametersBuilder.setTopP(req.topP!!)
        }
        if (req.topK != null) {
            parametersBuilder.setTopK(req.topK!!.toLong())
        }
        if (req.maxPromptTokens != null) {
            parametersBuilder.setMaxPromptTokens(req.maxPromptTokens!!.toLong())
        }
        return parametersBuilder.build()
    }

    /**
     * 创建OKHttpSSE请求
     *
     * @param url 请求地址
     * @param data 数据
     * @param headers 请求头
     * @return 请求
     */
    @Suppress("SameParameterValue")
    private fun createOKHttpSseRequest(url: String, data: ByteArray, headers: Map<String, String>): Request {
        val requestBody = data.toRequestBody()
        val requestBuilder = Request.Builder()
            .url(url)
            .post(requestBody)
        headers.forEach {
            requestBuilder.header(it.key, it.value)
        }
        requestBuilder.addHeader("Accept", "application/json, text/event-stream")
        return requestBuilder.build()
    }

    /**
     * 创建异步请求（基于流式）
     *
     * @param model 模型名称
     * @param content 对话内容
     * @param request 请求参数
     * @return 返回结果: Flowable
     */
    private fun createAsyncChat(model: String, content: String, request: VolcanoArkModelRequest): Flowable<VolcanoArkResponse> {
        return Flowable.create({ emitter ->
            textGenerationSse(model, content, request, object : EventSourceListener() {
                override fun onOpen(eventSource: EventSource, response: Response) {
                    super.onOpen(eventSource, response)
                    LogUtil.info(log, "VolcanoArkGptClientImpl.createAsyncChat >> 开启VOLCANO-ARK异步流式SSE请求..")
                }

                override fun onClosed(eventSource: EventSource) {
                    emitter.onComplete()
                    LogUtil.info(log, "VolcanoArkGptClientImpl.createAsyncChat >> 关闭VOLCANO-ARK异步流式SSE请求..")
                }

                override fun onEvent(eventSource: EventSource, id: String?, type: String?, data: String) {
                    if (ChatGptConstant.STREAM_DONE_FLAG.equals(data, true)) {
                        // 结束
                        return
                    }
                    val response = data.toObject<VolcanoArkResponse>()
                    response?.let {
                        emitter.onNext(it)
                    }
                }

                override fun onFailure(eventSource: EventSource, t: Throwable?, response: Response?) {
                    // 如果返回值是200（成功）
                    if (response?.code == HttpStatus.HTTP_OK) {
                        val dataMsg = response.body?.string()
                        emitter.onError(SdkException(SdkCommonErrorEnum.VOLCANO_ARK_SERVER_ERROR).setDetailMessage(dataMsg))
                    } else {
                        emitter.onError(t ?: Throwable(response?.body?.string()))
                    }
                }
            })

            // 为了防止数据丢失使用BUFFER方式的背压
        }, BackpressureStrategy.BUFFER)
    }
}