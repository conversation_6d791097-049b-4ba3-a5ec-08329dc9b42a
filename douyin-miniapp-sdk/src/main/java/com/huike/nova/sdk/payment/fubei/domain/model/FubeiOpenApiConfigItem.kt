package com.huike.nova.sdk.payment.fubei.domain.model

import com.huike.nova.sdk.douyin.ext.nullToEmpty

/**
 * <AUTHOR> (<EMAIL>)
 * @version FubeiOpenApiConfig.java, v1.0 12/08/2023 10:19 John Exp$
 */
data class FubeiOpenApiConfigItem(
    /**
     * 商家端应用AppId
     */
    var appId: String? = null,

    /**
     * 商家端应用AppSecret
     */
    var appSecret: String? = null,

    /**
     * 代理商Id
     */
    var vendorSn: String? = null,

    /**
     * 代理商Secret
     */
    var vendorSecret: String? = null
) {
    fun initMerchantApi(appId: String, secret:String): FubeiOpenApiConfigItem {
        this.appId = appId
        this.appSecret = secret
        return this
    }

    fun initAgentApi(vendorSn: String, secret:String): FubeiOpenApiConfigItem {
        this.vendorSn = vendorSn
        this.vendorSecret = secret
        return this
    }



    /**
     * 是否是商家级别
     */
    fun isAgentLevel() = vendorSn?.isNotBlank() == true

    /**
     * 是否是代理商级别
     */
    fun isMerchantLevel() = appId?.isNotBlank() == true

    /**
     * 获得Secret
     *
     * @return Secret
     */
    fun getSecret(): String {
        return (appSecret ?: vendorSecret).nullToEmpty()
    }
}
