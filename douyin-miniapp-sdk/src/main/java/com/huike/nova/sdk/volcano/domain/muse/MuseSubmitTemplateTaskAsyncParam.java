package com.huike.nova.sdk.volcano.domain.muse;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 提交视频合成任务
 *
 * <AUTHOR> (<EMAIL>)
 * @version ICCloudSubmitTemplateTaskAsyncParam.java, v1.0 02/29/2024 16:23 John Exp$
 */
@Data
@Accessors(chain = true)
public class MuseSubmitTemplateTaskAsyncParam {

    public static final String ACTION = "SubmitTemplateTaskAsync";

    public static final String VERSION = "2021-09-01";

    @J<PERSON><PERSON><PERSON>(name = "TemplateId")
    private String templateId;
    
    @J<PERSON><PERSON>ield(name = "TemplateType")
    private String templateType;

    @JSONField(name = "ParamsList")
    private List<MuseMediaInfoEntity> paramsList;

    @JSONField(name = "Title")
    private String title;

    @JSONField(name = "Resolution")
    private String Resolution;

    @JSONField(name = "Fps")
    private Integer Fps;

    @JSO<PERSON>ield(name = "Tts")
    private List<MuseTtsInfoEntity> Tts;

    @JSONField(name = "CallbackUri")
    private String CallbackUri;

    @JSONField(name = "CallbackParams")
    private String CallbackParams;

    @JSONField(name = "TeamSpaceId")
    private String TeamSpaceId;

    @JSONField(name = "Owner")
    private MuseMediaResourceOwnerEntity owner;
}
