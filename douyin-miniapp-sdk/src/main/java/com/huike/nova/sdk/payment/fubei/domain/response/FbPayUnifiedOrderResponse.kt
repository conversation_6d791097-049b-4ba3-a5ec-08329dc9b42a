package com.huike.nova.sdk.payment.fubei.domain.response

import com.alibaba.fastjson.annotation.JSONField
import java.math.BigDecimal

/**
 * 付呗订单预下单响应
 *
 * <AUTHOR> (<EMAIL>)
 * @version FbPayUnifiedOrderResponse.java, v1.0 12/09/2023 11:53 John Exp$
 */
class FbPayUnifiedOrderResponse {
    /**
     * 付呗订单号
     */
    @JSONField(name = "order_sn")
    var orderSn: String? = null

    /**
     * 外部系统订单号
     */
    @JSONField(name = "merchant_order_sn")
    var merchantOrderSn: String? = null

    /**
     * 预支付凭证
     */
    @JSONField(name = "prepay_id")
    var prepayId: String? = null

    /**
     * 付呗商户号
     */
    @JSONField(name = "merchant_id")
    var merchantId: String? = null

    /**
     * 支付方式
     * - wxpay: 微信
     * - alipay: 支付宝
     * - unionpay: 银联
     */
    @JSONField(name = "pay_type")
    var payType: String? = null

    /**
     * 订单金额，精确到0.01
     */
    @JSONField(name = "total_amount")
    var totalAmount: BigDecimal? = null

    /**
     * 商户门店号
     */
    @JSONField(name = "store_id")
    var storeId: Int? = null

    /**
     * 收银员ID
     */
    @JSONField(name = "cashier_id")
    var cashierId: Int? = null

    /**
     * 付款用户id，微信openid、支付宝账户等
     */
    @JSONField(name = "user_id")
    var userId: String? = null

    /**
     * 终端号
     */
    @JSONField(name = "device_no")
    var deviceNo: String? = null

    /**
     * 附加数据，原样返回，该字段主要用于商户携带订单的自定义数据
     */
    @JSONField(name = "attach")
    var attach: String? = null

    /**
     * 签名包，当 pay_type=wxpay 时才返回该字段
     */
    @JSONField(name = "sign_package")
    var signPackage: FbPayWechatSignPackage? = null

    /**
     * 银联云闪付APP拉起支付URL
     */
    @JSONField(name = "union_pay_url")
    var unionPayUrl: String? = null
}

/**
 * 微信支付签名包
 */
class FbPayWechatSignPackage {
    /**
     * 应用id
     */
    @JSONField(name = "appId")
    var appId: String? = null

    /**
     * 时间戳
     */
    @JSONField(name = "timeStamp")
    var timeStamp: String? = null

    /**
     * 随机串
     */
    @JSONField(name = "nonceStr")
    var nonceStr: String? = null

    /**
     * prepayId参数
     */
    @JSONField(name = "package")
    var packageValue: String? = null

    /**
     * 签名类型，默认RSA
     */
    @JSONField(name = "signType")
    var signType: String? = null

    /**
     * 签名
     */
    @JSONField(name = "paySign")
    var paySign: String? = null
}