package com.huike.nova.sdk.volcano.tts;

import com.huike.nova.sdk.model.SdkSpeechSynthesisResult;
import com.huike.nova.sdk.volcano.tts.model.VolcanoTtsRequest;
import com.huike.nova.sdk.volcano.tts.model.VolcanoTtsResponse;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

/**
 * 火山引擎 TTS 接口
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcanoTtsApi.java, v1.0 11/23/2023 15:20 John Exp$
 */
public interface VolcanoTtsApi {

    /**
     * 使用火山引擎语音合成，并上传至OSS
     *
     * @param text 转换的语音
     * @param voice 音色
     * @param sampleRate 采样率
     * @param volumeRatio 音量，默认1.0
     * @param emotion 情感，如果非多情感音色，该字段留空
     * @return 上传后返回的信息对象
     */
    SdkSpeechSynthesisResult speechSynthesis(@Nonnull String text, @Nonnull String voice, int sampleRate, float volumeRatio, @Nullable String emotion);

    /**
     * TTS合成
     * @param request 请求
     */
    VolcanoTtsResponse ttsSynthesis(@Nonnull VolcanoTtsRequest request);
}
