package com.huike.nova.sdk.volcano.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 火山引擎返回
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcEngineApiResponse.java, v1.0 02/28/2024 16:52 John Exp$
 */
@Data
public class VolcEngineApiResponse<T> {
    /**
     * 返回元数据
     */
    @JSONField(name = "ResponseMetadata")
    private VolcEngineMetaData responseMetadata;

    /**
     * 错误码
     */
    @JSONField(name = "Code")
    private Integer code;

    /**
     * 错误消息
     */
    @JSONField(name = "Message")
    private String message;

    /**
     * 结果
     */
    @JSONField(name = "Result")
    private T result;
}