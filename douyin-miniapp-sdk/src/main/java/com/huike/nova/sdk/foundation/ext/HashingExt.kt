/*
 *
 *  * Hangzhou Huike Technology co.,ltd.
 *  * Copyright (C) 2013-2023 All Rights Reserved.
 *
 *
 */

package com.huike.nova.sdk.foundation.ext

import com.google.common.hash.Hashing
import java.nio.charset.StandardCharsets

/**
 * 哈希扩展
 *
 * <AUTHOR> (<EMAIL>)
 * @version HashingExt.java, v1.0 02/28/2023 13:52 John Exp$
 */
fun String?.md5(): String {
    return if (this.isNullOrEmpty()) {
        ""
    } else {
        Hashing.md5().hashString(this, StandardCharsets.UTF_8).toString()
    }
}

fun String?.sha256(): String {
    return if (this.isNullOrEmpty()) {
        ""
    } else {
        Hashing.sha256().hashString(this, StandardCharsets.UTF_8).toString()
    }
}

fun ByteArray?.sha256(): String {
    return Hashing.sha256().hashBytes(this ?: ByteArray(0)).toString()
}

fun ByteArray.md5(): String {
    return Hashing.md5().hashBytes(this).toString()
}

/**
 * Hmac sha1
 *
 * @param key Key
 * @return 或者hmacSha1摘要
 */
fun String?.hmacSha1(key: String): ByteArray {
    if (this.isNullOrEmpty()) {
        return ByteArray(0)
    }
    return Hashing.hmacSha1(key.toByteArray()).hashString(this, StandardCharsets.UTF_8).asBytes()
}

fun String?.hmacSha256(key: String): ByteArray {
    return if (this.isNullOrEmpty()) {
        ByteArray(0)
    } else {
        Hashing.hmacSha256(key.toByteArray()).hashString(this, StandardCharsets.UTF_8).asBytes()
    }
}

fun String?.hmacSha256(key: ByteArray): ByteArray {
    return if (this.isNullOrEmpty()) {
        ByteArray(0)
    } else {
        Hashing.hmacSha256(key).hashString(this, StandardCharsets.UTF_8).asBytes()
    }
}