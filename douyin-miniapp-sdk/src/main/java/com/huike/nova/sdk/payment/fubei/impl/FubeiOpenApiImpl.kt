package com.huike.nova.sdk.payment.fubei.impl

import cn.hutool.core.text.StrPool
import com.google.common.base.Splitter
import com.google.common.net.HttpHeaders
import com.google.common.net.MediaType
import com.huike.nova.common.constant.StringPool
import com.huike.nova.common.util.LogUtil
import com.huike.nova.common.util.UrlEncodeUtil
import com.huike.nova.sdk.foundation.HttpRequestExecutor
import com.huike.nova.sdk.foundation.KtorClientFactory
import com.huike.nova.sdk.foundation.ext.md5
import com.huike.nova.sdk.foundation.ext.toJson
import com.huike.nova.sdk.payment.fubei.FubeiOpenApi
import com.huike.nova.sdk.payment.fubei.FubeiOpenApiConfig
import com.huike.nova.sdk.payment.fubei.FubeiOpenApiConstants
import com.huike.nova.sdk.payment.fubei.domain.model.FubeiOpenApiConfigItem
import com.huike.nova.sdk.payment.fubei.ext.toFubeiRequest
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.slf4j.MDCContext
import org.slf4j.LoggerFactory
import java.util.function.Consumer

/**
 * 付呗OpenApi实现类
 *
 * <AUTHOR> (<EMAIL>)
 * @version FubeiOpenApiImpl.java, v1.0 12/08/2023 11:12 John Exp$
 */
internal class FubeiOpenApiImpl : FubeiOpenApi {
    companion object {
        private val log = LoggerFactory.getLogger(FubeiOpenApiImpl::class.java)
    }

    /**
     * 请求接口
     */
    private val apiClient by lazy {
        KtorClientFactory.getClient(FubeiOpenApiConstants.DEFAULT_IDENTIFIER_CLIENT)
    }

    /**
     * 协程作用域
     */
    private val scope = HttpRequestExecutor.coroutineScope

    /**
     * 发送付呗请求
     *
     * @param configKey 配置Key
     * @param method 接口名称
     * @param data 请求格式
     * @return 响应格式
     */
    override fun sendRequest(configKey: String, method: String, data: Any?): String {
        val req = data.toFubeiRequest(configKey, method) ?: return StrPool.EMPTY_JSON
        val deferred = scope.async(Dispatchers.Default + MDCContext()) {
            var reqBody = ""
            var respBody: String
            val response = apiClient.post(FubeiOpenApiConstants.SHQ_API_GATEWAY_URL) {
                headers {
                    append(HttpHeaders.CONTENT_TYPE, MediaType.JSON_UTF_8.toString())
                }
                reqBody = req.toJson()
                setBody(reqBody)
            }
            if (!response.status.isSuccess()) {
                LogUtil.warn(log, "FubeiOpenApiImpl.sendRequest >> FUBEI-OPENAPI >> REQ_URL=${FubeiOpenApiConstants.SHQ_API_GATEWAY_URL}^API_METHOD=${method}^REQ_BODY=${reqBody}^RESP_STATUS=${response.status}")
                throw Exception("Request failed, ${response.status}")
            }
            response.bodyAsText().also {
                respBody = it
            }
            LogUtil.warn(log, "FubeiOpenApiImpl.sendRequest >> FUBEI-OPENAPI >> REQ_URL=${FubeiOpenApiConstants.SHQ_API_GATEWAY_URL}^API_METHOD=${method}^REQ_BODY=${reqBody}^RESP_BODY=${respBody}")
            respBody
        }
        // 等待执行完成
        return runBlocking {
            runCatching {
                deferred.await()
            }.onFailure {
                LogUtil.warn(log, "请求失败, api:{}, data:{}", method, data.toJson())
            }.getOrThrow()
        }
    }

    /**
     * 配置商户级API调用
     *
     * @param appId 应用Id
     * @param appSecret 应用密钥
     */
    override fun configMerchantApi(configKey: String, appId: String, appSecret: String) {
        FubeiOpenApiConfig.setOpenApiConfig(configKey, FubeiOpenApiConfigItem(appId, appSecret))
    }

    /**
     * 检查回调签名
     *
     * @param callbackString 回调内容
     * @param appSecret app密钥
     * @param dataCallback 回调数据
     * @return true-通过 false-未通过
     */
    @Suppress("UnstableApiUsage")
    override fun checkCallbackSignature(callbackString: String, appSecret: String, dataCallback: Consumer<String>?): Boolean {
        return runCatching {
            val elements = Splitter.on(StringPool.AMPERSAND).trimResults().withKeyValueSeparator(StringPool.EQUALS_CHAR).split(callbackString)
            // 检查签名串
            val sign = elements[FubeiOpenApiConstants.SIGN] ?: return false
            // 过滤Sign，排序，组合成x1=y1&x2=y2&x3=y3... + 密钥的字符串
            val map = elements.filter { it.key != FubeiOpenApiConstants.SIGN }.toSortedMap()
            val preSign = map
                .map {
                    UrlEncodeUtil.urlDecode(it.key) + StringPool.EQUALS_CHAR + UrlEncodeUtil.urlDecode(it.value)
                }
                .joinToString(StringPool.AMPERSAND) + appSecret
            // 做md5和签名比较
            val matched = sign.equals(preSign.md5(), true)
            if (matched) {
                dataCallback?.accept(UrlEncodeUtil.urlDecode(map[FubeiOpenApiConstants.DATA]?: StringPool.EMPTY))
            }
            matched
        }.getOrDefault(false)
    }
}