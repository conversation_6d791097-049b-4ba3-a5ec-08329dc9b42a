package com.huike.nova.sdk.volcano.domain.muse;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 槽位字段介绍
 * (<a href="https://bytedance.larkoffice.com/wiki/wikcnB9eyC4HR31cQc3i7YNO46g">文档链接</a>)
 * <AUTHOR> (<EMAIL>)
 * @version ICCloudMediaInfo.java, v1.0 02/29/2024 15:33 John Exp$
 */
@Data
public class MuseMediaInfoEntity {
    /**
     * 只读，槽位的唯一id，用于映射槽位到模板中的节点
     */
    private String uuid;

    /**
     * 只读，槽位类型，目前支持video、text、temptext；text指普通文字，temptext为特效文字
     */
    private String type;

    /**
     * 只读，槽位在最终视频中的开始时间，单位秒
     */
    @JSONField(name = "starttime")
    private BigDecimal startTime;

    /**
     * 只读，槽位在最终视频中的结束时间，单位秒
     */
    @JSONField(name = "endtime")
    private BigDecimal endTime;

    /**
     * 只读，text类型槽位中可能会有，渲染时需要，用户不必关注
     */
    private String prefix;

    /**
     * 只读，支持的TTS字数上限。（默认语速下）
     */
    private long recommendWords;

    /**
     * type为video时，传入的资源路径，http链接。不仅可以传视频链接（只支持mp4）还可以传图片链接。传入的视频时长应不小于clip_end-clip_start；且文件大小不大于50M
     * 视频，音频，图片 支持的素材封装格式与编码，见下方表格
     */
    private String source;

    /**
     * text类型槽位中的文本内容，文字不超过50个字符
     * 或者：
     * temptext类型槽位中的文本内容，例：{ 0："民宿打卡•像住在城堡里" ， 1："民宿打卡•像住在城堡里" }  ，内部key不可修改，value为可修改的文本。每段文字不超过50个字符
     */
    private String text;

    /**
     * 视频裁切起点和终点，单位秒（当source指向视频链接时，链接中的视频会被裁剪后再使用，裁剪起始时间为clip_start，裁剪结束时间为clip_end）
     */
    @JSONField(name = "clip_start")
    private Double clipStart;
    
    /**
     * 这两个字段可修改，但必须同时修改，保证clip_end与clip_start之差不变
     */
    @JSONField(name = "clip_end")
    private Double clipEnd;

    /**
     * 素材音量，范围0到200（素材原音量为100）
     */
    private Integer volume = 100;

    /**
     * 介绍
     */
    private String description;

    /**
     * video槽位中视频或图片的裁切坐标，分别是从左下角开始顺时针的4个点，坐标原点在图像中心，
     * 右上方为正坐标。例：全图坐标为[[-1.0，-1.0]，[-1.0，1.0]，[1.0，1.0]，[1.0，-1.0]]。x和y坐标需要分别除以二分之一宽高，因此坐标范围是[-1，1]。
     */
    private String crop;

    /**
     * 模板宽度
     */
    private Long width;

    /**
     * 模板高度
     */
    private Long height;

    /**
     * Id
     */
    private String reproductionId;
}
