package com.huike.nova.sdk.foundation.annotation

import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * Slf4j Kotlin
 *
 * <AUTHOR> (<EMAIL>)
 * @version Slf4jKt.java, v1.0 09/07/2023 18:10 John Exp$
 */
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
annotation class Slf4jKt {
    companion object{
        val <reified T> T.log: Logger
            inline get() = LoggerFactory.getLogger(T::class.java)
    }
}
