package com.huike.nova.sdk.volcano.domain.muse;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 文本实体
 *
 * <AUTHOR> (<EMAIL>)
 * @version MuseTextEntity.java, v1.0 03/04/2024 11:11 John Exp$
 */
@Data
@Accessors(chain = true)
public class MuseTextEntity {
    @JSONField(name = "Text")
    private String text;

    @JSONField(name = "Style")
    private MuseTextStyleEntity style;
}
