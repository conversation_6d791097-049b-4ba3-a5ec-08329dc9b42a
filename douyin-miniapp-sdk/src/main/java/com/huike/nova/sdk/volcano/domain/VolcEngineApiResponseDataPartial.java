package com.huike.nova.sdk.volcano.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 火山接口返回数据包装
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcEngineApiResponseDataPartial.java, v1.0 02/28/2024 16:55 John Exp$
 */
@Data
public class VolcEngineApiResponseDataPartial<T> {
    /**
     * 数据
     */
    @JSONField(name = "Data")
    private T data;

    /**
     * 消息内容
     */
    @JSONField(name = "Message")
    private String message;
}
