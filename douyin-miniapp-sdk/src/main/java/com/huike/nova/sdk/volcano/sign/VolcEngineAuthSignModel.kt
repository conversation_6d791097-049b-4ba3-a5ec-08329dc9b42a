package com.huike.nova.sdk.volcano.sign

import com.huike.nova.common.constant.StringPool
import com.huike.nova.sdk.douyin.ext.nullToEmpty
import com.huike.nova.sdk.foundation.SdkConstants
import com.huike.nova.sdk.volcano.VolcengineConstants
import org.springframework.web.util.UriComponentsBuilder

/**
 * 火山引擎签名对象
 *
 * <AUTHOR> (<EMAIL>)
 * @version VolcEngineSign.java, v1.0 02/18/2024 16:27 John Exp$
 */
class VolcEngineAuthSignModel(
    /**
     * 区域
     */
    var region: String = "cn-beijing",

    /**
     * 服务
     */
    var service: String = "iam",

    /**
     * scheme
     */
    val scheme: String = "https",

    /**
     * 主机地址
     */
    val host: String = "",

    /**
     * 接口地址
     */
    val path: String = "/",

    /**
     * AccessKey
     */
    var ak: String = "",

    /**
     * SecretKey
     */
    var sk: String = "",

    /**
     * 数据类型
     */
    var contentType: String = "application/x-www-form-urlencoded",

    /**
     * 查询串
     */
    var query: String? = null
) {
    /**
     * Get url
     *
     * @return url
     */
    fun getUrl(): String {
        return UriComponentsBuilder.newInstance()
            .scheme(scheme)
            .host(host)
            .path(path)
            .query(query ?: StringPool.EMPTY)
            .build().toUriString()
    }


    companion object {
        @JvmStatic
        fun create(region: String, host: String, ak: String, sk: String): VolcEngineAuthSignModel {
            val model = VolcEngineAuthSignModel(
                region = region,
                ak = ak,
                sk = sk,
                service = getServiceByHost(host),
                host = host
            )
            return model
        }

        /**
         * 创建火山方舟接口签名对象
         * https://www.volcengine.com/docs/82379/1099475
         *
         * @param url 接口地址，如https://maas-api.ml-platform-cn-beijing.volces.com/api/v1/chat
         * @param ak ak
         * @param sk sk
         * @return 签名对象
         */
        @JvmStatic
        fun createMaasApiSignModel(url: String, ak: String, sk: String): VolcEngineAuthSignModel {
            val uriComponents = UriComponentsBuilder.fromUriString(url).build()
            val host = uriComponents.host.nullToEmpty()
            return VolcEngineAuthSignModel(
                region = getRegionByHost(host),
                service = VolcengineConstants.Service.ML_MAAS,
                scheme = uriComponents.scheme.nullToEmpty(),
                host = host,
                path = uriComponents.path ?: StringPool.SLASH,
                ak = ak,
                sk = sk,
                // 火山方舟使用Protobuf格式
                contentType = SdkConstants.PROTOBUF_CONTENT_TYPE
            )
        }

        /**
         * 智能创作云接口
         * [接口文档](https://bytedance.larkoffice.com/wiki/wikcnB9eyC4HR31cQc3i7YNO46g)
         *
         * @param ak AccessKey
         * @param sk SecretKey
         * @return 签名对象
         */
        @JvmStatic
        fun createIcCloudApiModel(ak: String, sk: String, action: String, version: String): VolcEngineAuthSignModel {
            val url = UriComponentsBuilder.fromUriString(VolcengineConstants.Api.ICP_API)
                .queryParam("Action", action)
                .queryParam("Version", version)
                .build().toUriString()
            return createApiSignModel(url, ak, sk, VolcengineConstants.Region.CN_NORTH_1, VolcengineConstants.Service.IC_CLOUD_MUSE)
        }

        /**
         * 创建接口签名对象
         *
         * @param url 请求接口地址
         * @param ak AccessKey
         * @param sk SecretKey
         * @param region 区域
         * @param service 服务名称
         * @return 签名对象
         */
        @JvmStatic
        fun createApiSignModel(url: String, ak: String, sk: String, region: String, service: String, contentType: String = SdkConstants.APPLICATION_JSON): VolcEngineAuthSignModel {
            val uriComponents = UriComponentsBuilder.fromUriString(url).build()
            val host = uriComponents.host.nullToEmpty()
            return VolcEngineAuthSignModel(
                region = region,
                service = service,
                scheme = uriComponents.scheme.nullToEmpty(),
                host = host,
                path = uriComponents.path ?: StringPool.SLASH,
                ak = ak,
                sk = sk,
                contentType = contentType,
                query = uriComponents.query
            )
        }

        /**
         * 从主机地址地址中获得区域
         *
         * @param host 主机地址
         * @return 区域
         */
        private fun getRegionByHost(host: String): String {
            return when {
                host.contains(VolcengineConstants.Region.CN_BEIJING) -> VolcengineConstants.Region.CN_BEIJING
                host.contains(VolcengineConstants.Region.CN_SHANGHAI) -> VolcengineConstants.Region.CN_SHANGHAI
                host.contains(VolcengineConstants.Region.CN_HANGZHOU) -> VolcengineConstants.Region.CN_HANGZHOU
                else -> StringPool.EMPTY
            }
        }

        /**
         * 从主机地址中获得服务名称
         *
         * @param host 主机地址
         * @return 服务名称
         */
        private fun getServiceByHost(host: String): String {
            val n = host.indexOf(StringPool.DOT)
            return if (n > 0) {
                host.substring(0, n)
            } else{
                host
            }
        }
    }
}
