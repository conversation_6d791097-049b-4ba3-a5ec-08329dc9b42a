package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshGrantDO;
import com.fshows.gosh.dao.mapper.GoshGrantMapper;
import com.fshows.gosh.dao.repository.GoshGrantDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 来逛呗权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Service
public class GoshGrantDAOImpl extends ServiceImpl<GoshGrantMapper, GoshGrantDO> implements GoshGrantDAO {

    /**
     * 查询所有的权限
     *
     * @param belongType
     * @return
     */
    @Override
    public List<GoshGrantDO> queryAll(Integer belongType, String blocId) {
        return getBaseMapper().queryAll(belongType, blocId);
    }

    /**
     * 查询所有的权限
     *
     * @param belongType 归属类型 1-商家版小程序
     * @return 权限列表
     */
    @Override
    public List<GoshGrantDO> findAll(Integer belongType) {
        return query().eq(GoshGrantDO.BELONG_TYPE, belongType).eq(GoshGrantDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }
}
