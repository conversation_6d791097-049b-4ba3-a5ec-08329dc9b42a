/**
 * <AUTHOR>
 * @date 2025/1/6 11:31
 * @version 1.0 GoshShopRegisterExamineRecordDAO
 */
package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshShopRegisterExamineRecordDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @version GoshShopRegisterExamineRecordDAO.java, v 0.1 2025-01-06 11:31 tuyuwei
 */
public interface GoshShopRegisterExamineRecordDAO extends IService<GoshShopRegisterExamineRecordDO> {


    /**
     * 根据recordId查询记录
     *
     * @param recordId
     * @return
     */
    GoshShopRegisterExamineRecordDO getByRecordId(String recordId);

    /**
     * 根据shopId查询
     *
     * @param shopId
     * @return
     */
    List<GoshShopRegisterExamineRecordDO> getByShopId(String shopId);


    /**
     * 根据申请单id查询
     *
     * @param applyId
     * @return
     */
    GoshShopRegisterExamineRecordDO getByApplyId(String applyId);

    /**
     * 更新审核状态为失败
     *
     * @param recordId
     */
    void updateApplyStatus(String recordId);


    /**
     * 查寻最新入网个人信息
     * @param shopId
     * @param createTime
     * @return
     */
    GoshShopRegisterExamineRecordDO getRecordByTime(String shopId, Date createTime);

    /**
     * 根据状态查询操作记录
     *
     * @param applyStatus
     * @param shopId
     * @return
     */
    List<GoshShopRegisterExamineRecordDO> getByStatus(Integer applyStatus, String shopId);


    /**
     * 查询最新申请成功记录
     *
     * @param shopId
     * @return
     */
    GoshShopRegisterExamineRecordDO getSuccessApply(String shopId);

    /**
     * 查询驳回前最新的生效数据
     * @param shopId
     * @param createTime
     * @return
     */
    GoshShopRegisterExamineRecordDO getRecordByTimeAndStatus(String shopId, Date createTime);

}