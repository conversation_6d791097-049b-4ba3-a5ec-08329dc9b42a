package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.AilikeTiktokLifeProductGroupDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 抖音来客商品组表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface AilikeTiktokLifeProductGroupDAO extends IService<AilikeTiktokLifeProductGroupDO> {

    /**
     * 根据权益卡Id获得商品组记录
     *
     * @param merchantCardId 商家卡Id
     * @return 商品组记录
     */
    AilikeTiktokLifeProductGroupDO getProductGroupByQykMerchantCardId(String merchantCardId);
}
