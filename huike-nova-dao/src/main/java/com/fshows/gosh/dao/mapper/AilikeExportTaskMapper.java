package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.ExportTaskListDTO;
import com.fshows.gosh.dao.entity.AilikeExportTaskDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 导出任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface AilikeExportTaskMapper extends BaseMapper<AilikeExportTaskDO> {

    /**
     * 导出任务列表
     *
     * @param page
     * @param query
     * @return
     */
    Page<AilikeExportTaskDO> exportTaskList(Page<ExportTaskListDTO> page, @Param("query") ExportTaskListDTO query);
}
