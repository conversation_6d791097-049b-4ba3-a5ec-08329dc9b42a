/**
 * <AUTHOR>
 * @date 2025/1/6 11:35
 * @version 1.0 GoshShopRegisterExamineRecordDAOImpl
 */
package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshShopRegisterExamineRecordDO;
import com.fshows.gosh.dao.mapper.GoshShopRegisterExamineRecordMapper;
import com.fshows.gosh.dao.repository.GoshShopRegisterExamineRecordDAO;
import com.huike.nova.common.enums.ApplyExamineStatusEnum;
import com.huike.nova.common.enums.DelFlagEnum;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @version GoshShopRegisterExamineRecordDAOImpl.java, v 0.1 2025-01-06 11:35 tuyuwei
 */
@Service
public class GoshShopRegisterExamineRecordDAOImpl extends ServiceImpl<GoshShopRegisterExamineRecordMapper, GoshShopRegisterExamineRecordDO> implements GoshShopRegisterExamineRecordDAO {

    @Override
    public GoshShopRegisterExamineRecordDO getByRecordId(String recordId) {
        return query()
                .eq(GoshShopRegisterExamineRecordDO.RECORD_ID, recordId)
                .eq(GoshShopRegisterExamineRecordDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .one();
    }

    @Override
    public List<GoshShopRegisterExamineRecordDO> getByShopId(String shopId) {
        return query()
                .eq(GoshShopRegisterExamineRecordDO.SHOP_ID, shopId)
                .eq(GoshShopRegisterExamineRecordDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .orderByDesc(GoshShopRegisterExamineRecordDO.CREATE_TIME)
                .orderByDesc(GoshShopRegisterExamineRecordDO.ID)
                .list();
    }

    @Override
    public GoshShopRegisterExamineRecordDO getByApplyId(String applyId) {
        return query()
                .eq(GoshShopRegisterExamineRecordDO.APPLY_ID, applyId)
                .eq(GoshShopRegisterExamineRecordDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .orderByDesc(GoshShopRegisterExamineRecordDO.CREATE_TIME)
                .last("limit 1")
                .one();
    }

    @Override
    public void updateApplyStatus(String recordId) {
        update().eq(GoshShopRegisterExamineRecordDO.RECORD_ID, recordId)
                .eq(GoshShopRegisterExamineRecordDO.APPLY_STATUS, ApplyExamineStatusEnum.CHANGE_SETTLE_SUCCESS.getValue())
                .set(GoshShopRegisterExamineRecordDO.APPLY_STATUS, ApplyExamineStatusEnum.CHANGE_SETTLE_FAIL.getValue())
                .update();
    }

    @Override
    public GoshShopRegisterExamineRecordDO getRecordByTime(String shopId, Date createTime) {
        return getBaseMapper().getRecordByTime(shopId, createTime);
    }

    @Override
    public List<GoshShopRegisterExamineRecordDO> getByStatus(Integer applyStatus, String shopId) {
        return query()
                .eq(GoshShopRegisterExamineRecordDO.SHOP_ID, shopId)
                .eq(GoshShopRegisterExamineRecordDO.APPLY_STATUS, applyStatus)
                .eq(GoshShopRegisterExamineRecordDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .list();

    }

    @Override
    public GoshShopRegisterExamineRecordDO getSuccessApply(String shopId) {
        return getBaseMapper().getSuccessApply(shopId);
    }

    @Override
    public GoshShopRegisterExamineRecordDO getRecordByTimeAndStatus(String shopId, Date createTime) {
        return getBaseMapper().getRecordByTimeAndStatus(shopId, createTime);
    }

}