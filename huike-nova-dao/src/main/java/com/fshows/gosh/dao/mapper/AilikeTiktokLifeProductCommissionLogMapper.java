package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.result.ProductCommissionLogInfoResultDTO;
import com.fshows.gosh.dao.entity.AilikeTiktokLifeProductCommissionLogDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 抖音来客商品佣金变更记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
public interface AilikeTiktokLifeProductCommissionLogMapper extends BaseMapper<AilikeTiktokLifeProductCommissionLogDO> {

    /**
     * 分页查询商品佣金记录
     *
     * @param pageDto
     * @return {@link Page<ProductCommissionLogInfoResultDTO>}
     * <AUTHOR>
     */
    Page<ProductCommissionLogInfoResultDTO> pageListProductCommissionLog(Page<ProductCommissionLogInfoResultDTO> pageDto, @Param("businessProductId") String businessProductId);

}
