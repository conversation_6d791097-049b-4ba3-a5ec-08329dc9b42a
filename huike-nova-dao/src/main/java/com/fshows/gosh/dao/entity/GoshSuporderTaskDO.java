package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 补单任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@TableName("gosh_suporder_task")
public class GoshSuporderTaskDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 补单任务ID
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 集团id
     */
    @TableField("bloc_id")
    private String blocId;

    /**
     * 结算单号
     */
    @TableField("serial_no")
    private String serialNo;

    /**
     * 外部补单订单id
     */
    @TableField("out_sup_order_id")
    private String outSupOrderId;

    /**
     * 补单总金额，单位分(即补单文件中的“交易总金额-退款总金额=补单总金额”)
     */
    @TableField("total_amount")
    private Long totalAmount;

    /**
     * 补单明细总笔数（即交易补单笔数+退款补单笔数）
     */
    @TableField("total_count")
    private Integer totalCount;

    /**
     * 任务处理状态:INIT - 任务初始化;PROCESSING - 任务处理中;SUCCESS - 任务处理完成;FAIL - 任务处理失败
     */
    @TableField("task_status")
    private String taskStatus;

    /**
     * 任务处理失败原因
     */
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 补单任务处理阶段:1.任务初始化--TASK_INIT;2.文件解析入库--FILE_HANDLER;3.银行补单创单--SINGLE_SUB_ORDER;4.银行批次文件生成及上传--BANK_FILE_CREATE_UPLOAD;5.银行批次处理 -- BANK_FILE_HANDLER;6.补单批次完成 -- TASK_COMPLETE
     */
    @TableField("task_step")
    private String taskStep;

    /**
     * 付账通补单文件url
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String TASK_ID = "task_id";

    public static final String BLOC_ID = "bloc_id";

    public static final String OUT_SUP_ORDER_ID = "out_sup_order_id";

    public static final String TOTAL_AMOUNT = "total_amount";

    public static final String TOTAL_COUNT = "total_count";

    public static final String TASK_STATUS = "task_status";

    public static final String ERROR_MSG = "error_msg";

    public static final String TASK_STEP = "task_step";

    public static final String FILE_URL = "file_url";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
