package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.domain.param.ServicePriceConfigParamDTO;
import com.fshows.gosh.dao.entity.AilikeTiktokServicePriceConfigDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 集团服务商金额配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
public interface AilikeTiktokServicePriceConfigDAO extends IService<AilikeTiktokServicePriceConfigDO> {
    /**
     * 查询配置
     *
     * @param dto
     * @return
     */
    AilikeTiktokServicePriceConfigDO findConfig(ServicePriceConfigParamDTO dto);
}
