package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.entity.GoshBlocBindGrantDO;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <p>
 * 集团权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
public interface GoshBlocBindGrantMapper extends BaseMapper<GoshBlocBindGrantDO> {

    /**
     * 根据集团id查询集团权限id集合
     *
     * @param blocId 集团id
     * @return 集团权限id集合
     */
    List<String> findBlocGrantIdListByBlocId(@Param("blocId") String blocId);
}
