package com.fshows.gosh.dao.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.ExportTaskListDTO;
import com.fshows.gosh.dao.entity.AilikeExportTaskDO;
import com.fshows.gosh.dao.mapper.AilikeExportTaskMapper;
import com.fshows.gosh.dao.repository.AilikeExportTaskDAO;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.DelStatusEnum;
import com.huike.nova.common.enums.startask.web.OperateExportTaskStatusEnum;
import com.huike.nova.common.metadata.PageParam;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 导出任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Service
public class AilikeExportTaskDAOImpl extends ServiceImpl<AilikeExportTaskMapper, AilikeExportTaskDO> implements AilikeExportTaskDAO {

    /**
     * 查询用户导出中的任务数量
     *
     * @param userId
     * @return
     */
    @Override
    public Integer getUserExportingTaskCount(String userId) {
        return query().eq(AilikeExportTaskDO.USER_ID, userId)
                .eq(AilikeExportTaskDO.DEL_FLAG, CommonConstant.INTEGER_ONE)
                .eq(AilikeExportTaskDO.TASK_STATUS, OperateExportTaskStatusEnum.PROCESSING.getTaskStatus())
                .count().intValue();
    }

    /**
     * 根据taskId查询任务
     *
     * @param taskId
     * @return
     */
    @Override
    public AilikeExportTaskDO getByTaskId(String taskId) {
        return query().eq(AilikeExportTaskDO.TASK_ID, taskId).eq(AilikeExportTaskDO.DEL_FLAG, 1).one();
    }

    /**
     * 导出任务更新
     *
     * @param exportTaskDO
     */
    @Override
    public boolean updateTaskInfo(AilikeExportTaskDO exportTaskDO) {
        return update().set(StrUtil.isNotBlank(exportTaskDO.getOssPath()), AilikeExportTaskDO.OSS_PATH, exportTaskDO.getOssPath())
                .set(StrUtil.isNotBlank(exportTaskDO.getOssUrl()), AilikeExportTaskDO.OSS_URL, exportTaskDO.getOssUrl())
                .set(StrUtil.isNotBlank(exportTaskDO.getRemark()), AilikeExportTaskDO.REMARK, exportTaskDO.getRemark())
                .set(ObjectUtil.isNotNull(exportTaskDO.getCompleteTime()), AilikeExportTaskDO.COMPLETE_TIME, exportTaskDO.getCompleteTime())
                .set(StrUtil.isNotBlank(exportTaskDO.getFileName()), AilikeExportTaskDO.FILE_NAME, exportTaskDO.getFileName())
                .set(AilikeExportTaskDO.TASK_STATUS, exportTaskDO.getTaskStatus())
                .eq(AilikeExportTaskDO.TASK_ID, exportTaskDO.getTaskId())
                .eq(AilikeExportTaskDO.DEL_FLAG, CommonConstant.INTEGER_ONE)
                .update();
    }

    /**
     * 导出任务列表
     *
     * @param pageParam
     * @return
     */
    @Override
    public Page<AilikeExportTaskDO> exportTaskList(PageParam<ExportTaskListDTO> pageParam) {
        Page<ExportTaskListDTO> page = new Page<>();
        page.setCurrent(pageParam.getPage());
        page.setSize(pageParam.getPageSize());
        return getBaseMapper().exportTaskList(page, pageParam.getQuery());
    }

    /**
     * 删除任务
     *
     * @param taskId
     */
    @Override
    public void deleteTask(String taskId) {
        update().set(AilikeExportTaskDO.DEL_FLAG, CommonConstant.INTEGER_TWO).eq(AilikeExportTaskDO.TASK_ID, taskId).eq(AilikeExportTaskDO.DEL_FLAG, CommonConstant.INTEGER_ONE).update();
    }

    /**
     * 批量删除任务
     *
     * @param taskIdList
     */
    @Override
    public void batchDeleteTask(List<String> taskIdList) {
        update().set(AilikeExportTaskDO.DEL_FLAG, DelStatusEnum.DEL.getValue())
                .in(AilikeExportTaskDO.TASK_ID, taskIdList)
                .eq(AilikeExportTaskDO.DEL_FLAG, DelStatusEnum.NOT_DEL.getValue())
                .update();
    }

}
