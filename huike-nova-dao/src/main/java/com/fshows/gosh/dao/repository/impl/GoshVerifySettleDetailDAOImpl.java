package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fshows.gosh.dao.entity.GoshShopSettleFormDO;
import com.fshows.gosh.dao.entity.GoshVerifySettleDetailDO;
import com.fshows.gosh.dao.mapper.GoshVerifySettleDetailMapper;
import com.fshows.gosh.dao.repository.GoshVerifySettleDetailDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.constant.CommonConstant;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 核销结算明细数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Service
public class GoshVerifySettleDetailDAOImpl extends ServiceImpl<GoshVerifySettleDetailMapper, GoshVerifySettleDetailDO> implements GoshVerifySettleDetailDAO {

    /**
     * 根据结算单号查询补单明细
     *
     * @param serialNo 结算单号
     * @param dataId   数据Id
     * @return
     */
    @Override
    public List<GoshVerifySettleDetailDO> getListBySerialNo(String serialNo, long dataId) {
        return getBaseMapper().getListBySerialNo(serialNo, dataId);
    }

    /**
     * 根据门店Id查询补单明细
     *
     * @param shopId 门店Id
     * @return
     */
    @Override
    public List<GoshVerifySettleDetailDO> getListByShopId(String shopId) {
        return query().eq(GoshVerifySettleDetailDO.SHOP_ID, shopId)
                .list();
    }

    /**
     * 根据门店结算单号查询补单明细
     *
     * @param shopSerialNo 门店结算单号
     * @return
     */
    @Override
    public List<GoshVerifySettleDetailDO> getListByShopSerialNo(String shopSerialNo) {
        return query().eq(GoshVerifySettleDetailDO.SHOP_SERIAL_NO, shopSerialNo)
                .list();
    }

    /**
     * 结算单号
     *
     * @param serialNo 结算单号
     */
    @Override
    public boolean removeBySerialNo(String serialNo) {
        return remove(new LambdaQueryWrapper<GoshVerifySettleDetailDO>()
                .eq(GoshVerifySettleDetailDO::getSerialNo, serialNo)
        );
    }
}
