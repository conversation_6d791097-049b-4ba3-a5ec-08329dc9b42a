package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.ActivityListParamDTO;
import com.fshows.gosh.dao.domain.result.ActivityListResultDTO;
import com.fshows.gosh.dao.domain.result.ActivityStatusStatisticsResultDTO;
import com.fshows.gosh.dao.domain.result.QueryProductListResultDTO;
import com.fshows.gosh.dao.entity.AilikeQykWxActivityDO;
import com.fshows.gosh.dao.mapper.AilikeQykWxActivityMapper;
import com.fshows.gosh.dao.repository.AilikeQykWxActivityDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.metadata.PageParam;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 微信营销活动 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
public class AilikeQykWxActivityDAOImpl extends ServiceImpl<AilikeQykWxActivityMapper, AilikeQykWxActivityDO> implements AilikeQykWxActivityDAO {

    /**
     * 根据商品Id查询活动明细
     *
     * @param productId 商品id
     * @return
     */
    @Override
    public AilikeQykWxActivityDO findByProductId(String productId) {
        return query().eq(AilikeQykWxActivityDO.PRODUCT_ID, productId)
                .eq(AilikeQykWxActivityDO.IS_DEL, CommonConstant.NUMBER_ZERO)
                .last(" limit 1").one();
    }

    /**
     * 根据商品Id查询活动列表
     *
     * @param productIdList 商品id列表
     * @return
     */
    @Override
    public List<AilikeQykWxActivityDO> findListByProductdIdList(List<String> productIdList) {
        return query().in(AilikeQykWxActivityDO.PRODUCT_ID, productIdList)
                .eq(AilikeQykWxActivityDO.IS_DEL, CommonConstant.NUMBER_ZERO).list();
    }

    /**
     * 根据活动Id查询活动明细
     *
     * @param activityId 活动Id
     * @return
     */
    @Override
    public AilikeQykWxActivityDO findByActivityId(String activityId) {
        return query().eq(AilikeQykWxActivityDO.ACTIVITY_ID, activityId)
                .eq(AilikeQykWxActivityDO.IS_DEL, CommonConstant.NUMBER_ZERO)
                .last(" limit 1").one();
    }

    /**
     * 营销活动列表
     *
     * @param pageParam 参数
     * @return 活动列表
     */
    @Override
    public Page<ActivityListResultDTO> pageActivityList(PageParam<ActivityListParamDTO> pageParam) {
        Page<ActivityListResultDTO> page = new Page<>();
        page.setCurrent(pageParam.getPage());
        page.setSize(pageParam.getPageSize());
        return getBaseMapper().pageActivityList(page, pageParam.getQuery());
    }

    /**
     * 营销活动列表
     *
     * @param pageParam 参数
     * @return 活动列表
     */
    @Override
    public List<ActivityStatusStatisticsResultDTO> activityListStatistics(ActivityListParamDTO dto) {
        return getBaseMapper().activityListStatistics(dto);
    }

    /**
     * 导出商品信息
     *
     * @param dto           参数
     * @param resultHandler 结果数据
     */
    @Override
    public void exportActivityList(ActivityListParamDTO dto, ResultHandler<ActivityListResultDTO> resultHandler) {
        getBaseMapper().pageActivityList(dto, resultHandler);
    }

    /**
     * 营销活动上线
     */
    @Override
    public void activityOnline() {
        getBaseMapper().activityOnline();
    }

    /**
     * 营销活动下线
     */
    @Override
    public void activityOffline() {
        getBaseMapper().activityOffline();
    }

    /**
     * 查询线上活动列表
     *
     * @param dataId 数据Id
     * @return
     */
    @Override
    public List<AilikeQykWxActivityDO> findOnlineActivityList(Long dataId) {
        return getBaseMapper().findOnlineActivityList(dataId);
    }
}
