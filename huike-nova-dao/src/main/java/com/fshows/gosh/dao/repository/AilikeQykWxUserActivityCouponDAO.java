package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.AilikeQykWxUserActivityCouponDO;

/**
 * <p>
 * 微信营销活动-用户活动券表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
public interface AilikeQykWxUserActivityCouponDAO extends IService<AilikeQykWxUserActivityCouponDO> {

    /**
     * 根据券码查询活动券
     *
     * @param couponCode 券码
     * @return 活动券
     */
    AilikeQykWxUserActivityCouponDO getByCouponCode(String couponCode);

}
