package com.fshows.gosh.dao.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.PageWithdrawListDTO;
import com.fshows.gosh.dao.domain.result.PageWithdrawResultDTO;
import com.fshows.gosh.dao.domain.result.WithdrawStatisticsDTO;
import com.fshows.gosh.dao.entity.GoshShopWithdrawDO;
import com.fshows.gosh.dao.mapper.GoshShopWithdrawMapper;
import com.fshows.gosh.dao.repository.GoshShopWithdrawDAO;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.metadata.PageParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 商户提现表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Service
public class GoshShopWithdrawDAOImpl extends ServiceImpl<GoshShopWithdrawMapper, GoshShopWithdrawDO> implements GoshShopWithdrawDAO {

    @Override
    public boolean updateByWithdrawNo(String withdrawNo, String platformWithdrawNo, String withdrawStatus, String globalTrackStatus, String reason, Date finishTime, Integer refundTag) {
        return update()
                .set(StrUtil.isNotBlank(withdrawStatus), GoshShopWithdrawDO.WITHDRAW_STATUS, withdrawStatus)
                .set(StrUtil.isNotBlank(platformWithdrawNo), GoshShopWithdrawDO.PLATFORM_WITHDRAW_NO, platformWithdrawNo)
                .set(GoshShopWithdrawDO.GLOBAL_TRACK_STATUS, globalTrackStatus)
                .set(StrUtil.isNotBlank(reason), GoshShopWithdrawDO.REJECTED_REASON, reason)
                .set(ObjectUtil.isNotNull(finishTime), GoshShopWithdrawDO.FINISH_TIME, finishTime)
                .set(ObjectUtil.isNotNull(refundTag), GoshShopWithdrawDO.IS_REFUNDED, refundTag)
                .eq(GoshShopWithdrawDO.WITHDRAW_NO, withdrawNo).update();
    }

    @Override
    public GoshShopWithdrawDO getByWithdrawNo(String withdrawNo) {
        return query().eq(GoshShopWithdrawDO.WITHDRAW_NO, withdrawNo).one();
    }

    @Override
    public GoshShopWithdrawDO getBySerialNoAndWithdrawStatus(String shopSerialNo, String withdrawStatus) {
        return query().eq(GoshShopWithdrawDO.SHOP_SERIAL_NO, shopSerialNo)
                .eq(GoshShopWithdrawDO.WITHDRAW_STATUS, withdrawStatus).one();
    }

    @Override
    public List<GoshShopWithdrawDO> getListByShopSerialNo(String shopSerialNo) {
        return query().eq(GoshShopWithdrawDO.SHOP_SERIAL_NO, shopSerialNo).list();
    }

    @Override
    public List<GoshShopWithdrawDO> getListByStatusAndTimeRange(String withdrawStatus, Date startTime, Date endTime) {
        return query().eq(GoshShopWithdrawDO.WITHDRAW_STATUS, withdrawStatus)
                .between(GoshShopWithdrawDO.CREATE_TIME, startTime, endTime)
                .list();
    }

    @Override
    public GoshShopWithdrawDO getLastOnByShopSerialNo(String shopSerialNo) {
        return query().eq(GoshShopWithdrawDO.SHOP_SERIAL_NO, shopSerialNo)
                .orderByDesc(GoshShopWithdrawDO.CREATE_TIME).last("limit 1").one();
    }

    @Override
    public GoshShopWithdrawDO getFirstByCreateTime(Date startTime, Date endTime) {
        return query()
                .between(GoshShopWithdrawDO.CREATE_TIME, startTime, endTime)
                .orderByAsc(GoshShopWithdrawDO.CREATE_TIME)
                .last("limit 1")
                .one();
    }

    @Override
    public GoshShopWithdrawDO getLastOneByCreateTime(Date startTime, Date endTime) {
        return query()
                .between(GoshShopWithdrawDO.CREATE_TIME, startTime, endTime)
                .orderByDesc(GoshShopWithdrawDO.CREATE_TIME)
                .last("limit 1")
                .one();
    }

    @Override
    public List<GoshShopWithdrawDO> getListByMinMaxId(Long mindId, Long maxId, Date startTime, Date endTime) {
        return query()
                .between(GoshShopWithdrawDO.CREATE_TIME, startTime, endTime)
                .between(GoshShopWithdrawDO.ID, mindId, maxId).list();
    }

    /**
     * 账户提现管理列表
     *
     * @param pageDTO
     * @return
     */
    @Override
    public Page<PageWithdrawResultDTO> pageWithdrawList(PageParam<PageWithdrawListDTO> pageDTO) {
        Page<PageWithdrawListDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());
        return getBaseMapper().pageWithdrawList(page, pageDTO.getQuery());
    }

    /**
     * 账户提现统计
     *
     * @param withdrawListDTO
     * @return
     */
    @Override
    public WithdrawStatisticsDTO withdrawStatistics(PageWithdrawListDTO withdrawListDTO) {
        return getBaseMapper().withdrawStatistics(withdrawListDTO);
    }

    /**
     * 账户提现管理列表(流式)
     *
     * @param param
     * @param resultHandler
     */
    @Override
    @DS(CommonConstant.READ_ONLY_DB_DATA_SOURCE)
    public void exportOperationWithdrawList(PageWithdrawListDTO param, ResultHandler<PageWithdrawResultDTO> resultHandler) {
        getBaseMapper().pageWithdrawList(param, resultHandler);
    }

    /**
     * 更新提现单不能重提
     *
     * @param orderNo
     * @return
     */
    @Override
    public boolean updateNoRewithdraw(String orderNo) {
        return update()
                .set(GoshShopWithdrawDO.IS_REWITHDRAW, 2)
                .eq(GoshShopWithdrawDO.WITHDRAW_NO, orderNo)
                .update();
    }

    /**
     * 电子回单更新
     *
     * @param withdrawNo
     * @param electronicReceiptUrl
     * @return
     */
    @Override
    public boolean updateElectronicReceiptUrl(String withdrawNo, String electronicReceiptUrl) {
        return update()
                .set(GoshShopWithdrawDO.ELECTRONIC_RECEIPT_URL, electronicReceiptUrl)
                .eq(GoshShopWithdrawDO.WITHDRAW_NO, withdrawNo)
                .update();
    }

    /**
     * 更新结算账户信息
     *
     * @param withdrawNo
     * @param settleAccountNo
     * @param bankName
     * @param accountName
     * @return
     */
    @Override
    public boolean updatesAccountInfo(String withdrawNo, String settleAccountNo, String bankName, String accountName) {
        if (StringUtils.isBlank(settleAccountNo) && StringUtils.isBlank(bankName) && StringUtils.isBlank(accountName)) {
            return Boolean.TRUE;
        }
        return update()
                .set(StringUtils.isNotBlank(settleAccountNo), GoshShopWithdrawDO.SETTLE_ACCOUNT_NO, settleAccountNo)
                .set(StringUtils.isNotBlank(bankName), GoshShopWithdrawDO.BANK_NAME, bankName)
                .set(StringUtils.isNotBlank(accountName), GoshShopWithdrawDO.ACCOUNT_NAME, accountName)
                .eq(GoshShopWithdrawDO.WITHDRAW_NO, withdrawNo)
                .update();
    }

    /**
     * 查询没有银行卡账号的提现单
     *
     * @return
     */
    @Override
    public List<GoshShopWithdrawDO> findWithdrawByNoSettleAccountNo() {
        return query()
                .eq(GoshShopWithdrawDO.SETTLE_ACCOUNT_NO, "")
                .ne(GoshShopWithdrawDO.BANK_NAME, "")
                .list();
    }

    /**
     * 根据转账单号查询提现单
     *
     * @param orderNo
     * @return
     */
    @Override
    public GoshShopWithdrawDO getByTransferNo(String orderNo) {
        return query()
                .eq(GoshShopWithdrawDO.TRANSFER_NO, orderNo)
                .eq(GoshShopWithdrawDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }
}
