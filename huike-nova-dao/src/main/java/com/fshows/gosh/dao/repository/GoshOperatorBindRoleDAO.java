package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.param.OperationAccountRoleListDTO;
import com.fshows.gosh.dao.entity.GoshOperatorBindRoleDO;
import com.fshows.gosh.dao.entity.GoshOperatorGrantInfoDO;
import com.fshows.gosh.dao.entity.GoshOperatorRoleInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0 GoshOperatorBindGrantDAO
 * @date 2024/11/1 15:45
 */
public interface GoshOperatorBindRoleDAO extends IService<GoshOperatorBindRoleDO> {

    /**
     * 获取权限列表
     *
     * @param operatorId
     * @return
     */
    List<GoshOperatorGrantInfoDO> getGrantByOperatorId(String operatorId);


    /**
     * 通过运营id获取角色信息
     * @param operatorId
     * @return
     */
    List<OperationAccountRoleListDTO> getRoleInfo(List<String> operatorId);

    /**
     * 查询绑定角色关系
     *
     * @return
     */
    List<GoshOperatorBindRoleDO> getBindRoles();



    List<GoshOperatorRoleInfoDO> getByOperatorId(String operatorId);


    /**
     * 根据角色查询账号绑定关系
     *
     * @param roleId
     * @return
     */
    List<GoshOperatorBindRoleDO> getBindRolesByRoleId(String roleId);

    /**
     * 根据operatorId删除角色关系
     * @param operatorId
     */
    void deleteBindByOperatorId(String operatorId);


    /**
     * 根据roleId删除角色关系
     * @param roleId
     */
    void deleteBindByRoleId(String roleId);

}
