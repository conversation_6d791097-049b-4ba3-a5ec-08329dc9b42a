package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.param.PageWithdrawListDTO;
import com.fshows.gosh.dao.domain.result.PageWithdrawResultDTO;
import com.fshows.gosh.dao.domain.result.WithdrawStatisticsDTO;
import com.fshows.gosh.dao.entity.GoshShopTransferDO;
import com.fshows.gosh.dao.entity.GoshShopWithdrawDO;
import com.huike.nova.common.metadata.PageParam;
import org.apache.ibatis.session.ResultHandler;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 商户提现表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
public interface GoshShopWithdrawDAO extends IService<GoshShopWithdrawDO> {


    /**
     * 更新提现单
     *
     * @param withdrawNo
     * @param platformWithdrawNo
     * @param withdrawStatus
     * @param globalTrackStatus
     * @param reason
     * @param finishTime
     * @return boolean
     */
    boolean updateByWithdrawNo(String withdrawNo, String platformWithdrawNo, String withdrawStatus, String globalTrackStatus, String reason, Date finishTime, Integer refundTag);


    /**
     * 根据提现单号查询
     *
     * @param withdrawNo
     * @return {@link GoshShopWithdrawDO}
     */
    GoshShopWithdrawDO getByWithdrawNo(String withdrawNo);


    /**
     * 根据结算单号、状态查询转账单信息
     *
     * @param shopSerialNo
     * @param withdrawStatus
     * @return {@link GoshShopWithdrawDO}
     */
    GoshShopWithdrawDO getBySerialNoAndWithdrawStatus(String shopSerialNo, String withdrawStatus);


    /**
     * 根据结算单号查询提现
     *
     * @param shopSerialNo
     * @return {@link List}<{@link GoshShopTransferDO}>
     */
    List<GoshShopWithdrawDO> getListByShopSerialNo(String shopSerialNo);


    /**
     * 根据状态和时间范围查询
     *
     * @param withdrawStatus
     * @param startTime
     * @param endTime
     * @return {@link List}<{@link GoshShopWithdrawDO}>
     */
    List<GoshShopWithdrawDO> getListByStatusAndTimeRange(String withdrawStatus, Date startTime, Date endTime);


    /**
     * 根据商户结算单号查询最新失败的提现单
     *
     * @param shopSerialNo
     * @return {@link GoshShopWithdrawDO}
     */
    GoshShopWithdrawDO getLastOnByShopSerialNo(String shopSerialNo);

    /**
     * 查询指定日期第一条数据
     *
     * @param startTime
     * @param endTime
     * @return {@link GoshShopWithdrawDO}
     */
    GoshShopWithdrawDO getFirstByCreateTime(Date startTime, Date endTime);

    /**
     * 查询指定日期最后一条数据
     *
     * @param startTime
     * @param endTime
     * @return {@link GoshShopWithdrawDO}
     */
    GoshShopWithdrawDO getLastOneByCreateTime(Date startTime, Date endTime);


    /**
     * 根据id和时间查询
     *
     * @param mindId
     * @param maxId
     * @param startTime
     * @param endTime
     * @return {@link List}<{@link GoshShopWithdrawDO}>
     */
    List<GoshShopWithdrawDO> getListByMinMaxId(Long mindId, Long maxId, Date startTime, Date endTime);

    /**
     * 账户提现管理列表
     *
     * @param pageDTO
     * @return
     */
    Page<PageWithdrawResultDTO> pageWithdrawList(PageParam<PageWithdrawListDTO> pageDTO);

    /**
     * 账户提现统计
     *
     * @param withdrawListDTO
     * @return
     */
    WithdrawStatisticsDTO withdrawStatistics(PageWithdrawListDTO withdrawListDTO);

    /**
     * 账户提现管理列表(流式)
     *
     * @param param
     * @param resultHandler
     */
    void exportOperationWithdrawList(PageWithdrawListDTO param, ResultHandler<PageWithdrawResultDTO> resultHandler);

    /**
     * 更新提现单不能重提
     *
     * @param orderNo
     * @return
     */
    boolean updateNoRewithdraw(String orderNo);

    /**
     * 电子回单更新
     *
     * @param withdrawNo
     * @param electronicReceiptUrl
     * @return
     */
    boolean updateElectronicReceiptUrl(String withdrawNo, String electronicReceiptUrl);

    /**
     * 更新结算账户信息
     *
     * @param withdrawNo
     * @param settleAccountNo
     * @param bankName
     * @param accountName
     * @return
     */
    boolean updatesAccountInfo(String withdrawNo, String settleAccountNo, String bankName, String accountName);

    /**
     * 查询没有银行卡账号的提现单
     *
     * @return
     */
    List<GoshShopWithdrawDO> findWithdrawByNoSettleAccountNo();

    /**
     * 根据转账单号查询提现单
     *
     * @param orderNo
     * @return
     */
    GoshShopWithdrawDO getByTransferNo(String orderNo);
}
