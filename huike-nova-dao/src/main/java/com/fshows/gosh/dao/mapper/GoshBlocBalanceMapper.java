package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.entity.GoshBlocBalanceDO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * <p>
 * 账户余额表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
public interface GoshBlocBalanceMapper extends BaseMapper<GoshBlocBalanceDO> {

    /**
     * 行锁
     *
     * @param balanceId
     * @return
     */
    GoshBlocBalanceDO getBalanceAccountForUpdate(@Param("balanceId") String balanceId);

    /**
     * 入金余额处理
     *
     * @param balanceId
     * @param totalBalance
     * @param privilegeCouponBalance
     * @param currentBalance
     * @return
     */
    boolean updateCurrentBalance(@Param("balanceId") String balanceId,
                                 @Param("totalBalance") BigDecimal totalBalance,
                                 @Param("privilegeCouponBalance") BigDecimal privilegeCouponBalance,
                                 @Param("currentBalance") BigDecimal currentBalance);

    /**
     * 转账余额处理
     *
     * @param balanceId
     * @param totalBalance
     * @param privilegeCouponBalance
     * @param transferBalance
     * @param transitBalance
     * @return
     */
    boolean updateTransferBalance(@Param("balanceId") String balanceId,
                                  @Param("totalBalance") BigDecimal totalBalance,
                                  @Param("privilegeCouponBalance") BigDecimal privilegeCouponBalance,
                                  @Param("transferBalance") BigDecimal transferBalance,
                                  @Param("transitBalance") BigDecimal transitBalance);

    /**
     * 分账余额处理
     *
     * @param balanceId
     * @param totalBalance
     * @param privilegeCouponBalance
     * @param transferBalance
     * @param transitBalance
     * @return
     */
    boolean updateTransferSplitBalance(@Param("balanceId") String balanceId,
                                       @Param("totalBalance") BigDecimal totalBalance,
                                       @Param("privilegeCouponBalance") BigDecimal privilegeCouponBalance,
                                       @Param("transferBalance") BigDecimal transferBalance,
                                       @Param("transitBalance") BigDecimal transitBalance);
}
