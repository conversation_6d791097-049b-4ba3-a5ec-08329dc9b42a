package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.GoshMinaAdvertDayRecordDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 来逛呗商家版小程序广告日汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface GoshMinaAdvertDayRecordDAO extends IService<GoshMinaAdvertDayRecordDO> {

    /**
     * 根据日期和广告Id查询广告曝光对象
     *
     * @param visitDay 日期
     * @param advertId 广告Id
     * @return 广告曝光对象
     */
    GoshMinaAdvertDayRecordDO findByDayAndAdvertId(Integer visitDay, String advertId);

    /**
     * @param visitDay 日期
     * @param type     类型 ：1-点击 2-曝光
     * @param advertId 广告id
     * @param uvValue  uv值
     */
    void modifyVisitData(Integer visitDay, Integer type, String advertId, Integer uvValue);
}
