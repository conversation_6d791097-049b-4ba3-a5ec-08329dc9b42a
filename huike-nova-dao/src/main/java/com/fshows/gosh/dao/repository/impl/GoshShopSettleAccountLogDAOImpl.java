package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.PageSettlementCardListDTO;
import com.fshows.gosh.dao.domain.param.QueryAccountPageParamDTO;
import com.fshows.gosh.dao.domain.result.PageSettlementCardResultDTO;
import com.fshows.gosh.dao.domain.result.QueryAccountPageResultDTO;
import com.fshows.gosh.dao.entity.GoshShopSettleAccountLogDO;
import com.fshows.gosh.dao.mapper.GoshShopSettleAccountLogMapper;
import com.fshows.gosh.dao.repository.GoshShopSettleAccountLogDAO;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.metadata.PageParam;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 商铺结算卡记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Service
public class GoshShopSettleAccountLogDAOImpl extends ServiceImpl<GoshShopSettleAccountLogMapper, GoshShopSettleAccountLogDO> implements GoshShopSettleAccountLogDAO {

    /**
     * 结算卡信息分页列表
     *
     * @param pageDTO
     * @return
     */
    @Override
    public Page<PageSettlementCardResultDTO> pageSettlementCardList(PageParam<PageSettlementCardListDTO> pageDTO) {
        Page<PageSettlementCardListDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());
        return getBaseMapper().pageSettlementCardList(page, pageDTO.getQuery());
    }

    /**
     * 更新结算卡信息
     *
     * @param shopId
     * @param rejectedReason
     * @return
     */
    @Override
    public boolean updateSettleAccountByShopId(String shopId, String rejectedReason) {
        return update()
                .set(GoshShopSettleAccountLogDO.CARD_STATUS, CommonConstant.INTEGER_TWO)
                .set(GoshShopSettleAccountLogDO.REJECTED_REASON, rejectedReason)
                .eq(GoshShopSettleAccountLogDO.SHOP_ID, shopId)
                .eq(GoshShopSettleAccountLogDO.CARD_STATUS, CommonConstant.INTEGER_ONE)
                .update();
    }

    /**
     * 根据shopId查询结算卡记录
     *
     * @param shopId
     * @return
     */
    @Override
    public List<GoshShopSettleAccountLogDO> findListByShopId(String shopId) {
        return query()
                .eq(GoshShopSettleAccountLogDO.SHOP_ID, shopId)
                .list();
    }

    /**
     * 根据SettleAccountType 查询结算卡信息
     *
     * @return
     */
    @Override
    public List<GoshShopSettleAccountLogDO> findListBySettleAccountType() {
        return query()
                .eq(GoshShopSettleAccountLogDO.SETTLE_ACCOUNT_TYPE, CommonConstant.INTEGER_TWO)
                .list();
    }

    /**
     * 更新结算卡信息
     *
     * @param id
     * @param companyName
     * @param licenseNo
     * @return
     */
    @Override
    public boolean updateLicenseInfo(Long id, String companyName, String licenseNo) {
        return update()
                .set(GoshShopSettleAccountLogDO.COMPANY_NAME, companyName)
                .set(GoshShopSettleAccountLogDO.LICENSE_NO, licenseNo)
                .eq(GoshShopSettleAccountLogDO.ID, id)
                .update();
    }

    /**
     * 查询绑定成功的结算卡信息
     *
     * @param shopId 门店Id
     * @return
     */
    @Override
    public GoshShopSettleAccountLogDO findBindSuccessSettleAccountLog(String shopId) {
        return query().eq(GoshShopSettleAccountLogDO.SHOP_ID, shopId)
                .eq(GoshShopSettleAccountLogDO.CARD_STATUS, CommonConstant.INTEGER_ONE)
                .eq(GoshShopSettleAccountLogDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1").one();
    }

    @Override
    public boolean updateSettleAccountLogStatus(String shopId, String rejectedReason) {
        return update()
                .eq(GoshShopSettleAccountLogDO.SHOP_ID, shopId)
                .eq(GoshShopSettleAccountLogDO.CARD_STATUS, CommonConstant.INTEGER_ONE)
                .set(GoshShopSettleAccountLogDO.CARD_STATUS, CommonConstant.INTEGER_TWO)
                .set(GoshShopSettleAccountLogDO.REJECTED_REASON, rejectedReason)
                .update();
    }

    /**
     * 查询账户信息列表
     *
     * @param pageParam
     * @return
     */
    @Override
    public Page<QueryAccountPageResultDTO> queryAccountPage(PageParam<QueryAccountPageParamDTO> pageParam) {
        Page<QueryAccountPageParamDTO> page = new Page<>();
        page.setCurrent(pageParam.getPage());
        page.setSize(pageParam.getPageSize());
        return getBaseMapper().queryAccountPage(page, pageParam.getQuery());
    }
}
