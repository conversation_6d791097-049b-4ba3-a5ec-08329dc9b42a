package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.PagePaymentListParamDTO;
import com.fshows.gosh.dao.domain.result.PagePaymentListResultDTO;
import com.fshows.gosh.dao.domain.result.SettleFormResultDTO;
import com.fshows.gosh.dao.entity.GoshSettleFormDO;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/**
 * <p>
 * 结算单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface GoshSettleFormMapper extends BaseMapper<GoshSettleFormDO> {

    List<SettleFormResultDTO> getSettleFormResultList(@Param("settleDate") String settleDate, @Param("relationBalanceId") String relationBalanceId);

    List<SettleFormResultDTO> getMergeTransferSuccessSettleFormResultList(@Param("settleDate") String settleDate, @Param("relationBalanceId") String relationBalanceId);


    /**
     * 分页查看出款对账列表
     *
     * @param page
     * @param query
     * @return
     */
    Page<PagePaymentListResultDTO> pagePaymentList(Page<PagePaymentListResultDTO> page, @Param("query") PagePaymentListParamDTO query);

    /**
     * 导出出款对账列表
     *
     * @param dto
     * @param resultHandler
     */
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = Integer.MIN_VALUE)
    void paymentListExport(@Param("query") PagePaymentListParamDTO dto, ResultHandler<PagePaymentListResultDTO> resultHandler);
}
