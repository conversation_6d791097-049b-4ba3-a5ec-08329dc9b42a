package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.entity.GoshMinaBlocBindGrantDO;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <p>
 * 商家版小程序-集团绑定权限 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface GoshMinaBlocBindGrantMapper extends BaseMapper<GoshMinaBlocBindGrantDO> {

    /**
     * 根据集团id查询集团小程序绑定权限
     *
     * @param blocId 集团id
     * @return 集团绑定权限
     */
    List<String> findBlocMinaGrantIdListByBlocId(@Param("blocId") String blocId);
}
