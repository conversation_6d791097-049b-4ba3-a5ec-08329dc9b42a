package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshRoleDO;
import com.fshows.gosh.dao.mapper.GoshRoleMapper;
import com.fshows.gosh.dao.repository.GoshRoleDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.enums.EmployeeAdminEnum;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.dao.domain.param.oem.FindRolePageDTO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 来逛呗角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Service
public class GoshRoleDAOImpl extends ServiceImpl<GoshRoleMapper, GoshRoleDO> implements GoshRoleDAO {

    /**
     * 根据角色id查询角色信息
     *
     * @param roleId
     * @return
     */
    @Override
    public GoshRoleDO getByRoleId(String roleId) {
        return query().eq(GoshRoleDO.ROLE_ID, roleId).eq(GoshRoleDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).last("limit 1").one();
    }

    /**
     * 分页查看角色列表
     *
     * @param param
     * @return
     */
    @Override
    public Page<GoshRoleDO> pageQueryRole(PageParam<FindRolePageDTO> param) {
        Page<GoshRoleDO> page = new Page<>();
        page.setCurrent(param.getPage());
        page.setSize(param.getPageSize());
        return getBaseMapper().pageQueryRole(page, param.getQuery());
    }

    /**
     * 新增角色
     *
     * @param roleDO
     * @return
     */
    @Override
    public boolean saveRole(GoshRoleDO roleDO) {
        return save(roleDO);
    }

    /**
     * 根据角色名称查询角色信息
     *
     * @param roleName
     * @param belongId
     * @param belongType
     * @return
     */
    @Override
    public GoshRoleDO getRoleByRoleName(String roleName, String belongId, Integer belongType) {
        return query()
                .eq(GoshRoleDO.ROLE_NAME, roleName)
                .eq(GoshRoleDO.BELONG_ID, belongId)
                .eq(GoshRoleDO.BELONG_TYPE, belongId)
                .last("limit 1")
                .one();
    }

    /**
     * 更新角色信息
     *
     * @param roleId
     * @param roleName
     * @param remark
     * @param isAllSelect
     * @return
     */
    @Override
    public boolean updateRole(String roleId, String roleName, String remark, Integer isAllSelect) {
        return update()
                .set(GoshRoleDO.ROLE_NAME, roleName)
                .set(GoshRoleDO.IS_ALL_SELECT, isAllSelect)
                .set(GoshRoleDO.REMARK, remark)
                .eq(GoshRoleDO.ROLE_ID, roleId)
                .update();
    }

    /**
     * 删除角色
     *
     * @param roleId
     * @return
     */
    @Override
    public boolean deleteRole(String roleId) {
        return update()
                .set(GoshRoleDO.IS_DEL, DelFlagEnum.DEL.getValue())
                .eq(GoshRoleDO.ROLE_ID, roleId)
                .update();
    }

    /**
     * 获取角色全部列表(管理员除外)
     *
     * @param belongType
     * @param belongId
     * @return
     */
    @Override
    public List<GoshRoleDO> queryAllRole(Integer belongType, String belongId) {
        return query()
                .eq(GoshRoleDO.BELONG_ID, belongId)
                .eq(GoshRoleDO.BELONG_TYPE, belongType)
                .eq(GoshRoleDO.IS_ADMIN, EmployeeAdminEnum.NOT_ADMIN.getValue())
                .list();
    }

    /**
     * 通过门店id查询收银员角色信息
     *
     * @param belongId
     * @param systemRoleType
     * @return
     */
    @Override
    public GoshRoleDO getCashierRoleByBelongId(String belongId, Integer systemRoleType) {
        return query()
                .eq(GoshRoleDO.BELONG_ID, belongId)
                .eq(GoshRoleDO.SYSTEM_ROLE_TYPE, systemRoleType)
                .last("limit 1")
                .one();
    }

    /**
     * 根据商铺获取管理员角色
     *
     * @param shopId
     * @return
     */
    @Override
    public GoshRoleDO getAdminRole(String shopId) {
        return query()
                .eq(GoshRoleDO.BELONG_ID, shopId)
                .eq(GoshRoleDO.IS_ADMIN, EmployeeAdminEnum.IS_ADMIN.getValue())
                .last("limit 1")
                .one();
    }
}
