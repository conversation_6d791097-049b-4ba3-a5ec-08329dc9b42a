package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshBlocRoleBindGrantDO;
import com.fshows.gosh.dao.mapper.GoshBlocRoleBindGrantMapper;
import com.fshows.gosh.dao.repository.GoshBlocRoleBindGrantDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 角色和权限关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class GoshBlocRoleBindGrantDAOImpl extends ServiceImpl<GoshBlocRoleBindGrantMapper, GoshBlocRoleBindGrantDO> implements GoshBlocRoleBindGrantDAO {

    /**
     * 根据角色id查询
     *
     * @param oldRoleId 角色id
     * @return 角色和权限关系
     */
    @Override
    public List<GoshBlocRoleBindGrantDO> findByRoleId(String oldRoleId) {
        return query()
                .eq(GoshBlocRoleBindGrantDO.ROLE_ID, oldRoleId)
                .eq(GoshBlocRoleBindGrantDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .list()
                ;
    }

    /**
     * 根据角色id删除
     *
     * @param roleId 角色id
     */
    @Override
    public void deleteByRoleId(String roleId) {
        update()
                .set(GoshBlocRoleBindGrantDO.IS_DEL, DelFlagEnum.DEL.getValue())
                .eq(GoshBlocRoleBindGrantDO.ROLE_ID, roleId)
                .eq(GoshBlocRoleBindGrantDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .update();
    }

    /**
     * 根据角色id查询权限id列表
     *
     * @param roleId 角色id
     * @return 权限id列表
     */
    @Override
    public List<String> findGrantIdListByRoleId(String roleId) {
        return getBaseMapper().findGrantIdListByRoleId(roleId);
    }
}
