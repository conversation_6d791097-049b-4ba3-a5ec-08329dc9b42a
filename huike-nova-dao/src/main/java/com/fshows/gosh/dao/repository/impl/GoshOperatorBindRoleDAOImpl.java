/**
 * <AUTHOR>
 * @date 2024/11/4 13:38
 * @version 1.0 GoshOperatorBindGrantDAOImpl
 */
package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.OperationAccountRoleListDTO;
import com.fshows.gosh.dao.entity.GoshOperatorBindRoleDO;
import com.fshows.gosh.dao.entity.GoshOperatorGrantInfoDO;
import com.fshows.gosh.dao.entity.GoshOperatorRoleInfoDO;
import com.fshows.gosh.dao.mapper.GoshOperatorBindRoleMapper;
import com.fshows.gosh.dao.repository.GoshOperatorBindRoleDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @version GoshOperatorBindGrantDAOImpl.java, v 0.1 2024-11-04 13:38 tuyuwei
 */
@Service
public class GoshOperatorBindRoleDAOImpl extends ServiceImpl<GoshOperatorBindRoleMapper, GoshOperatorBindRoleDO> implements GoshOperatorBindRoleDAO {


    @Override
    public List<GoshOperatorGrantInfoDO> getGrantByOperatorId(String operatorId) {
        return getBaseMapper().getGrantByOperatorId(operatorId);
    }

    @Override
    public List<OperationAccountRoleListDTO> getRoleInfo(List<String> operatorIds) {
        return getBaseMapper().getRoleInfo(operatorIds);
    }

    @Override
    public List<GoshOperatorBindRoleDO> getBindRoles() {
        return getBaseMapper().selectList(new LambdaQueryWrapper<GoshOperatorBindRoleDO>()
                .eq(GoshOperatorBindRoleDO::getIsDel, DelFlagEnum.NOT_DEL.getValue()));
    }

    @Override
    public List<GoshOperatorRoleInfoDO> getByOperatorId(String operatorId) {
        return getBaseMapper().getByOperatorId(operatorId);
    }

    @Override
    public List<GoshOperatorBindRoleDO> getBindRolesByRoleId(String roleId) {
        return getBaseMapper().selectList(new LambdaQueryWrapper<GoshOperatorBindRoleDO>().eq(GoshOperatorBindRoleDO::getRoleId, roleId));
    }

    @Override
    public void deleteBindByOperatorId(String operatorId) {
         getBaseMapper().update(null, new LambdaUpdateWrapper<GoshOperatorBindRoleDO>()
                .eq(GoshOperatorBindRoleDO::getOperatorId, operatorId)
                .set(GoshOperatorBindRoleDO::getIsDel, DelFlagEnum.DEL.getValue()));
    }

    @Override
    public void deleteBindByRoleId(String roleId) {
        getBaseMapper().update(null, new LambdaUpdateWrapper<GoshOperatorBindRoleDO>()
                .eq(GoshOperatorBindRoleDO::getRoleId, roleId)
                .set(GoshOperatorBindRoleDO::getIsDel, DelFlagEnum.DEL.getValue()));
    }


}
