package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshBlocOrganizePlatformPermissionsDO;
import com.fshows.gosh.dao.mapper.GoshBlocOrganizePlatformPermissionsMapper;
import com.fshows.gosh.dao.repository.GoshBlocOrganizePlatformPermissionsDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 组织平台权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Service
public class GoshBlocOrganizePlatformPermissionsDAOImpl extends ServiceImpl<GoshBlocOrganizePlatformPermissionsMapper, GoshBlocOrganizePlatformPermissionsDO> implements GoshBlocOrganizePlatformPermissionsDAO {

    /**
     * 根据组织id查询组织平台权限
     *
     * @param orgId 组织id
     * @return 组织平台权限
     */
    @Override
    public List<GoshBlocOrganizePlatformPermissionsDO> findByOrgId(String orgId) {
        return query()
                .eq(GoshBlocOrganizePlatformPermissionsDO.ORG_ID, orgId)
                .eq(GoshBlocOrganizePlatformPermissionsDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .list();
    }

    /**
     * 根据组织id列表查询组织平台权限
     *
     * @param orgIdList 组织id列表
     * @return 组织平台权限
     */
    @Override
    public List<GoshBlocOrganizePlatformPermissionsDO> findByOrgIdList(List<String> orgIdList) {
        return query().in(GoshBlocOrganizePlatformPermissionsDO.ORG_ID, orgIdList).eq(GoshBlocOrganizePlatformPermissionsDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }

    /**
     * 根据组织id列表删除组织平台权限
     *
     * @param orgIdList 组织id列表
     */
    @Override
    public void deleteByOrgIdList(List<String> orgIdList) {
        update().set(GoshBlocOrganizePlatformPermissionsDO.IS_DEL, DelFlagEnum.DEL.getValue()).in(GoshBlocOrganizePlatformPermissionsDO.ORG_ID, orgIdList).eq(GoshBlocOrganizePlatformPermissionsDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).update();
    }
}
