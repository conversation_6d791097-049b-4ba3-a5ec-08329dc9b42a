package com.fshows.gosh.dao.mapper;

import com.fshows.gosh.dao.domain.result.OrderDataSummaryResultDTO;
import com.fshows.gosh.dao.entity.AilikeGroupPoiDaySummaryDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huike.nova.dao.domain.param.OrderDataSummaryParamDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 集团交易账单日汇总表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-06
 */
public interface AilikeGroupPoiDaySummaryMapper extends BaseMapper<AilikeGroupPoiDaySummaryDO> {

    /**
     * 历史数据订单概览
     *
     * @param dto 参数
     * @return 订单概览结果
     */
    OrderDataSummaryResultDTO orderHistoryDataSummary(@Param("dto") OrderDataSummaryParamDTO dto);

}
