package com.fshows.gosh.dao.mapper;

import com.fshows.gosh.dao.entity.GoshVerifySettleDetailDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 核销结算明细数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface GoshVerifySettleDetailMapper extends BaseMapper<GoshVerifySettleDetailDO> {
    /**
     * 根据结算单号查询补单明细
     *
     * @param serialNo 结算单号
     * @param dataId   数据Id
     * @return
     */
    List<GoshVerifySettleDetailDO> getListBySerialNo(@Param("serialNo") String serialNo, @Param("dataId") long dataId);
}
