package com.fshows.gosh.dao.domain.result.refund;

import lombok.Data;

import java.math.BigDecimal;

/**
 * PageRefundResultDTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/9
 */
@Data
public class PageRefundResultDTO {

    /**
     * 售后id
     */
    private String afterSaleId;

    /**
     * 集团id
     */
    private String appId;

    /**
     * 集团名称
     */
    private String orgName;

    /**
     * 核销广场Id
     */
    private String poiId;

    /**
     * 核销广场名称
     */
    private String poiName;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 商圈卡id
     */
    private String productId;

    /**
     * 商圈卡名称
     */
    private String productName;

    /**
     * 商圈卡券码
     */
    private String couponCode;

    /**
     * 退款数量
     */
    private Integer refundCount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 商家券状态:1-已使用;2-未使用
     */
    private Integer merchantCouponStatus;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 券状态
     */
    private Integer couponStatus;

    /**
     * 顾客手机号
     */
    private String customerPhone;

    /**
     * 退款申请时间
     */
    private String refundApplyTime;

    /**
     * 审核时间
     */
    private String auditTime;

    /**
     * 审核链接
     */
    private String auditLink;

    /**
     * 退款操作人
     */
    private String refundName;

    /**
     * 退款操作时间
     */
    private String refundTime;
}