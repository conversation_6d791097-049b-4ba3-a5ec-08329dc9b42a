package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshBlocBalanceDO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 账户余额表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
public interface GoshBlocBalanceDAO extends IService<GoshBlocBalanceDO> {

    /**
     * 根据balance_id查询数据
     *
     * @param balanceId
     * @return
     */
    GoshBlocBalanceDO getByBalanceId(String balanceId);

    /**
     * 根据集团id和平台类型查询数据
     *
     * @param blocId
     * @param platformType
     * @return
     */
    GoshBlocBalanceDO getByBlocIdAndPlatformType(String blocId, String platformType);

    /**
     * 余额表添加行锁
     *
     * @param balanceId
     * @return
     */
    GoshBlocBalanceDO getBalanceAccountForUpdate(String balanceId);

    /**
     * 入金余额处理
     *
     * @param balanceId
     * @param totalBalance
     * @param privilegeCouponBalance
     * @param currentBalance
     * @return
     */
    boolean updateCurrentBalance(String balanceId,
                                 BigDecimal totalBalance,
                                 BigDecimal privilegeCouponBalance,
                                 BigDecimal currentBalance);

    /**
     * 转账余额处理
     *
     * @param balanceId
     * @param totalBalance
     * @param privilegeCouponBalance
     * @param transferBalance
     * @param transitBalance
     * @return
     */
    boolean updateTransferBalance(String balanceId,
                                  BigDecimal totalBalance,
                                  BigDecimal privilegeCouponBalance,
                                  BigDecimal transferBalance,
                                  BigDecimal transitBalance);

    /**
     * 分账余额处理
     *
     * @param balanceId
     * @param totalBalance
     * @param privilegeCouponBalance
     * @param transferBalance
     * @param transitBalance
     * @return
     */
    boolean updateTransferSplitBalance(String balanceId,
                                       BigDecimal totalBalance,
                                       BigDecimal privilegeCouponBalance,
                                       BigDecimal transferBalance,
                                       BigDecimal transitBalance);

    /**
     * 根据集团id查询数据
     *
     * @param blocId 集团 id
     * @return List<GoshBlocBalanceDO>
     */
    List<GoshBlocBalanceDO> findByBlocId(String blocId);
}
