package com.fshows.gosh.dao.mapper;

import com.fshows.gosh.dao.entity.GoshBlocRoleBindGrantDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色和权限关系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshBlocRoleBindGrantMapper extends BaseMapper<GoshBlocRoleBindGrantDO> {

    /**
     * 根据角色id查询权限id列表
     * @param roleId 角色id
     * @return
     */
    List<String> findGrantIdListByRoleId(@Param("roleId") String roleId);
}
