package com.fshows.gosh.dao.mapper;

import com.fshows.gosh.dao.entity.GoshMinaAdvertDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 来逛呗商家版小程序广告 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface GoshMinaAdvertMapper extends BaseMapper<GoshMinaAdvertDO> {
    /**
     * 查找广告列表
     *
     * @param advertLocation 广告位置
     * @return return
     */
    List<GoshMinaAdvertDO> findAdvertList(@Param("advertLocation") String advertLocation);
}
