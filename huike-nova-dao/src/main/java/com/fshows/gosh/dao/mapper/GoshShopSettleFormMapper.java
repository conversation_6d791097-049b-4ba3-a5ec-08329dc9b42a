package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.entity.GoshShopSettleFormDO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 商铺结算单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface GoshShopSettleFormMapper extends BaseMapper<GoshShopSettleFormDO> {

    /**
     * 更新状态和原因
     * @param shopSerialNo
     * @param settleFormStatus
     * @return boolean
     */
    boolean updateStatusAndReason(@Param("shopSerialNo")String shopSerialNo,@Param("settleFormStatus") Integer settleFormStatus);
}
