package com.fshows.gosh.dao.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshShopSettleFormDO;
import com.fshows.gosh.dao.mapper.GoshShopSettleFormMapper;
import com.fshows.gosh.dao.repository.GoshShopSettleFormDAO;
import com.huike.nova.common.enums.acct.ShopSettleFormStatusEnum;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 商铺结算单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Service
public class GoshShopSettleFormDAOImpl extends ServiceImpl<GoshShopSettleFormMapper, GoshShopSettleFormDO> implements GoshShopSettleFormDAO {

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    @Override
    public boolean batchShopSettleForm(List<GoshShopSettleFormDO> list) {
        return saveBatch(list);
    }

    /**
     * 根据serialNo删除
     *
     * @param serialNo
     * @return
     */
    @Override
    public boolean removeBySerialNo(String serialNo) {
        return remove(new LambdaQueryWrapper<GoshShopSettleFormDO>()
                .eq(GoshShopSettleFormDO::getSerialNo, serialNo)
        );
    }

    @Override
    public List<GoshShopSettleFormDO> getListBySerialNo(String serialNo) {
        QueryChainWrapper<GoshShopSettleFormDO> eq = query().eq(GoshShopSettleFormDO.SERIAL_NO, serialNo);
        return eq.list();
    }

    @Override
    public GoshShopSettleFormDO getByShopSerialNo(String shopSerialNo) {
        return query()
                .eq(GoshShopSettleFormDO.SHOP_SERIAL_NO, shopSerialNo)
                .last("limit 1")
                .one();

    }

    @Override
    public boolean updateSettleFormStatusAndReason(String shopSerialNo, Integer settleFormStatus, String reason) {
        return update()
                .set(GoshShopSettleFormDO.SETTLE_FORM_STATUS, settleFormStatus)
                .set(StrUtil.isNotBlank(reason), GoshShopSettleFormDO.REJECTED_REASON, reason)
                .eq(GoshShopSettleFormDO.SHOP_SERIAL_NO, shopSerialNo)
                .update();
    }

    /**
     * 根据状态查询列表
     *
     * @param accountId
     * @param settleFormStatus
     * @return
     */
    @Override
    public List<GoshShopSettleFormDO> getDealListByAccountId(String accountId, Integer settleFormStatus) {
        QueryChainWrapper<GoshShopSettleFormDO> eq = query()
                .eq(GoshShopSettleFormDO.ACCOUNT_ID, accountId)
                .eq(ObjectUtil.isNotNull(settleFormStatus), GoshShopSettleFormDO.SETTLE_FORM_STATUS, settleFormStatus);
        return eq.list();
    }

    @Override
    public boolean updateStatusAndResetReason(String shopSerialNo, Integer settleFormStatus) {
        return baseMapper.updateStatusAndReason(shopSerialNo, settleFormStatus);
    }

    /**
     * 更新补单任务Id
     *
     * @param shopSerialNoList 门店结算单号
     * @param taskId           补单任务Id
     * @return
     */
    @Override
    public boolean updateTaskIdByShopSerialNoList(List<String> shopSerialNoList, String taskId) {
        return update()
                .set(GoshShopSettleFormDO.TASK_ID, taskId)
                .in(GoshShopSettleFormDO.SHOP_SERIAL_NO, shopSerialNoList)
                .update();
    }

    /**
     * 根据补单任务id查询门店结算数据
     *
     * @param taskId 补单任务id
     * @return
     */
    @Override
    public List<GoshShopSettleFormDO> getListByTaskId(String taskId) {
        return query().eq(GoshShopSettleFormDO.TASK_ID, taskId).list();
    }

    /**
     * 根据结算单号将失败的数据进行初始化
     *
     * @param serialNo                 结算单号
     * @param shopSettleFormStatusList 门店结算状态
     * @return
     */
    @Override
    public boolean updateInitBySerialNo(String serialNo, List<Integer> shopSettleFormStatusList) {
        return update().set(GoshShopSettleFormDO.SETTLE_FORM_STATUS, ShopSettleFormStatusEnum.INIT.getValue())
                .eq(GoshShopSettleFormDO.SERIAL_NO, serialNo)
                .in(GoshShopSettleFormDO.SETTLE_FORM_STATUS, shopSettleFormStatusList)
                .update();
    }

    /**
     * 根据门店结算单号数据进行初始化
     *
     * @param shopSerialNoList 门店结算单号
     * @return
     */
    @Override
    public boolean updateInitByShopSerialNoList(List<String> shopSerialNoList) {
        return update().set(GoshShopSettleFormDO.SETTLE_FORM_STATUS, ShopSettleFormStatusEnum.INIT.getValue())
                .in(GoshShopSettleFormDO.SHOP_SERIAL_NO, shopSerialNoList)
                .update();
    }

    /**
     * 根据门店结算单号查询列表数据
     *
     * @param shopSerialNoList 门店结算单号
     * @return
     */
    @Override
    public List<GoshShopSettleFormDO> getListByShopSerialNoList(List<String> shopSerialNoList) {
        return query().in(GoshShopSettleFormDO.SHOP_SERIAL_NO, shopSerialNoList).list();
    }

    /**
     * 查询处理中的门店结算数据
     *
     * @param shopId 门店Id
     * @return
     */
    @Override
    public List<GoshShopSettleFormDO> getProcessingListByShopId(String shopId) {
        return query().eq(GoshShopSettleFormDO.SHOP_ID, shopId)
                .in(GoshShopSettleFormDO.SETTLE_FORM_STATUS, Arrays.asList(ShopSettleFormStatusEnum.INIT.getValue(),
                        ShopSettleFormStatusEnum.PROCESSING.getValue(),
                        ShopSettleFormStatusEnum.FAIL.getValue())).list();
    }

    @Override
    public Integer failCountByShopId(String shopId) {
        return query().eq(GoshShopSettleFormDO.SHOP_ID, shopId)
                .ne(GoshShopSettleFormDO.SETTLE_FORM_STATUS, ShopSettleFormStatusEnum.SUCCESS.getValue())
                .count().intValue();
    }
}
