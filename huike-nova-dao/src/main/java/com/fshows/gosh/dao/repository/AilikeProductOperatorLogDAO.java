package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.AilikeProductOperatorLogDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 商品操作日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface AilikeProductOperatorLogDAO extends IService<AilikeProductOperatorLogDO> {

    /**
     * 查询商品的操作日志记录列表
     *
     * @param businessProductId
     * @return
     */
    List<AilikeProductOperatorLogDO> findProductOperatorLogList(String businessProductId);

    /**
     * 查询日志明细
     *
     * @param logId
     * @return
     */
    AilikeProductOperatorLogDO findProductOperatorLog(String logId);

}
