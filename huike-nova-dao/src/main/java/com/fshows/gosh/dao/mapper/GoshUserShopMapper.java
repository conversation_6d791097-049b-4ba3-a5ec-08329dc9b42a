package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.UserShopParamDTO;
import com.fshows.gosh.dao.entity.GoshUserShopDO;
import com.huike.nova.dao.domain.param.ConfirmShopParamDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 来逛呗用户商铺表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshUserShopMapper extends BaseMapper<GoshUserShopDO> {

    /**
     * 根据userId查询店铺列表
     *
     * @param userId
     * @return
     */
    List<UserShopParamDTO> findByUserId(String userId);

    /**
     * 查询用户未确认的商铺列表
     *
     * @param userId
     * @return
     */
    List<UserShopParamDTO> findUnconfirmedShopList(String userId);

    /**
     * 查询已确认商铺选择列表
     *
     * @param userId
     * @return
     */
    List<UserShopParamDTO> findConfirmShopList(String userId);

    /**
     * 查询已确认商铺选择列表
     *
     * @param page  分页参数
     * @param dto   业务参数
     * @return
     */
    Page<UserShopParamDTO> pageConfirmShopList(Page<UserShopParamDTO> page, @Param("dto") ConfirmShopParamDTO dto);

    /**
     * 获取选择商铺信息
     *
     * @param shopId
     * @param userId
     * @param confirmStatus
     * @return
     */
    UserShopParamDTO getChooseShopDetail(@Param("shopId") String shopId, @Param("userId") String userId, @Param("confirmStatus") Integer confirmStatus);

    /**
     * 门店列表查询
     *
     * @param userId
     * @param shopName
     * @param confirmStatus
     * @return
     */
    List<UserShopParamDTO> findShopList(@Param("userId") String userId, @Param("shopName") String shopName, @Param("confirmStatus") Integer confirmStatus);
}
