package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 账户余额表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Data
@TableName("gosh_bloc_balance")
public class GoshBlocBalanceDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 余额记录id
     */
    @TableField("balance_id")
    private String balanceId;

    /**
     * 平台类型 TIKTOK-抖音 ALIPAY-支付宝 MEITUAN-美团
     */
    @TableField("platform_type")
    private String platformType;

    public static final String BALANCE_TYPE = "balance_type";
    /**
     * 余额类型 BLOC-集团平台商，PLATFORM-统一平台商
     */
    @TableField("balance_type")
    private String balanceType;

    /**
     * 总余额
     */
    @TableField("total_balance")
    private BigDecimal totalBalance;

    /**
     * 可用余额
     */
    @TableField("current_balance")
    private BigDecimal currentBalance;

    /**
     * 转账在途余额
     */
    @TableField("transfer_transit_balance")
    private BigDecimal transferTransitBalance;

    /**
     * 商圈权益卡待结算金额
     */
    @TableField("privilege_coupon_balance")
    private BigDecimal privilegeCouponBalance;

    /**
     * 营销补差余额
     */
    @TableField("supplement_balance")
    private BigDecimal supplementBalance;

    /**
     * 冻结余额
     */
    @TableField("frozen_balance")
    private BigDecimal frozenBalance;

    public static final String ID = "id";

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
    public static final String BALANCE_ID = "balance_id";

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }
    public static final String PLATFORM_TYPE = "platform_type";
    /**
     * 关联用户id（PLATFORM类型为固定id）
     */
    @TableField("bloc_id")
    private String blocId;
    public static final String BLOC_ID = "bloc_id";
    public static final String TOTAL_BALANCE = "total_balance";
    public static final String CURRENT_BALANCE = "current_balance";
    public static final String TRANSFER_TRANSIT_BALANCE = "transfer_transit_balance";
    public static final String PRIVILEGE_COUPON_BALANCE = "privilege_coupon_balance";
    public static final String SUPPLEMENT_BALANCE = "supplement_balance";
    public static final String FROZEN_BALANCE = "frozen_balance";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    public static final String TRANSFER_SPLIT_TRANSIT_BALANCE = "transfer_split_transit_balance";
    /**
     * 是否删除: 0-未删除 1-已删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 分账在途金额
     */
    @TableField("transfer_split_transit_balance")
    private BigDecimal transferSplitTransitBalance;

}
