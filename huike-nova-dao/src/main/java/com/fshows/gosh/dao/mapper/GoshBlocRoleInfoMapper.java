package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.BlocRolePageListDTO;
import com.fshows.gosh.dao.domain.result.AccountRoleResultDTO;
import com.fshows.gosh.dao.domain.result.BlocRolePageListResultDTO;
import com.fshows.gosh.dao.entity.GoshBlocRoleInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshBlocRoleInfoMapper extends BaseMapper<GoshBlocRoleInfoDO> {

    /**
     * 角色列表
     *
     * @param page  分页
     * @param query 查询条件
     * @return 角色列表
     */
    Page<BlocRolePageListResultDTO> blocRolePageList(Page<BlocRolePageListDTO> page, @Param("query") BlocRolePageListDTO query);

    /**
     * 查询组织下所有可管理的角色
     *
     * @param orgPath 路径
     * @param orgId
     * @return 角色列表
     */
    List<GoshBlocRoleInfoDO> blocRoleListByOrgId(@Param("orgPath") String orgPath,@Param("orgId") String orgId);

    /**
     * 根据角色名称获取角色信息
     *
     * @param roleName 角色名称
     * @param orgId    组织id
     * @return 角色信息
     */
    GoshBlocRoleInfoDO getRoleByRoleName(@Param("roleName") String roleName, @Param("orgId") String orgId, @Param("roleId") String roleId);

    /**
     * 根据账号id列表获取角色信息
     *
     * @param accountIdList 角色id
     * @return 角色信息
     */
    List<AccountRoleResultDTO> blocRoleListByAccountIdList(@Param("accountIdList") List<String> accountIdList);

    /**
     * 根据账号id获取角色信息
     *
     * @param accountId 账号id
     * @return 角色信息
     */
    List<AccountRoleResultDTO> blocRoleListByAccountId(@Param("accountId") String accountId);
}
