package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshBlocBalanceChangeRecordDO;

/**
 * <p>
 * 账户余额变更记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
public interface GoshBlocBalanceChangeRecordDAO extends IService<GoshBlocBalanceChangeRecordDO> {

    /**
     * 查下余额变更记录是否存在
     *
     * @param changeSn
     * @param balanceId
     * @param changeType
     * @return
     */
    GoshBlocBalanceChangeRecordDO getByChangeSn(String changeSn, String balanceId, String changeType);
}
