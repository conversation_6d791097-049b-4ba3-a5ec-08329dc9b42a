package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.PageSettlementCardListDTO;
import com.fshows.gosh.dao.domain.param.QueryAccountPageParamDTO;
import com.fshows.gosh.dao.domain.result.PageSettlementCardResultDTO;
import com.fshows.gosh.dao.domain.result.QueryAccountPageResultDTO;
import com.fshows.gosh.dao.entity.GoshShopSettleAccountLogDO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 商铺结算卡记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface GoshShopSettleAccountLogMapper extends BaseMapper<GoshShopSettleAccountLogDO> {

    /**
     * 结算卡信息分页列表
     *
     * @param page
     * @param query
     * @return
     */
    Page<PageSettlementCardResultDTO> pageSettlementCardList(Page<PageSettlementCardListDTO> page, @Param("dto") PageSettlementCardListDTO query);

    /**
     * 结算卡信息分页列表
     *
     * @param page
     * @param query
     * @return
     */
    Page<QueryAccountPageResultDTO> queryAccountPage(Page<QueryAccountPageParamDTO> page, @Param("dto")QueryAccountPageParamDTO query);
}
