package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.entity.GoshRoleDO;
import com.huike.nova.dao.domain.param.oem.FindRolePageDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 来逛呗角色表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshRoleMapper extends BaseMapper<GoshRoleDO> {

    /**
     * 分页查看角色列表
     *
     * @param page
     * @param query
     * @return
     */
    Page<GoshRoleDO> pageQueryRole(Page<GoshRoleDO> page, @Param("query") FindRolePageDTO query);
}
