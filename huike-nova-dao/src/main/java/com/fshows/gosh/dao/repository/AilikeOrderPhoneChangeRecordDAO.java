package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.AilikeOrderPhoneChangeRecordDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 订单手机号变更记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface AilikeOrderPhoneChangeRecordDAO extends IService<AilikeOrderPhoneChangeRecordDO> {

    /**
     * 根据外部订单号查询手机号变更记录
     *
     * @param outOrderSn 外部订单号
     * @return 手机号变更记录列表
     */
    List<AilikeOrderPhoneChangeRecordDO> findByOutOrderSn(String outOrderSn);
}
