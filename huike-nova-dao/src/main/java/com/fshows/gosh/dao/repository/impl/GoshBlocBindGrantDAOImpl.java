package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshBlocBindGrantDO;
import com.fshows.gosh.dao.mapper.GoshBlocBindGrantMapper;
import com.fshows.gosh.dao.repository.GoshBlocBindGrantDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 集团权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@Service
public class GoshBlocBindGrantDAOImpl extends ServiceImpl<GoshBlocBindGrantMapper, GoshBlocBindGrantDO> implements GoshBlocBindGrantDAO {

    /**
     * 根据集团id查询集团权限id集合
     *
     * @param blocId 集团id
     * @return 集团权限id集合
     */
    @Override
    public List<String> findBlocGrantIdListByBlocId(String blocId) {
        return getBaseMapper().findBlocGrantIdListByBlocId(blocId);
    }

    /**
     * 根据集团 id 删除集团权限
     *
     * @param blocId 集团 id
     */
    @Override
    public void deleteByBlocId(String blocId) {
        update().set(GoshBlocBindGrantDO.IS_DEL, DelFlagEnum.DEL.getValue()).eq(GoshBlocBindGrantDO.BLOC_ID, blocId).update();
    }
}
