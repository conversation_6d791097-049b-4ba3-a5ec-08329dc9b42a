package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.SearchOperationAccountParamDTO;
import com.fshows.gosh.dao.domain.param.SearchOperationRoleParamDTO;
import com.fshows.gosh.dao.domain.result.SearchOperationAccountResultDTO;
import com.fshows.gosh.dao.domain.result.SearchOperationRoleResultDTO;
import com.fshows.gosh.dao.entity.GoshOperatorRoleInfoDO;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0 GoshOperationRoleInfoMapper
 * @date 2024/11/13 19:12
 */
public interface GoshOperationRoleInfoMapper extends BaseMapper<GoshOperatorRoleInfoDO> {



    /**
     * 查询角色列表（分页）
     *
     * @param page
     * @param query
     * @return
     */
    Page<SearchOperationRoleResultDTO> searchRoleList(Page<SearchOperationRoleParamDTO> page, @Param("query") SearchOperationRoleParamDTO query);

}
