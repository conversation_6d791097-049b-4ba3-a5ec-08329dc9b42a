/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.dao.domain.result.settleday;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version QueryDailySettlementListResultDTO.java, v 0.1 2024-07-18 4:35 PM ruanzy
 */
@Data
public class QueryDailySettlementListResultDTO {

    /**
     * 结算金额
     */
    private BigDecimal settledAmount;

    /**
     * 预计结算金额
     */
    private BigDecimal preSettledAmount;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 券售卖金额
     */
    private BigDecimal couponSaleAmount;

    /**
     * 核销日期
     */
    private Integer verifyDay;

    /**
     * 结算状态 1：待结算 2：结算中 3：已结算
     */
    private Integer settledStatus;

    /**
     * 平台
     */
    private String channelType;

    /**
     * 商品类型:1-团购券;11-代金券;111-商圈卡
     */
    private Integer productType;

    /**
     * 月核销日期
     */
    private String monthVerifyDay;
}