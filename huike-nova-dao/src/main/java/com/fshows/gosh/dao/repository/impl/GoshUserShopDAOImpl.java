package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.UserShopParamDTO;
import com.fshows.gosh.dao.entity.GoshUserShopDO;
import com.fshows.gosh.dao.mapper.GoshUserShopMapper;
import com.fshows.gosh.dao.repository.GoshUserShopDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.dao.domain.param.ConfirmShopParamDTO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 来逛呗用户商铺表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Service
public class GoshUserShopDAOImpl extends ServiceImpl<GoshUserShopMapper, GoshUserShopDO> implements GoshUserShopDAO {

    /**
     * 根据userId查询店铺列表
     *
     * @param userId
     * @return
     */
    @Override
    public List<UserShopParamDTO> findByUserId(String userId) {
        return getBaseMapper().findByUserId(userId);
    }

    /**
     * 查询用户未确认的商铺列表
     *
     * @param userId
     * @return
     */
    @Override
    public List<UserShopParamDTO> findUnconfirmedShopList(String userId) {
        return getBaseMapper().findUnconfirmedShopList(userId);
    }

    /**
     * 查询已确认商铺选择列表
     *
     * @param userId
     * @return
     */
    @Override
    public List<UserShopParamDTO> findConfirmShopList(String userId) {
        return getBaseMapper().findConfirmShopList(userId);
    }

    /**
     * 分页查询已确认商铺选择列表
     *
     * @param pageParam 参数
     * @return return
     */
    @Override
    public Page<UserShopParamDTO> pageConfirmShopList(PageParam<ConfirmShopParamDTO> pageParam) {
        Page<UserShopParamDTO> page = new Page<>();
        page.setSize(pageParam.getPageSize());
        page.setCurrent(pageParam.getPage());
        return getBaseMapper().pageConfirmShopList(page, pageParam.getQuery());
    }

    /**
     * 获取选择商铺信息
     *
     * @param shopId
     * @param userId
     * @param confirmStatus
     * @return
     */
    @Override
    public UserShopParamDTO getChooseShopDetail(String shopId, String userId, Integer confirmStatus) {
        return getBaseMapper().getChooseShopDetail(shopId, userId, confirmStatus);
    }

    /**
     * 门店列表查询
     *
     * @param userId
     * @param shopName
     * @param confirmStatus
     * @return
     */
    @Override
    public List<UserShopParamDTO> findShopList(String userId, String shopName, Integer confirmStatus) {
        return getBaseMapper().findShopList(userId, shopName, confirmStatus);
    }

    /**
     * 根据商铺id和用户id查询商铺信息
     *
     * @param shopId
     * @param userId
     * @return
     */
    @Override
    public GoshUserShopDO getByShopIdAndUserId(String shopId, String userId) {
        return query().eq(GoshUserShopDO.SHOP_ID, shopId).eq(GoshUserShopDO.USER_ID, userId).eq(GoshUserShopDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).last("limit 1").one();
    }

    /**
     * 根据角色id查询关联信息
     *
     * @param roleId
     * @return
     */
    @Override
    public List<GoshUserShopDO> findRoleByRoleId(String roleId) {
        return query()
                .eq(GoshUserShopDO.ROLE_ID, roleId)
                .list();
    }

    /**
     * 保存关联信息
     *
     * @param goshUserShopDO
     * @return
     */
    @Override
    public boolean saveUserShop(GoshUserShopDO goshUserShopDO) {
        return save(goshUserShopDO);
    }

    /**
     * 更新关联信息
     *
     * @param identityId
     * @param name
     * @param roleId
     * @return
     */
    @Override
    public boolean updateAccount(String identityId, String name, String roleId) {
        return update()
                .set(GoshUserShopDO.NAME, name)
                .set(GoshUserShopDO.ROLE_ID, roleId)
                .eq(GoshUserShopDO.IDENTITY_ID, identityId)
                .update();
    }

    /**
     * 删除关联信息
     *
     * @param identityId
     * @return
     */
    @Override
    public boolean deleteAccount(String identityId) {
        return update()
                .set(GoshUserShopDO.IS_DEL, DelFlagEnum.DEL.getValue())
                .eq(GoshUserShopDO.IDENTITY_ID, identityId)
                .update();
    }

    /**
     * 根据身份id查询关联信息
     *
     * @param identityId
     * @return
     */
    @Override
    public GoshUserShopDO getUserShopByIdentityId(String identityId) {
        return query()
                .eq(GoshUserShopDO.IDENTITY_ID, identityId)
                .last("limit 1")
                .one();
    }

    /**
     * 查询商铺管理员身份信息
     *
     * @param shopId 商铺id
     * @return
     */
    @Override
    public GoshUserShopDO getAdminByShopId(String shopId) {
        return query().eq(GoshUserShopDO.SHOP_ID, shopId).eq(GoshUserShopDO.IS_ADMIN, 1).eq(GoshUserShopDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).last("limit 1").one();
    }
}
