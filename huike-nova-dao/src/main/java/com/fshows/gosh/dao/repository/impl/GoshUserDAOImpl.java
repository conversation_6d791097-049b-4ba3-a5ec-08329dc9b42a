package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.PageAccountListParamDTO;
import com.fshows.gosh.dao.domain.result.AccountDetailResultDTO;
import com.fshows.gosh.dao.domain.result.PageAccountListResultDTO;
import com.fshows.gosh.dao.entity.GoshUserDO;
import com.fshows.gosh.dao.mapper.GoshUserMapper;
import com.fshows.gosh.dao.repository.GoshUserDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.dao.handler.FieldEncryptUtil;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 来逛呗-商家小程序用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Service
public class GoshUserDAOImpl extends ServiceImpl<GoshUserMapper, GoshUserDO> implements GoshUserDAO {

    /**
     * 根据手机号查询用户信息
     *
     * @param phone 手机号(加密前)
     * @return
     */
    @Override
    public GoshUserDO checkPhone(String phone) {
        return query()
                .eq(GoshUserDO.PHONE_NUMBER, FieldEncryptUtil.encode(phone))
                .last("limit 1")
                .one();
    }

    /**
     * 新增用户
     *
     * @param goshUserDO
     * @return
     */
    @Override
    public boolean saveUser(GoshUserDO goshUserDO) {
        return save(goshUserDO);
    }

    /**
     * 根据手机号查询用户信息
     *
     * @param phoneEncode 手机号(加密后)
     * @return
     */
    @Override
    public GoshUserDO getByPhoneEncode(String phoneEncode) {
        return query()
                .eq(GoshUserDO.PHONE_NUMBER, phoneEncode)
                .eq(GoshUserDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 分页查询员工列表
     *
     * @param pageParam
     * @return
     */
    @Override
    public Page<PageAccountListResultDTO> pageAccountList(PageParam<PageAccountListParamDTO> pageParam) {
        Page<PageAccountListResultDTO> page = new Page<>();
        page.setCurrent(pageParam.getPage());
        page.setSize(pageParam.getPageSize());
        return getBaseMapper().pageAccountList(page, pageParam.getQuery());
    }

    /**
     * 查询门店下的手机号数量
     *
     * @param phoneNumber
     * @param shopId
     * @return
     */
    @Override
    public Integer checkPhoneByShopId(String phoneNumber, String shopId) {
        return getBaseMapper().checkPhoneByShopId(FieldEncryptUtil.encode(phoneNumber), shopId);
    }

    /**
     * 根据身份id查询员工信息
     *
     * @param identityId
     * @param shopId
     * @return
     */
    @Override
    public AccountDetailResultDTO getAccountDetail(String identityId, String shopId) {
        return getBaseMapper().getAccountDetail(identityId, shopId);
    }

    /**
     * 根据userId查询用户信息
     *
     * @param userId
     * @return
     */
    @Override
    public GoshUserDO getByUserId(String userId) {
        return query()
                .eq(GoshUserDO.USER_ID, userId)
                .last("limit 1")
                .one();
    }
}
