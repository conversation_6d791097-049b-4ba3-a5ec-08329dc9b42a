package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 余额钱包关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
@TableName("gosh_wallet_correlation")
public class GoshWalletCorrelationDO implements Serializable {

    public static final String ID = "id";
    public static final String BALANCE_ID = "balance_id";
    public static final String PLATFORM_WALLET_ID = "platform_wallet_id";
    public static final String CORRELATION_TYPE = "correlation_type";
    public static final String PLATFORM_TYPE = "platform_type";
    public static final String RELATION_BALANCE_ID = "relation_balance_id";
    public static final String REMARK = "remark";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 余额id
     */
    @TableField("balance_id")
    private String balanceId;
    /**
     * 平台钱包id
     */
    @TableField("platform_wallet_id")
    private String platformWalletId;
    /**
     * 关联类型 1-单集团平台钱包 2-统一佣金账户 3-多集团平台钱包
     */
    @TableField("correlation_type")
    private Integer correlationType;
    /**
     * 平台类型 TIKTOK-抖音 ALIPAY-支付宝 MEITUAN-美团
     */
    @TableField("platform_type")
    private String platformType;
    /**
     * 关联余额ID（钱包类型为2或3时存在）
     */
    @TableField("relation_balance_id")
    private String relationBalanceId;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
    /**
     * 是否删除: 0-未删除 1-已删除
     */
    @TableField("is_del")
    private Integer isDel;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

}
