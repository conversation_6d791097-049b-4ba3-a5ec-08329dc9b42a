package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.param.SearchOperationAccountParamDTO;
import com.fshows.gosh.dao.domain.result.SearchOperationAccountResultDTO;
import com.fshows.gosh.dao.entity.GoshOperatorAccountDO;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;

import java.util.List;

/**
 * <p>
 * 来逛呗运营后台账号 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
public interface GoshOperatorAccountDAO extends IService<GoshOperatorAccountDO> {

    /**
     * 获取运营账号信息
     *
     * @param account
     * @return
     */
    GoshOperatorAccountDO getOperatorAccount(String account);

    /**
     * 更新账号最后登录时间
     *
     * @param operatorId
     * @param lastLoginTime
     * @return
     */
    boolean updateLastLoginTime(String operatorId, Integer lastLoginTime);


    /**
     * 根据账号名称查询用户
     *
     * @param account 账号名称
     * @return 存在返回操作者对象，不存在返回NULL
     */
    GoshOperatorAccountDO queryByAccount(String account);

    /**
     * 查询运营账号
     * @param pageDTO
     * @return
     */
    Page<SearchOperationAccountResultDTO> queryAllAccount(PageParam<SearchOperationAccountParamDTO> pageDTO);


    /**
     * 修改运营账号状态
     * @param status
     * @param operatorId
     */
    void updateAccountStatus(Integer status, String operatorId);

    /**
     * 删除运营账号
     * @param operatorId
     */
    void deleteAccount(String operatorId);


    /**
     * 通过运营Id查询运营账号
     * @param operatorId
     * @return
     */
    GoshOperatorAccountDO searchByOperatorId(String operatorId);

    /**
     * 通过运营Id查询运营账号(包括已删除)
     *
     * @param operatorId
     * @return
     */
    GoshOperatorAccountDO searchOperatorWithoutDel(String operatorId);
}
