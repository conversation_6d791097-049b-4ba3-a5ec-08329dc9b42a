package com.fshows.gosh.dao.mapper;

import com.fshows.gosh.dao.domain.param.OrderDataSummaryPoiProductParamDTO;
import com.fshows.gosh.dao.entity.AilikeTiktokLifeProductPoiDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 抖音来客商品适用门店列表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
public interface AilikeTiktokLifeProductPoiMapper extends BaseMapper<AilikeTiktokLifeProductPoiDO> {

    /**
     * 获得POI Id和商品Id获得符合条件的业务商品Id
     *
     * @param businessProductIdList 商品id
     * @param poiIdList poi列表
     * @return 符合条件的商品商品Id
     */
    List<String> getBusinessProductIdByPoiId(@Param("dto") OrderDataSummaryPoiProductParamDTO dto);
}
