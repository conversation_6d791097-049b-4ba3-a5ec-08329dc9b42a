package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshMerchantCouponConfirmRecordDO;

/**
 * <p>
 * 商家券（子券）货盘确认记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
public interface GoshMerchantCouponConfirmRecordDAO extends IService<GoshMerchantCouponConfirmRecordDO> {

    /**
     * 根据商铺id获取最新未确认记录
     *
     * @param shopId 门店id
     * @return 未确认记录
     */
    GoshMerchantCouponConfirmRecordDO getTheLatestNotConfirmRecordByShopId(String shopId);

    /**
     * 根据商铺id获取最新确认记录
     *
     * @param shopId 门店id
     * @return 未确认记录
     */
    GoshMerchantCouponConfirmRecordDO getTheLatestConfirmRecordByShopId(String shopId);

    /**
     * 根据商铺id获取最新弹窗记录
     *
     * @param shopId 门店id
     * @return 未弹窗记录
     */
    GoshMerchantCouponConfirmRecordDO getTheLatestNotPopUpRecordByShopId(String shopId);

    /**
     * 根据记录id修改记录弹窗状态
     *
     * @param recordId 记录id
     */
    void updatePopUpStatusByRecordId(String recordId);

    /**
     * 根据记录id修改记录确认状态
     *
     * @param recordId 记录id
     */
    void confirmRecordByRecordId(String recordId, Integer confirmStatus);

    /**
     * 根据母券的业务商品Id删除货盘确认记录
     *
     * @param parentBusinessProductId 母券的业务商品Id
     */
    void deleteByParentBusinessProductId(String parentBusinessProductId);

    /**
     * 根据业务商品Id删除货盘确认记录
     *
     * @param businessProductId 业务商品Id
     */
    void deleteByBusinessProductId(String businessProductId);
}
