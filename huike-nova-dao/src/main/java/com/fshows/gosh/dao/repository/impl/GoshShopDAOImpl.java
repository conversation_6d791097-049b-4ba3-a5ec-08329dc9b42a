package com.fshows.gosh.dao.repository.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.OrgShopPageListDTO;
import com.fshows.gosh.dao.domain.param.PageShopAccountListDTO;
import com.fshows.gosh.dao.domain.param.ShopPageListDTO;
import com.fshows.gosh.dao.domain.result.OrgPoiStoreInfoResultDTO;
import com.fshows.gosh.dao.domain.result.OrgShopCountResultDTO;
import com.fshows.gosh.dao.domain.result.PageShopAccountResultDTO;
import com.fshows.gosh.dao.domain.result.ShopPageListResultDTO;
import com.fshows.gosh.dao.entity.GoshShopDO;
import com.fshows.gosh.dao.mapper.GoshShopMapper;
import com.fshows.gosh.dao.repository.GoshShopDAO;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.enums.gosh.ConfirmStatusEnum;
import com.huike.nova.common.metadata.PageParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 来逛呗-商铺表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Service
public class GoshShopDAOImpl extends ServiceImpl<GoshShopMapper, GoshShopDO> implements GoshShopDAO {

    /**
     * 根据orgIdList查询商铺数量
     *
     * @param orgIdList
     * @return
     */
    @Override
    public List<OrgShopCountResultDTO> countByOrgIdList(List<String> orgIdList) {
        return getBaseMapper().countByOrgIdList(orgIdList);
    }

    /**
     * 根据orgIdList查询商铺数量
     *
     * @param pageDTO 分页参数
     * @return 列表
     */
    @Override
    public Page<ShopPageListResultDTO> shopPageList(PageParam<ShopPageListDTO> pageDTO) {
        Page<ShopPageListDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());
        ShopPageListDTO organizePageListDTO = pageDTO.getQuery();
        return getBaseMapper().organizePageList(page, organizePageListDTO);
    }

    /**
     * 根据orgIdList查询商铺数量
     *
     * @param orgId         广场 id
     * @param searchContent 门店名称or门店id
     * @return 门店列表
     */
    @Override
    public List<GoshShopDO> findShopByOrgId(String orgId, String searchContent) {
        return getBaseMapper().findShopByOrgId(orgId, searchContent);
    }

    /**
     * 根据shopId查询商铺信息
     *
     * @param shopId
     * @return
     */
    @Override
    public GoshShopDO getByShopId(String shopId) {
        return query()
                .eq(GoshShopDO.SHOP_ID, shopId)
                .eq(GoshShopDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 更新确认状态
     *
     * @param shopId
     * @return
     */
    @Override
    public boolean updateConfirmStatus(String shopId) {
        return update()
                .set(GoshShopDO.CONFIRM_STATUS, ConfirmStatusEnum.CONFIRMED.getValue())
                .eq(GoshShopDO.SHOP_ID, shopId)
                .update();
    }

    /**
     * 更新开户审核状态
     *
     * @param shopId
     * @param applyStatus
     * @param reason
     * @return
     */
    @Override
    public boolean updateApplyStatus(String shopId, Integer applyStatus, String reason) {
        return update()
                .set(GoshShopDO.APPLY_STATUS, applyStatus)
                .set(StringUtils.isNotBlank(reason), GoshShopDO.REASON, reason)
                .eq(GoshShopDO.SHOP_ID, shopId)
                .eq(GoshShopDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .update();
    }

    /**
     * 更新自动提现状态
     *
     * @param shopId       门店id
     * @param autoWithdraw 自动提现状态
     * @return
     */
    @Override
    public boolean updateAutoWithdrawStatus(String shopId, Integer autoWithdraw) {
        return update()
                .set(GoshShopDO.AUTO_WITHDRAW, autoWithdraw)
                .eq(GoshShopDO.SHOP_ID, shopId)
                .eq(GoshShopDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .update();
    }

    /**
     * 查询广场下面的门店数据
     *
     * @param pageParam
     * @return
     */
    @Override
    public Page<ShopPageListResultDTO> findShopListByOrgId(PageParam<OrgShopPageListDTO> pageParam) {
        Page<OrgShopPageListDTO> page = new Page<>();
        page.setCurrent(pageParam.getPage());
        page.setSize(pageParam.getPageSize());
        return getBaseMapper().findShopListByOrgId(page, pageParam.getQuery());
    }

    /**
     * 查询集团广场的日汇总数据(流式查询)
     *
     * @param param
     * @param resultHandler
     * <AUTHOR>
     */
    @Override
    public void exportShopListByOrgId(OrgShopPageListDTO param, ResultHandler<ShopPageListResultDTO> resultHandler) {
        getBaseMapper().exportShopListByOrgId(param, resultHandler);
    }

    /**
     * 根据orgIdList查询商铺数量
     *
     * @param storeIdList@return
     */
    @Override
    public List<GoshShopDO> getListByStoreIdList(List<String> storeIdList) {
        return query().in(GoshShopDO.STORE_ID, storeIdList)
                .eq(GoshShopDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .list();
    }

    /**
     * 根据门店Id查询所属组织信息
     *
     * @param storeIdList
     * @return
     */
    @Override
    public List<OrgPoiStoreInfoResultDTO> findOrgStoreList(List<String> storeIdList) {
        return getBaseMapper().findOrgStoreList(storeIdList);
    }

    /**
     * 根据门店id查询数据
     *
     * @param storeId
     * @return
     */
    @Override
    public GoshShopDO getByStoreId(String storeId) {
        return query()
                .eq(GoshShopDO.STORE_ID, storeId)
                .last("limit 1")
                .one();
    }

    /**
     * 根据accountId查询数据
     *
     * @param accountId
     * @return
     */
    @Override
    public GoshShopDO getByAccountId(String accountId) {
        return query()
                .eq(GoshShopDO.ACCOUNT_ID, accountId)
                .eq(GoshShopDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 账户信息列表
     *
     * @param pageDTO
     * @return
     */
    @Override
    public Page<PageShopAccountResultDTO> pageShopAccountList(PageParam<PageShopAccountListDTO> pageDTO) {
        Page<PageShopAccountListDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());
        return getBaseMapper().pageShopAccountList(page, pageDTO.getQuery());
    }

    /**
     * 查询账户信息列表(流式)
     *
     * @param param
     * @param resultHandler
     */
    @DS(CommonConstant.READ_ONLY_DB_DATA_SOURCE)
    @Override
    public void exportOperationShopAccountList(PageShopAccountListDTO param, ResultHandler<PageShopAccountResultDTO> resultHandler) {
        getBaseMapper().pageShopAccountList(param, resultHandler);
    }

    /**
     * 根据审核状态查询商铺列表
     *
     * @param applyStatus
     * @return
     */
    @Override
    public List<GoshShopDO> findListByApplyStatus(Integer applyStatus) {
        return query()
                .eq(GoshShopDO.APPLY_STATUS, applyStatus)
                .list();
    }

    /**
     * 根据审核状态查询商铺列表
     *
     * @param shopIdList 商铺 id 列表
     * @return 商铺列表
     */
    @Override
    public List<GoshShopDO> findByShopIdList(List<String> shopIdList) {
        return query().in(GoshShopDO.SHOP_ID, shopIdList).eq(GoshShopDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }

    /**
     * 根据店铺id查询店铺信息
     *
     * @param storeIdList
     * @return
     */
    @Override
    public List<GoshShopDO> findShopListByStoreIdList(List<String> storeIdList) {
        return query()
                .in(GoshShopDO.STORE_ID, storeIdList)
                .list();
    }
}
