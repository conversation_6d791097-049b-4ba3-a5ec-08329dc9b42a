package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshBlocAccountBindOrganizeDO;

import java.util.List;

/**
 * <p>
 * 集团账号和组织关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshBlocAccountBindOrganizeDAO extends IService<GoshBlocAccountBindOrganizeDO> {

    /**
     * 根据账号id查询
     *
     * @param accountIdList
     * @return
     */
    List<GoshBlocAccountBindOrganizeDO> findByAccountIdList(List<String> accountIdList);

    /**
     * 根据账号id查询
     *
     * @param accountId
     * @return
     */
    List<GoshBlocAccountBindOrganizeDO> getByAccountId(String accountId);

    /**
     * 根据账号id查询关联组织id列表
     *
     * @param accountId 账号id
     * @return orgIdList
     */
    List<String> getOrgIdListByAccountId(String accountId);

    /**
     * 根据账号id删除
     *
     * @param accountId
     */
    void deleteByAccountId(String accountId);

    /**
     * 根据组织id删除
     *
     * @param orgId
     */
    void deleteByOrgId(String orgId);

    /**
     * 根据组织id查询
     *
     * @param orgId
     * @return
     */
    List<GoshBlocAccountBindOrganizeDO> getByOrgId(String orgId);
}
