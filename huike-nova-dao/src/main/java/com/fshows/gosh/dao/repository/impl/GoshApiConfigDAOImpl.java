package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshApiConfigDO;
import com.fshows.gosh.dao.mapper.GoshApiConfigMapper;
import com.fshows.gosh.dao.repository.GoshApiConfigDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 来逛呗-接口调用配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
@Service
public class GoshApiConfigDAOImpl extends ServiceImpl<GoshApiConfigMapper, GoshApiConfigDO> implements GoshApiConfigDAO {

    /**
     * 查询appid
     *
     * @param userId
     * @param userType
     * @return
     */
    @Override
    public GoshApiConfigDO getByUerId(String userId, String userType) {
        return query()
                .eq(GoshApiConfigDO.USER_ID, userId)
                .eq(GoshApiConfigDO.USER_TYPE, userType)
                .eq(GoshApiConfigDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .one();
    }
}
