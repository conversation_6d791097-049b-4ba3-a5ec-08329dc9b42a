package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshWalletCorrelationDO;
import com.fshows.gosh.dao.mapper.GoshWalletCorrelationMapper;
import com.fshows.gosh.dao.repository.GoshWalletCorrelationDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 余额钱包关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Service
public class GoshWalletCorrelationDAOImpl extends ServiceImpl<GoshWalletCorrelationMapper, GoshWalletCorrelationDO> implements GoshWalletCorrelationDAO {

    /**
     * 根据钱包id查询数据
     *
     * @param platformWalletId 平台钱包Id
     * @return data
     */
    @Override
    public GoshWalletCorrelationDO getByPlatformWalletId(String platformWalletId) {
        return query()
                .eq(GoshWalletCorrelationDO.PLATFORM_WALLET_ID, platformWalletId)
                .eq(GoshWalletCorrelationDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 根据balance_id查询关联钱包列表
     *
     * @param balanceId 钱包id
     * @return rt
     */
    @Override
    public List<GoshWalletCorrelationDO> selectByRelationBalanceId(String balanceId, Integer correlationType) {
        return query()
                .eq(GoshWalletCorrelationDO.RELATION_BALANCE_ID, balanceId)
                .eq(GoshWalletCorrelationDO.CORRELATION_TYPE, correlationType)
                .eq(GoshWalletCorrelationDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .list();
    }

    /**
     * 根据balance_id查询数据
     *
     * @param balanceId
     * @return
     */
    @Override
    public GoshWalletCorrelationDO getByBalanceId(String balanceId) {
        return query()
                .eq(GoshWalletCorrelationDO.BALANCE_ID, balanceId)
                .eq(GoshWalletCorrelationDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 根据correlationType查询关联钱包列表
     *
     * @param correlationType
     * @return
     */
    @Override
    public List<GoshWalletCorrelationDO> selectByCorrelationType(Integer correlationType) {
        return query()
                .eq(GoshWalletCorrelationDO.CORRELATION_TYPE, correlationType)
                .eq(GoshWalletCorrelationDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .list();
    }

    /**
     * 根据balance_id批量删除数据
     *
     * @param balanceIdList
     */
    @Override
    public void deleteByBalanceIdList(List<String> balanceIdList) {
        getBaseMapper().deleteByBalanceIdList(balanceIdList);
    }

    /**
     * 根据BalanceIdList查询
     *
     * @param balanceIdList 余额 id 列表
     */
    @Override
    public List<GoshWalletCorrelationDO> findByBalanceIdList(List<String> balanceIdList) {
        return query().in(GoshWalletCorrelationDO.BALANCE_ID, balanceIdList).list();
    }
}
