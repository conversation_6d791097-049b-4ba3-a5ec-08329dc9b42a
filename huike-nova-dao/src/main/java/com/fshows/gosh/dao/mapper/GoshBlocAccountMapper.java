package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.BlocAccountPageListDTO;
import com.fshows.gosh.dao.entity.GoshBlocAccountDO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 集团账号 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshBlocAccountMapper extends BaseMapper<GoshBlocAccountDO> {

    /**
     * 集团账号列表
     *
     * @param page  分页
     * @param query 查询条件
     * @return 分页结果
     */
    Page<GoshBlocAccountDO> blocAccountPageList(Page<BlocAccountPageListDTO> page, @Param("query") BlocAccountPageListDTO query);

    /**
     * 角色列表
     *
     * @param account 账号
     * @return 分页结果
     */
    GoshBlocAccountDO getIgnoreIsDelByAccount(@Param("account") String account);
}
