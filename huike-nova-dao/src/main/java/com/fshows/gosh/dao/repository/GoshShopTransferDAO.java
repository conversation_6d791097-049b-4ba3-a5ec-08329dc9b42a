package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.GoshShopTransferDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 商户转账表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
public interface GoshShopTransferDAO extends IService<GoshShopTransferDO> {


    /**
     * 更新转账单
     *
     * @param transferNo
     * @param transferStatus
     * @param reason
     * @param finishTime
     * @param platformTransferNo
     * @return boolean
     */
    boolean updateByTransferNo(String transferNo, String transferStatus, String reason, Date finishTime, String platformTransferNo);

    /**
     * 根据结算单号查询雨转款单
     *
     * @param shopSerialNo
     * @return {@link List}<{@link GoshShopTransferDO}>
     */
    List<GoshShopTransferDO> getListByShopSerialNo(String shopSerialNo);

    /**
     * 根据转账单查询
     *
     * @param transferNo
     * @return {@link GoshShopTransferDO}
     */
    GoshShopTransferDO getByTransferNo(String transferNo);


    /**
     * 根据结算单和转账状态查询
     *
     * @param serialNo
     * @param transferStatus
     * @return {@link GoshShopTransferDO}
     */
    GoshShopTransferDO getBySerialNoAndTransferStatus(String serialNo, String transferStatus);


    /**
     * 更新提现单号
     *
     * @param transferNo
     * @param withdrawNo
     * @return boolean
     */
    boolean updateWithdrawNoByTransferNo(String transferNo, String withdrawNo);


    /**
     * 根据状态和创建时间查询订单
     *
     * @param transferStatus
     * @param startTime
     * @param endTime
     * @return {@link List}<{@link GoshShopTransferDO}>
     */
    List<GoshShopTransferDO> getListByStatusAndTimeRange(String transferStatus, Date startTime, Date endTime);


    /**
     * 根据结算单号查询最新转账单
     *
     * @param shopSerialNo
     * @return {@link List}<{@link GoshShopTransferDO}>
     */
    GoshShopTransferDO getLastOneByShopSerialNo(String shopSerialNo);

    /**
     * 获取门店维度转账记录明细
     *
     * @param platformTransferNo 平台转账id
     * @return
     */
    List<GoshShopTransferDO> getListByPlatformTransferNo(String platformTransferNo);

}
