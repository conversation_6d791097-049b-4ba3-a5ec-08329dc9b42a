package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.entity.GoshGrantDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 来逛呗权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshGrantMapper extends BaseMapper<GoshGrantDO> {

    /**
     * 查询所有的权限
     *
     * @param belongType 归属类型 1-商家版小程序
     * @param blocId     集团id
     * @return 权限列表
     */
    List<GoshGrantDO> queryAll(@Param("belongType") Integer belongType, @Param("blocId") String blocId);
}
