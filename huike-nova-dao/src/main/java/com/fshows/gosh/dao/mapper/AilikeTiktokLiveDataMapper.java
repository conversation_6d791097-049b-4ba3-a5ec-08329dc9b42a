package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.LiveDataParamDTO;
import com.fshows.gosh.dao.domain.result.LiveDataInfoResultDTO;
import com.fshows.gosh.dao.domain.result.LiveDataSummaryResultDTO;
import com.fshows.gosh.dao.entity.AilikeTiktokLiveDataDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 抖音来客直播数据记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface AilikeTiktokLiveDataMapper extends BaseMapper<AilikeTiktokLiveDataDO> {

    /**
     * 直播数据统计
     *
     * @param dto
     * @return
     */
    LiveDataSummaryResultDTO liveDataSummary(@Param("dto") LiveDataParamDTO dto);

    /**
     * 分页查询直播数据
     *
     * @param page
     * @param dto
     * @return
     */
    Page<LiveDataInfoResultDTO> pageLiveData(Page<LiveDataInfoResultDTO> page, @Param("dto") LiveDataParamDTO dto);
}
