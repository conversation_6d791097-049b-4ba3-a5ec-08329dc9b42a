package com.fshows.gosh.dao.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.result.ProductCommissionLogInfoResultDTO;
import com.fshows.gosh.dao.entity.AilikeTiktokLifeProductCommissionLogDO;
import com.fshows.gosh.dao.mapper.AilikeTiktokLifeProductCommissionLogMapper;
import com.fshows.gosh.dao.repository.AilikeTiktokLifeProductCommissionLogDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.enums.DelStatusEnum;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抖音来客商品佣金变更记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Service
public class AilikeTiktokLifeProductCommissionLogDAOImpl extends ServiceImpl<AilikeTiktokLifeProductCommissionLogMapper, AilikeTiktokLifeProductCommissionLogDO> implements AilikeTiktokLifeProductCommissionLogDAO {

    /**
     * 保存商品佣金日志
     *
     * @param tiktokLifeProductCommissionLogDO
     * @return
     */
    @Override
    public boolean saveProductCommissionLog(AilikeTiktokLifeProductCommissionLogDO tiktokLifeProductCommissionLogDO) {
        return save(tiktokLifeProductCommissionLogDO);
    }

    @Override
    public AilikeTiktokLifeProductCommissionLogDO getByBusinessProductIdAndStatus(String businessProductId, Integer commissionStatus) {
        return query()
                .eq(AilikeTiktokLifeProductCommissionLogDO.BUSINESS_PRODUCT_ID, businessProductId)
                .eq(AilikeTiktokLifeProductCommissionLogDO.COMMISSION_STATUS, commissionStatus)
                .eq(AilikeTiktokLifeProductCommissionLogDO.DEL_FLAG, DelStatusEnum.NOT_DEL.getValue())
                .orderByDesc(AilikeTiktokLifeProductCommissionLogDO.CREATE_TIME)
                .last("limit 1")
                .one();
    }

    @Override
    public boolean updateCommissionStatus(Integer commissionStatus, String commissionLogId, Integer effectiveTime) {
        return update()
                .set(AilikeTiktokLifeProductCommissionLogDO.COMMISSION_STATUS, commissionStatus)
                .set(ObjectUtil.isNotNull(effectiveTime), AilikeTiktokLifeProductCommissionLogDO.EFFECTIVE_TIME, effectiveTime)
                .eq(AilikeTiktokLifeProductCommissionLogDO.COMMISSION_LOG_ID, commissionLogId)
                .eq(AilikeTiktokLifeProductCommissionLogDO.DEL_FLAG, DelStatusEnum.NOT_DEL.getValue())
                .update();
    }

    @Override
    public Page<ProductCommissionLogInfoResultDTO> pageListProductCommissionLog(Integer page, Integer pageSize, String businessProductId) {
        Page<ProductCommissionLogInfoResultDTO> pageDto = new Page<>();
        pageDto.setCurrent(page);
        pageDto.setSize(pageSize);
        return getBaseMapper().pageListProductCommissionLog(pageDto, businessProductId);
    }
}
