package com.fshows.gosh.dao.mapper;

import com.fshows.gosh.dao.domain.param.VerifyDataSummaryParamDTO;
import com.fshows.gosh.dao.domain.result.ChannelTypeVerifySummaryResultDTO;
import com.fshows.gosh.dao.domain.result.ProductTypeVerifySummaryResultDTO;
import com.fshows.gosh.dao.domain.result.VerifyDataSummaryResultDTO;
import com.fshows.gosh.dao.domain.result.VerifyDaySummaryResultDTO;
import com.fshows.gosh.dao.entity.GoshMerchantVerifyDayDataDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 来逛呗-商家核销数据汇总 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshMerchantVerifyDayDataMapper extends BaseMapper<GoshMerchantVerifyDayDataDO> {
    /**
     * 删除日汇总数据
     *
     * @param appId
     * @param verifyDay
     * @return
     */
    boolean deleteByAppIdAndDay(@Param("appId") String appId, @Param("verifyDay") Integer verifyDay);

    /**
     * 核销数据总汇总
     *
     * @param dto
     * @return
     */
    VerifyDataSummaryResultDTO verifyTotalSummary(@Param("dto") VerifyDataSummaryParamDTO dto);

    /**
     * 核销数据日汇总
     *
     * @param dto
     * @return
     */
    List<VerifyDaySummaryResultDTO> verifyDaySummary(@Param("dto") VerifyDataSummaryParamDTO dto);


    /**
     * 按通道统计核销汇总数据
     *
     * @param dto
     * @return
     */
    List<ChannelTypeVerifySummaryResultDTO> channelTypeVerifySummary(@Param("dto")VerifyDataSummaryParamDTO dto);

    /**
     * 按商品类型统计核销汇总数据
     *
     * @param dto
     * @return
     */
    List<ProductTypeVerifySummaryResultDTO> productTypeVerifySummary(@Param("dto")VerifyDataSummaryParamDTO dto);
}
