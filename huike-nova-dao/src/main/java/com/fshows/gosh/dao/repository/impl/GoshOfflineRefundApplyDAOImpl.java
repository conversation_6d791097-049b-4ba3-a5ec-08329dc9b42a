package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.refund.PageOfflinePaymentParamDTO;
import com.fshows.gosh.dao.domain.result.refund.PageOfflinePaymentResultDTO;
import com.fshows.gosh.dao.entity.GoshOfflineRefundApplyDO;
import com.fshows.gosh.dao.mapper.GoshOfflineRefundApplyMapper;
import com.fshows.gosh.dao.repository.GoshOfflineRefundApplyDAO;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.util.StringUtil;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 抖音线下退款申请 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Service
public class GoshOfflineRefundApplyDAOImpl extends ServiceImpl<GoshOfflineRefundApplyMapper, GoshOfflineRefundApplyDO> implements GoshOfflineRefundApplyDAO {

    /**
     * 查询出款单列表
     *
     * @param pageDTO 参数
     * @return 数据
     */
    @Override
    public Page<PageOfflinePaymentResultDTO> pageOfflinePaymentList(PageParam<PageOfflinePaymentParamDTO> pageDTO) {
        Page<PageOfflinePaymentResultDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());
        return getBaseMapper().pageOfflinePaymentList(page, pageDTO.getQuery());
    }

    /**
     * 导出线下退款申请数据
     *
     * @param dto           参数
     * @param resultHandler 数据
     */
    @Override
    public void exportOfflinePaymentData(PageOfflinePaymentParamDTO dto, ResultHandler<PageOfflinePaymentResultDTO> resultHandler) {
        getBaseMapper().pageOfflinePaymentList(dto, resultHandler);
    }

    /**
     * 查询线下打款明细
     *
     * @param paymentId 打款id
     * @return 数据
     */
    @Override
    public GoshOfflineRefundApplyDO findByPaymentId(String paymentId) {
        return query().eq(GoshOfflineRefundApplyDO.PAYMENT_ID, paymentId)
                .eq(GoshOfflineRefundApplyDO.IS_DEL, CommonConstant.NUMBER_ZERO)
                .last("limit 1").one();
    }

    /**
     * 查询线下打款明细列表
     *
     * @param paymentIdList 打款id列表
     * @return 列表
     */
    @Override
    public List<GoshOfflineRefundApplyDO> findByPaymentIdList(List<String> paymentIdList) {
        return query().in(GoshOfflineRefundApplyDO.PAYMENT_ID, paymentIdList)
                .eq(GoshOfflineRefundApplyDO.IS_DEL, CommonConstant.NUMBER_ZERO)
                .list();
    }

    /**
     * 查询订单和券码数量
     *
     * @param orderId    订单号
     * @param couponCode 券码
     * @return 数量
     */
    @Override
    public Integer countApplyCouponCode(String orderId, String couponCode, String paymentId) {
        return query().eq(GoshOfflineRefundApplyDO.ORDER_SN, orderId)
                .like(StringUtil.isNotEmpty(couponCode), GoshOfflineRefundApplyDO.COUPON_CODE, couponCode)
                .eq(GoshOfflineRefundApplyDO.IS_DEL, CommonConstant.NUMBER_ZERO)
                .ne(StringUtil.isNotEmpty(paymentId), GoshOfflineRefundApplyDO.PAYMENT_ID, paymentId)
                .count().intValue();
    }

    @Override
    public List<GoshOfflineRefundApplyDO> queryRecordsByOutOrderSn(String orderId) {
        return query()
                .eq(GoshOfflineRefundApplyDO.ORDER_SN, orderId)
                .eq(GoshOfflineRefundApplyDO.IS_DEL, CommonConstant.NUMBER_ZERO)
                .list();
    }
}
