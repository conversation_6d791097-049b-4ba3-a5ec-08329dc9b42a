package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.PageWithdrawListDTO;
import com.fshows.gosh.dao.domain.result.PageWithdrawResultDTO;
import com.fshows.gosh.dao.domain.result.WithdrawStatisticsDTO;
import com.fshows.gosh.dao.entity.GoshShopWithdrawDO;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

/**
 * <p>
 * 商户提现表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
public interface GoshShopWithdrawMapper extends BaseMapper<GoshShopWithdrawDO> {

    /**
     * 账户提现管理列表
     *
     * @param page
     * @param query
     * @return
     */
    Page<PageWithdrawResultDTO> pageWithdrawList(Page<PageWithdrawListDTO> page, @Param("dto") PageWithdrawListDTO query);


    /**
     * 账户提现管理列表(流式)
     *
     * @param param
     * @param resultHandler
     */
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = Integer.MIN_VALUE)
    void pageWithdrawList(@Param("dto") PageWithdrawListDTO param, ResultHandler<PageWithdrawResultDTO> resultHandler);

    /**
     * 提现统计
     *
     * @param withdrawListDTO
     * @return
     */
    WithdrawStatisticsDTO withdrawStatistics(@Param("dto") PageWithdrawListDTO withdrawListDTO);
}
