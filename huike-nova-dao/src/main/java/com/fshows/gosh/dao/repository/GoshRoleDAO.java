package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshRoleDO;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.dao.domain.param.oem.FindRolePageDTO;

import java.util.List;

/**
 * <p>
 * 来逛呗角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshRoleDAO extends IService<GoshRoleDO> {

    /**
     * 根据角色id查询角色信息
     *
     * @param roleId
     * @return
     */
    GoshRoleDO getByRoleId(String roleId);

    /**
     * 分页查看角色列表
     *
     * @param param
     * @return
     */
    Page<GoshRoleDO> pageQueryRole(PageParam<FindRolePageDTO> param);

    /**
     * 新增角色
     *
     * @param roleDO
     * @return
     */
    boolean saveRole(GoshRoleDO roleDO);

    /**
     * 根据角色名称查询角色信息
     *
     * @param roleName
     * @param belongId
     * @param belongType
     * @return
     */
    GoshRoleDO getRoleByRoleName(String roleName, String belongId, Integer belongType);

    /**
     * 更新角色信息
     *
     * @param roleId
     * @param roleName
     * @param remark
     * @param isAllSelect
     * @return
     */
    boolean updateRole(String roleId, String roleName, String remark, Integer isAllSelect);

    /**
     * 删除角色
     *
     * @param roleId
     * @return
     */
    boolean deleteRole(String roleId);

    /**
     * 获取角色全部列表
     *
     * @param belongType
     * @param belongId
     * @return
     */
    List<GoshRoleDO> queryAllRole(Integer belongType, String belongId);

    /**
     * 通过门店id查询收银员角色信息
     *
     * @param belongId
     * @param systemRoleType
     * @return
     */
    GoshRoleDO getCashierRoleByBelongId(String belongId, Integer systemRoleType);

    /**
     * 根据商铺获取管理员角色
     *
     * @param shopId
     * @return
     */
    GoshRoleDO getAdminRole(String shopId);
}
