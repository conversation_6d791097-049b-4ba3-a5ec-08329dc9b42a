package com.fshows.gosh.dao.mapper;

import com.fshows.gosh.dao.entity.GoshMinaAdvertDayRecordDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 来逛呗商家版小程序广告日汇总 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface GoshMinaAdvertDayRecordMapper extends BaseMapper<GoshMinaAdvertDayRecordDO> {

    /**
     * 更新广告数据
     *
     * @param visitDay 日期
     * @param type     类型 ：1-点击 2-曝光
     * @param advertId 广告id
     * @param uvValue  uv值
     */
    void modifyVisitData(@Param("visitDay") Integer visitDay, @Param("type")Integer type, @Param("advertId")String advertId, @Param("uvValue")Integer uvValue);

}
