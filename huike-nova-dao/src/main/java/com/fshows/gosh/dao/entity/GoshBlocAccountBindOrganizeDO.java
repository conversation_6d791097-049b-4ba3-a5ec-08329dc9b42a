package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 集团账号和组织关系
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Data
@TableName("gosh_bloc_account_bind_organize")
public class GoshBlocAccountBindOrganizeDO implements Serializable {

    public static final String ID = "id";
    public static final String BLOC_ID = "bloc_id";
    public static final String ORG_ID = "org_id";
    public static final String ACCOUNT_ID = "account_id";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 集团id
     */
    @TableField("bloc_id")
    private String blocId;
    /**
     * 组织id
     */
    @TableField("org_id")
    private String orgId;
    /**
     * 账号id
     */
    @TableField("account_id")
    private String accountId;
    /**
     * 是否删除:0未删除,1已删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

}
