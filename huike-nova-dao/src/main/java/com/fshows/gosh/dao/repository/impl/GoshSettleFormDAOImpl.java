package com.fshows.gosh.dao.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.PagePaymentListParamDTO;
import com.fshows.gosh.dao.domain.result.PagePaymentListResultDTO;
import com.fshows.gosh.dao.domain.result.SettleFormResultDTO;
import com.fshows.gosh.dao.entity.GoshSettleFormDO;
import com.fshows.gosh.dao.mapper.GoshSettleFormMapper;
import com.fshows.gosh.dao.repository.GoshSettleFormDAO;
import com.huike.nova.common.enums.acct.SettleFormStatusEnum;
import com.huike.nova.common.enums.acct.SettleFormTransferStatusEnum;
import com.huike.nova.common.metadata.PageParam;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 结算单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Service
public class GoshSettleFormDAOImpl extends ServiceImpl<GoshSettleFormMapper, GoshSettleFormDO> implements GoshSettleFormDAO {

    /**
     * 查询出款单列表
     *
     * @param pageDTO
     * @return
     */
    @Override
    public Page<PagePaymentListResultDTO> pagePaymentList(PageParam<PagePaymentListParamDTO> pageDTO) {
        Page<PagePaymentListResultDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());
        return getBaseMapper().pagePaymentList(page, pageDTO.getQuery());
    }

    /**
     * 导出出款单列表
     *
     * @param dto
     * @param resultHandler
     * @return
     */
    @Override
    public void paymentListExport(PagePaymentListParamDTO dto, ResultHandler<PagePaymentListResultDTO> resultHandler) {
        getBaseMapper().paymentListExport(dto, resultHandler);
    }

    /**
     * 根据集团id和时间获取结算单
     *
     * @param settlementDay
     * @param blocId
     * @param channelType
     * @return
     */
    @Override
    public GoshSettleFormDO getSettleForm(int settlementDay, String blocId, String channelType, Integer meiTuanType) {
        return query()
                .eq(GoshSettleFormDO.BLOC_ID, blocId)
                .eq(GoshSettleFormDO.SETTLE_DATE, settlementDay)
                .eq(GoshSettleFormDO.PLATFORM_TYPE, channelType)
                .eq(GoshSettleFormDO.MEI_TUAN_TYPE, meiTuanType)
                .last("limit 1")
                .one();
    }

    /**
     * 保存结算单
     *
     * @param settleFormDO
     * @return
     */
    @Override
    public boolean saveSettleForm(GoshSettleFormDO settleFormDO) {
        return save(settleFormDO);
    }

    /**
     * 根据serialNo删除结算单
     *
     * @param serialNo
     * @return
     */
    @Override
    public boolean removeBySerialNo(String serialNo) {
        return remove(new LambdaQueryWrapper<GoshSettleFormDO>()
                .eq(GoshSettleFormDO::getSerialNo, serialNo)
        );
    }

    @Override
    public List<GoshSettleFormDO> getSettleFormList(String blocId, String platformType, String settleDate) {
        QueryChainWrapper<GoshSettleFormDO> eq = query()
                .eq(StrUtil.isNotBlank(blocId), GoshSettleFormDO.BLOC_ID, blocId)
                .eq(GoshSettleFormDO.SETTLE_DATE, settleDate)
                .eq(StrUtil.isNotBlank(platformType), GoshSettleFormDO.PLATFORM_TYPE, platformType)
                .eq(GoshSettleFormDO.SETTLE_FORM_STATUS, SettleFormStatusEnum.CONFIRM.getValue())
                .in(GoshSettleFormDO.TRANSFER_STATUS, SettleFormTransferStatusEnum.getNeedDealList());
        return eq.list();

    }

    @Override
    public boolean updateSettleFormStatus(String serialNo, Integer settleFormStatus) {

        return update()
                .set(GoshSettleFormDO.SETTLE_FORM_STATUS, settleFormStatus)
                .eq(GoshSettleFormDO.SERIAL_NO, serialNo)
                .update();
    }

    @Override
    public boolean updateTransferStatus(String serialNo, Integer transferStatus, Integer settleFormStatus) {
        return update()
                .set(GoshSettleFormDO.TRANSFER_STATUS, transferStatus)
                .set(ObjectUtil.isNotNull(settleFormStatus), GoshSettleFormDO.SETTLE_FORM_STATUS, settleFormStatus)
                .eq(GoshSettleFormDO.SERIAL_NO, serialNo)
                .update();
    }

    /**
     * 更新转账状态
     *
     * @param serialNo   结算单
     * @param billStatus 对账状态
     * @param billSource 对账来源
     * @param operator   操作人
     * @return boolean
     */
    @Override
    public boolean updateBillStatus(String serialNo, Integer billStatus, String billSource, String operator) {
        return update()
                .set(GoshSettleFormDO.BILL_STATUS, billStatus)
                .set(GoshSettleFormDO.BILL_SOURCE, billSource)
                .set(GoshSettleFormDO.OPERATOR, operator)
                .eq(GoshSettleFormDO.SERIAL_NO, serialNo)
                .update();
    }

    @Override
    public GoshSettleFormDO getSettleFormBySerialNo(String serialNo) {
        return query()
                .eq(GoshSettleFormDO.SERIAL_NO, serialNo)
                .last("limit 1")
                .one();
    }

    @Override
    public List<SettleFormResultDTO> getSettleFormResultList(String settleDate, String relationBalanceId) {
        return baseMapper.getSettleFormResultList(settleDate, relationBalanceId);
    }

    @Override
    public List<SettleFormResultDTO> getMergeTransferSuccessSettleFormResultList(String settleDate, String relationBalanceId) {
        return baseMapper.getMergeTransferSuccessSettleFormResultList(settleDate, relationBalanceId);
    }

    /**
     * 更新合并转账单号
     *
     * @param mergeTransferNo
     * @param serialNoList
     * @return boolean
     */
    @Override
    public boolean updateMergeTransferNo(String mergeTransferNo, Integer mergeTransferStatus, List<String> serialNoList) {
        return update()
                .set(GoshSettleFormDO.MERGE_TRANSFER_NO, mergeTransferNo)
                .set(GoshSettleFormDO.MERGE_TRANSFER_STATUS, mergeTransferStatus)
                .in(GoshSettleFormDO.SERIAL_NO, serialNoList)
                .update();
    }

    /**
     * 更新合并转账状态
     *
     * @param mergeTransferNo
     * @param mergeTransferStatus
     * @return boolean
     */
    @Override
    public boolean updateMergeTransferStatus(String mergeTransferNo, Integer mergeTransferStatus) {
        return update()
                .set(GoshSettleFormDO.MERGE_TRANSFER_STATUS, mergeTransferStatus)
                .eq(GoshSettleFormDO.MERGE_TRANSFER_NO, mergeTransferNo)
                .update();
    }

    @Override
    public boolean updateTransferStatusAndMergeTransferStatus(String serialNo, Integer transferStatus, Integer mergeTransferStatus) {
        return update()
                .set(GoshSettleFormDO.TRANSFER_STATUS, transferStatus)
                .set(ObjectUtil.isNotNull(mergeTransferStatus), GoshSettleFormDO.MERGE_TRANSFER_STATUS, mergeTransferStatus)
                .eq(GoshSettleFormDO.SERIAL_NO, serialNo)
                .update();
    }

    /**
     * 修改结算数据
     *
     * @param formDO 参数
     */
    @Override
    public void modifyPaymentData(GoshSettleFormDO formDO) {
        update().set(GoshSettleFormDO.SETTLE_FORM_AMOUNT, formDO.getSettleFormAmount())
                .set(GoshSettleFormDO.NON_BUSINESS_AMOUNT, formDO.getNonBusinessAmount())
                .set(GoshSettleFormDO.BUSINESS_MOTHER_CARD_AMOUNT, formDO.getBusinessMotherCardAmount())
                .set(GoshSettleFormDO.INCOME_DIFF_AMOUNT, formDO.getIncomeDiffAmount())
                .set(GoshSettleFormDO.DEDUCT_DIFF_AMOUNT, formDO.getDeductDiffAmount())
                .set(GoshSettleFormDO.MOTHER_INCOME_DIFF_AMOUNT, formDO.getMotherIncomeDiffAmount())
                .set(GoshSettleFormDO.MOTHER_DEDUCT_DIFF_AMOUNT, formDO.getMotherDeductDiffAmount())
                .set(GoshSettleFormDO.OUT_PRODUCT_SETTLE_AMOUNT, formDO.getOutProductSettleAmount())
                .set(GoshSettleFormDO.DIFF_AMOUNT, formDO.getDiffAmount())
                .set(GoshSettleFormDO.DIFF_COMMISSION, formDO.getDiffCommission())
                .set(GoshSettleFormDO.OPERATOR, formDO.getOperator())
                .eq(GoshSettleFormDO.SERIAL_NO, formDO.getSerialNo())
                .update();
    }
}
