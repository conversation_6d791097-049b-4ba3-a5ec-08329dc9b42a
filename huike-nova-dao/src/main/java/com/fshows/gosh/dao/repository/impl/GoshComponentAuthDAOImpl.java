package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshComponentAuthDO;
import com.fshows.gosh.dao.mapper.GoshComponentAuthMapper;
import com.fshows.gosh.dao.repository.GoshComponentAuthDAO;
import com.huike.nova.common.util.FsDateUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 账户组件授权表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
@Service
public class GoshComponentAuthDAOImpl extends ServiceImpl<GoshComponentAuthMapper, GoshComponentAuthDO> implements GoshComponentAuthDAO {

    /**
     * 查询是否存在未失效的授权链接
     *
     * @param accountId
     * @param componentType
     * @return
     */
    @Override
    public GoshComponentAuthDO getNotExpireComponentAuth(String accountId, String componentType) {
        return query()
                .eq(GoshComponentAuthDO.ACCOUNT_ID, accountId)
                .eq(GoshComponentAuthDO.COMPONENT_TYPE, componentType)
                .gt(GoshComponentAuthDO.EXPIRE_TIMESTAMP, FsDateUtils.getTimeStamp(new Date()) + (60*10))
                .eq(GoshComponentAuthDO.AUTH_STATUS, 1)
                .last("limit 1")
                .one();
    }

    @Override
    public void updateAuthStatus(Long id, Integer authStatus) {
        update().set(GoshComponentAuthDO.AUTH_STATUS, authStatus).eq(GoshComponentAuthDO.ID, id)
                .update();
    }
}
