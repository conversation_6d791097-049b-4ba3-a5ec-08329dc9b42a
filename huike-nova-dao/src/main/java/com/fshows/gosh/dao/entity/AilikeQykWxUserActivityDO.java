package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 微信营销活动-用户活动表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Data
@TableName("ailike_qyk_wx_user_activity")
public class AilikeQykWxUserActivityDO implements Serializable {

    public static final String ID = "id";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    public static final String USER_ACTIVITY_RELATION_ID = "user_activity_relation_id";
    public static final String USER_ID = "user_id";
    public static final String ACTIVITY_ID = "activity_id";
    public static final String PRODUCT_ID = "product_id";
    public static final String PARTICIPATION_USER_COUNT = "participation_user_count";
    public static final String ORDER_PAY_USER_COUNT = "order_pay_user_count";
    public static final String COUPON_PAY_COUNT = "coupon_pay_count";
    public static final String COUPON_VERIFY_COUNT = "coupon_verify_count";
    public static final String VERIFY_TOTAL_AMOUNT = "verify_total_amount";
    public static final String COMMISSION_TOTAL_AMOUNT = "commission_total_amount";
    public static final String IS_DEL = "is_del";
    public static final String OPEN_ID = "open_id";
    public static final String NICK_NAME = "nick_name";
    public static final String AVATAR = "avatar";
    public static final String SHARE_POSTER = "share_poster";
    public static final String POSTER_BASEMAP = "poster_basemap";
    public static final String POSTER_USER_AVATAR = "poster_user_avatar";
    public static final String POSTER_USER_NICK_NAME = "poster_user_nick_name";
    public static final String VISITS_COUNT = "visits_count";
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 用户活动关联 id（业务 id）
     */
    @TableField("user_activity_relation_id")
    private String userActivityRelationId;
    /**
     * 用户 id
     */
    @TableField("user_id")
    private String userId;
    /**
     * 营销活动 id
     */
    @TableField("activity_id")
    private String activityId;
    /**
     * 平台方商品 id
     */
    @TableField("product_id")
    private String productId;
    /**
     * 参与用户数（推广人数）
     */
    @TableField("participation_user_count")
    private Integer participationUserCount;
    /**
     * 订单支付用户数量（下单人数）
     */
    @TableField("order_pay_user_count")
    private Integer orderPayUserCount;
    /**
     * 券支付数量（下单券数）
     */
    @TableField("coupon_pay_count")
    private Integer couponPayCount;
    /**
     * 券核销数量（核销券数）
     */
    @TableField("coupon_verify_count")
    private Integer couponVerifyCount;
    /**
     * 核销总金额(核销金额（元）)
     */
    @TableField("verify_total_amount")
    private BigDecimal verifyTotalAmount;
    /**
     * 佣金总额（已赚佣金（元））
     */
    @TableField("commission_total_amount")
    private BigDecimal commissionTotalAmount;
    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 用户 openId
     */
    @TableField("open_id")
    private String openId;
    /**
     * 用户昵称
     */
    @TableField("nick_name")
    private String nickName;
    /**
     * 用户头像
     */
    @TableField("avatar")
    private String avatar;
    /**
     * 分享海报
     */
    @TableField("share_poster")
    private String sharePoster;
    /**
     * 海报底图
     */
    @TableField("poster_basemap")
    private String posterBasemap;
    /**
     * 海报用户头像
     */
    @TableField("poster_user_avatar")
    private String posterUserAvatar;
    /**
     * 海报用户昵称
     */
    @TableField("poster_user_nick_name")
    private String posterUserNickName;
    /**
     * 访问数量
     */
    @TableField("visits_count")
    private Integer visitsCount;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

}
