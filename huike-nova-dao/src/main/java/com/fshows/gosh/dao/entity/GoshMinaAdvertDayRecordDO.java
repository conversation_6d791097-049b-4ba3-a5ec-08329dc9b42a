package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 来逛呗商家版小程序广告日汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Data
@TableName("gosh_mina_advert_day_record")
public class GoshMinaAdvertDayRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 广告id
     */
    @TableField("adv_id")
    private String advId;

    /**
     * 访问日期 20250225
     */
    @TableField("visit_day")
    private Integer visitDay;

    /**
     * 广告位置:HOME
     */
    @TableField("visit_pv")
    private Integer visitPv;

    /**
     * 广告图片地址
     */
    @TableField("visit_uv")
    private Integer visitUv;

    /**
     * 广告跳转地址
     */
    @TableField("click_pv")
    private Integer clickPv;

    /**
     * 投放开始时间
     */
    @TableField("click_uv")
    private Integer clickUv;

    /**
     * 0正常 1删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String ADV_ID = "adv_id";

    public static final String VISIT_DAY = "visit_day";

    public static final String VISIT_PV = "visit_pv";

    public static final String VISIT_UV = "visit_uv";

    public static final String CLICK_PV = "click_pv";

    public static final String CLICK_UV = "click_uv";

    public static final String IS_DEL = "is_del";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
