package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.AilikeQykWxUserStatisticsDO;
import com.fshows.gosh.dao.mapper.AilikeQykWxUserStatisticsMapper;
import com.fshows.gosh.dao.repository.AilikeQykWxUserStatisticsDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 微信营销活动-用户数据汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Service
public class AilikeQykWxUserStatisticsDAOImpl extends ServiceImpl<AilikeQykWxUserStatisticsMapper, AilikeQykWxUserStatisticsDO> implements AilikeQykWxUserStatisticsDAO {

    /**
     * 根据用户id查询
     *
     * @param userId
     * @return
     */
    @Override
    public AilikeQykWxUserStatisticsDO getByUserId(String userId) {
        return query().eq(AilikeQykWxUserStatisticsDO.USER_ID, userId)
                .eq(AilikeQykWxUserStatisticsDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last(" limit 1")
                .one();
    }

}
