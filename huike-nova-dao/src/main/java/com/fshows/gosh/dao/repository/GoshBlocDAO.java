package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.param.BlocQueryParamDTO;
import com.fshows.gosh.dao.domain.result.BlocQueryResultDTO;
import com.fshows.gosh.dao.domain.result.MerchantMinaLoginResultDTO;
import com.fshows.gosh.dao.entity.GoshBlocDO;
import com.huike.nova.common.metadata.PageParam;

import java.util.List;

/**
 * <p>
 * 来逛呗集团表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshBlocDAO extends IService<GoshBlocDO> {

    /**
     * 获取服务商appId
     *
     * @param orgId
     * @return
     */
    MerchantMinaLoginResultDTO getServiceAppId(String orgId);

    /**
     * 根据集团id获取集团信息
     *
     * @param blocId
     * @return
     */
    GoshBlocDO getBlocInfo(String blocId);

    /**
     * 根据appId获取集团信息
     *
     * @param blocName 集团名称
     * @return GoshBlocDO
     */
    GoshBlocDO getBlocByBlocName(String blocName);

    /**
     * 根据appId获取集团信息
     *
     * @param appId
     * @return
     */
    GoshBlocDO getBlocByAppId(String appId);

    /**
     * 查询所有集团
     *
     * @return
     */
    List<GoshBlocDO> findAllBloc();

    /**
     * 查询部分集团账号有权限的集团列表
     *
     * @param operatorId 运营后台账号Id
     * @return
     */
    List<GoshBlocDO> findBlocListByOperatorId(String operatorId);

    /**
     * 分页查询集团列表数据
     *
     * @param pageDTO
     * @return
     */
    Page<BlocQueryResultDTO> pageBlocList(PageParam<BlocQueryParamDTO> pageDTO);

    /**
     * 更新集团状态
     *
     * @param blocId     集团 id
     * @param blocStatus 集团状态
     */
    void updateBlocStatus(String blocId, Integer blocStatus);
}
