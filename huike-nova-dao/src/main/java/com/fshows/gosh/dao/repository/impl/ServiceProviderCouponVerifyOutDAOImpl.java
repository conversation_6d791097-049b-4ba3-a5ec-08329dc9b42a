package com.fshows.gosh.dao.repository.impl;

import com.fshows.gosh.dao.domain.result.OutProductSettleAmountResultDTO;
import com.fshows.gosh.dao.entity.ServiceProviderCouponVerifyOutDO;
import com.fshows.gosh.dao.mapper.ServiceProviderCouponVerifyOutMapper;
import com.fshows.gosh.dao.repository.ServiceProviderCouponVerifyOutDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 外部商品服务商核销卡券记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Service
public class ServiceProviderCouponVerifyOutDAOImpl extends ServiceImpl<ServiceProviderCouponVerifyOutMapper, ServiceProviderCouponVerifyOutDO> implements ServiceProviderCouponVerifyOutDAO {

    /**
     * @param appId       appId
     * @param settleDay   结算日
     * @param poiIdList   广场列表
     * @param channelType 通道
     */
    @Override
    public OutProductSettleAmountResultDTO statisticsSettleAmount(String appId, Integer settleDay, List<String> poiIdList, String channelType, String channelCode) {
        return getBaseMapper().statisticsSettleAmount(appId, settleDay, poiIdList, channelType, channelCode);
    }

    /**
     * 根据appid统计差异金额
     *
     * @param appId       appId
     * @param settleDay   结算日
     * @param channelType 通道
     * @param dataType    数据类型
     */
    @Override
    public BigDecimal statisticsDifferenceAmount(String appId, Integer settleDay, String channelType, String channelCode, String dataType, Integer orgProductType) {
        return getBaseMapper().statisticsDifferenceAmount(appId, settleDay, channelType, channelCode, dataType, orgProductType);
    }

    /**
     * 修改差异数据的结算单号
     *
     * @param appId       appId
     * @param settleDay   结算日
     * @param channelType 平台
     * @param channelCode 通道
     */
    @Override
    public void modifySerialNo(String appId, Integer settleDay, String channelType, String channelCode, String serialNo) {
        getBaseMapper().modifySerialNo(appId, settleDay, channelType, channelCode, serialNo);
    }
}
