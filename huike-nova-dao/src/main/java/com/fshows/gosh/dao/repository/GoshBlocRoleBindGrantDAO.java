package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.GoshBlocRoleBindGrantDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 角色和权限关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshBlocRoleBindGrantDAO extends IService<GoshBlocRoleBindGrantDO> {

    /**
     * 根据角色id查询
     *
     * @param oldRoleId 角色id
     * @return 角色和权限关系
     */
    List<GoshBlocRoleBindGrantDO> findByRoleId(String oldRoleId);

    /**
     * 根据角色id删除
     *
     * @param roleId 角色id
     */
    void deleteByRoleId(String roleId);

    /**
     * 根据角色id查询权限id列表
     *
     * @param roleId 角色id
     * @return 权限id列表
     */
    List<String> findGrantIdListByRoleId(String roleId);
}
