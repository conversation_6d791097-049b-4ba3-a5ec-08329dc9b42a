package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshOfflineRefundOperationRecordDO;
import com.fshows.gosh.dao.mapper.GoshOfflineRefundOperationRecordMapper;
import com.fshows.gosh.dao.repository.GoshOfflineRefundOperationRecordDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 线下退款操作记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Service
public class GoshOfflineRefundOperationRecordDAOImpl extends ServiceImpl<GoshOfflineRefundOperationRecordMapper, GoshOfflineRefundOperationRecordDO> implements GoshOfflineRefundOperationRecordDAO {

    /**
     * 根据paymentId查询操作记录列表
     *
     * @param paymentId 打款id
     * @return 操作记录列表
     */
    @Override
    public List<GoshOfflineRefundOperationRecordDO> findByPaymentId(String paymentId) {
        LambdaQueryWrapper<GoshOfflineRefundOperationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GoshOfflineRefundOperationRecordDO::getPaymentId, paymentId)
                .eq(GoshOfflineRefundOperationRecordDO::getIsDel, DelFlagEnum.NOT_DEL.getValue())
                .orderByDesc(GoshOfflineRefundOperationRecordDO::getCreateTime);
        return this.list(queryWrapper);
    }

    /**
     * 根据recordId查询最新的操作记录
     *
     * @param recordId 记录ID
     * @return 最新的操作记录
     */
    @Override
    public GoshOfflineRefundOperationRecordDO findLatestByRecordId(String recordId) {
        LambdaQueryWrapper<GoshOfflineRefundOperationRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GoshOfflineRefundOperationRecordDO::getRecordId, recordId)
                .eq(GoshOfflineRefundOperationRecordDO::getIsDel, DelFlagEnum.NOT_DEL.getValue())
                .orderByDesc(GoshOfflineRefundOperationRecordDO::getCreateTime)
                .last("LIMIT 1");
        return this.getOne(queryWrapper);
    }
}
