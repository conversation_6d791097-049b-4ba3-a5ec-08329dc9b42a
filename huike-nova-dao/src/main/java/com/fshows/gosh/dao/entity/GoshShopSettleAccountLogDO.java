package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 商铺结算卡记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
@Data
@TableName("gosh_shop_settle_account_log")
public class GoshShopSettleAccountLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商铺id
     */
    @TableField("shop_id")
    private String shopId;

    /**
     * 账户id
     */
    @TableField("account_id")
    private String accountId;

    /**
     * 银行账号类型 1-法人对私卡 2-企业对公户
     */
    @TableField("settle_account_type")
    private Integer settleAccountType;

    /**
     * 法人姓名
     */
    @TableField("legal_name")
    private String legalName;

    /**
     * 结算银行卡号
     */
    @TableField("settle_account_no")
    private String settleAccountNo;

    /**
     * 银行总行名称
     */
    @TableField("settle_bank_name")
    private String settleBankName;

    /**
     * 开户支行联行号
     */
    @TableField("settle_bank_branch_code")
    private String settleBankBranchCode;

    /**
     * 法人证件类型 1-身份证 2-护照（国内护照）
     */
    @TableField("legal_cert_type")
    private Integer legalCertType;

    /**
     * 法人证件号码
     */
    @TableField("legal_cert_no")
    private String legalCertNo;

    /**
     * 结算卡状态 ：1-生效 2-失效
     */
    @TableField("card_status")
    private Integer cardStatus;

    /**
     * 失败原因
     */
    @TableField("rejected_reason")
    private String rejectedReason;

    /**
     * 0正常 1删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public static final String ID = "id";
    public static final String SHOP_ID = "shop_id";
    public static final String ACCOUNT_ID = "account_id";
    public static final String COMPANY_NAME = "company_name";

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public static final String LICENSE_NO = "license_no";

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

    /**
     * 申请单ID
     */
    @TableField("apply_id")
    private String applyId;
    /**
     * 营业执照名称
     */
    @TableField("company_name")
    private String companyName;
    /**
     * 营业执照注册号码
     */
    @TableField("license_no")
    private String licenseNo;

    public static final String SETTLE_ACCOUNT_TYPE = "settle_account_type";

    public static final String LEGAL_NAME = "legal_name";

    public static final String SETTLE_ACCOUNT_NO = "settle_account_no";

    public static final String SETTLE_BANK_NAME = "settle_bank_name";

    public static final String SETTLE_BANK_BRANCH_CODE = "settle_bank_branch_code";

    public static final String LEGAL_CERT_TYPE = "legal_cert_type";

    public static final String LEGAL_CERT_NO = "legal_cert_no";

    public static final String CARD_STATUS = "card_status";

    public static final String REJECTED_REASON = "rejected_reason";

    public static final String IS_DEL = "is_del";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String APPLY_ID = "apply_id";

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

}
