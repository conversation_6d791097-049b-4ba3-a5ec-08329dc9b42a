package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.PagePaymentListParamDTO;
import com.fshows.gosh.dao.domain.param.refund.PageOfflinePaymentParamDTO;
import com.fshows.gosh.dao.domain.param.refund.PageRefundParamDTO;
import com.fshows.gosh.dao.domain.result.PagePaymentListResultDTO;
import com.fshows.gosh.dao.domain.result.refund.PageOfflinePaymentResultDTO;
import com.fshows.gosh.dao.domain.result.refund.PageRefundResultDTO;
import com.fshows.gosh.dao.entity.GoshOfflineRefundApplyDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

/**
 * <p>
 * 抖音线下退款申请 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface GoshOfflineRefundApplyMapper extends BaseMapper<GoshOfflineRefundApplyDO> {

    /**
     * 查询出款单列表
     *
     * @param page  页参数
     * @param query 查询参数
     * @return 数据
     */
    Page<PageOfflinePaymentResultDTO> pageOfflinePaymentList(Page<PageOfflinePaymentResultDTO> page, @Param("query") PageOfflinePaymentParamDTO query);

    /**
     * 导出线上退款申请数据
     *
     * @param query         参数
     * @param resultHandler 数据
     */
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = Integer.MIN_VALUE)
    void pageOfflinePaymentList(@Param("query") PageOfflinePaymentParamDTO query, ResultHandler<PageOfflinePaymentResultDTO> resultHandler);
}
