package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 导出任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Data
@TableName("ailike_export_task")
public class AilikeExportTaskDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务id
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 导出用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 导出平台类型
     */
    @TableField("platform_type")
    private String platformType;

    /**
     * 导出业务类型
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 导出参数
     */
    @TableField("task_param")
    private String taskParam;

    /**
     * oss文件路径
     */
    @TableField("oss_path")
    private String ossPath;

    /**
     * oss下载地址
     */
    @TableField("oss_url")
    private String ossUrl;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 任务结束时间
     */
    @TableField("complete_time")
    private Date completeTime;

    /**
     * 任务状态 0:未生成；1:已生成；2:处理中；3:无数据 ；4:生成失败
     */
    @TableField("task_status")
    private Integer taskStatus;

    /**
     * 异常信息
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否删除. 1-未删除 2-已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCompleteTime() {
        if (this.completeTime != null) {
          return new Date(this.completeTime.getTime());
        } else {
          return null;
        }
    }

    public void setCompleteTime(Date completeTime) {
        if (completeTime != null) {
            this.completeTime = new Date(completeTime.getTime());
        } else {
            this.completeTime = null;
        }
    }
    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String TASK_ID = "task_id";

    public static final String USER_ID = "user_id";

    public static final String PLATFORM_TYPE = "platform_type";

    public static final String BUSINESS_TYPE = "business_type";

    public static final String TASK_PARAM = "task_param";

    public static final String OSS_PATH = "oss_path";

    public static final String OSS_URL = "oss_url";

    public static final String FILE_NAME = "file_name";

    public static final String COMPLETE_TIME = "complete_time";

    public static final String TASK_STATUS = "task_status";

    public static final String REMARK = "remark";

    public static final String DEL_FLAG = "del_flag";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
