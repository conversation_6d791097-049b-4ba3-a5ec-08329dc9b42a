package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 线下退款操作记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@TableName("gosh_offline_refund_operation_record")
public class GoshOfflineRefundOperationRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 记录 id
     */
    @TableField("record_id")
    private String recordId;

    /**
     * 打款 id
     */
    @TableField("payment_id")
    private String paymentId;

    /**
     * 操作类型 1 发起团款申请 2 重新发起退款申请 3 打款完成 4 打款失败
     */
    @TableField("operation_type")
    private Integer operationType;

    /**
     * 详情内容（json）
     */
    @TableField("detail_content")
    private String detailContent;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String RECORD_ID = "record_id";

    public static final String PAYMENT_ID = "payment_id";

    public static final String OPERATION_TYPE = "operation_type";

    public static final String DETAIL_CONTENT = "detail_content";

    public static final String IS_DEL = "is_del";

}
