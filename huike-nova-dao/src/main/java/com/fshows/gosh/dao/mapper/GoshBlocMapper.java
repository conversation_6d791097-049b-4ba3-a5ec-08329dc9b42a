package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.BlocAccountPageListDTO;
import com.fshows.gosh.dao.domain.param.BlocQueryParamDTO;
import com.fshows.gosh.dao.domain.result.BlocQueryResultDTO;
import com.fshows.gosh.dao.domain.result.MerchantMinaLoginResultDTO;
import com.fshows.gosh.dao.entity.GoshBlocAccountDO;
import com.fshows.gosh.dao.entity.GoshBlocDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huike.nova.common.metadata.PageParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 来逛呗集团表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshBlocMapper extends BaseMapper<GoshBlocDO> {

    /**
     * 获取服务商appId
     *
     * @param orgId
     * @return
     */
    MerchantMinaLoginResultDTO getServiceAppId(String orgId);


    /**
     * 分页查询集团列表数据
     *
     * @param page  页数据
     * @param query 查询参数
     * @return 返回列表
     */
    Page<BlocQueryResultDTO> pageBlocList(Page<BlocQueryResultDTO> page, @Param("query") BlocQueryParamDTO query);

    /**
     * 查询部分集团账号有权限的集团列表
     *
     * @param operatorId 运营后台账号Id
     * @return
     */
     List<GoshBlocDO> findBlocListByOperatorId(@Param("operatorId") String operatorId);

}
