package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.GoshShopFreezeLogDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 来逛呗-商铺表结算冻结操作日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface GoshShopFreezeLogDAO extends IService<GoshShopFreezeLogDO> {
    /**
     * @param shopId 门店Id
     * @return 数据
     */
    List<GoshShopFreezeLogDO> getFreezeLogList(String shopId);
}
