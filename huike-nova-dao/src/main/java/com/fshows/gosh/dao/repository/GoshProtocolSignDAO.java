package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshProtocolSignDO;

import java.util.List;

/**
 * <p>
 * 来逛呗协议签署表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshProtocolSignDAO extends IService<GoshProtocolSignDO> {

    /**
     * 批量保存商户协议
     *
     * @param list
     * @return
     */
    boolean batchProtocolSign(List<GoshProtocolSignDO> list);

}
