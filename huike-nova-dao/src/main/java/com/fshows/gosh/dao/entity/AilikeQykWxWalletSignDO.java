package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 钱包签约关系记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Data
@TableName("ailike_qyk_wx_wallet_sign")
public class AilikeQykWxWalletSignDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务签约id
     */
    @TableField("app_id")
    private String appId;

    /**
     * 业务签约id
     */
    @TableField("business_sign_id")
    private String businessSignId;

    /**
     * 出账钱包Id
     */
    @TableField("out_wallet_id")
    private String outWalletId;

    /**
     * 进账钱包id
     */
    @TableField("in_wallet_id")
    private String inWalletId;

    /**
     * 关联余额ID,存在则无需签约直接为成功
     */
    @TableField("relation_balance_id")
    private String relationBalanceId;

    /**
     * 付呗签约id
     */
    @TableField("sign_id")
    private String signId;

    /**
     * 付呗url
     */
    @TableField("sign_url")
    private String signUrl;

    /**
     * 签约状态：INIT:初始化； WAIT_SIGN：待签约；SUCCESS：已签约；FAIL：签约失败
     */
    @TableField("sign_status")
    private String signStatus;

    /**
     * 签约失败原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String APP_ID = "app_id";

    public static final String BUSINESS_SIGN_ID = "business_sign_id";

    public static final String OUT_WALLET_ID = "out_wallet_id";

    public static final String IN_WALLET_ID = "in_wallet_id";

    public static final String RELATION_BALANCE_ID = "relation_balance_id";

    public static final String SIGN_ID = "sign_id";

    public static final String SIGN_URL = "sign_url";

    public static final String SIGN_STATUS = "sign_status";

    public static final String REASON = "reason";

    public static final String IS_DEL = "is_del";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
