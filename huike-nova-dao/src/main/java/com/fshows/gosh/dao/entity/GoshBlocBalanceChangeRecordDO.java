package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 账户余额变更记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Data
@TableName("gosh_bloc_balance_change_record")
public class GoshBlocBalanceChangeRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 余额记录id
     */
    @TableField("balance_id")
    private String balanceId;

    /**
     * 变更来源记录标识
     */
    @TableField("change_sn")
    private String changeSn;

    /**
     * 变更类型
     */
    @TableField("change_type")
    private String changeType;

    /**
     * 备注
     */
    @TableField("change_remark")
    private String changeRemark;

    /**
     * 变更金额
     */
    @TableField("change_amount")
    private BigDecimal changeAmount;

    /**
     * 变更前总余额
     */
    @TableField("before_total_balance")
    private BigDecimal beforeTotalBalance;

    /**
     * 变更后总余额
     */
    @TableField("after_total_balance")
    private BigDecimal afterTotalBalance;

    /**
     * 变更前可用余额
     */
    @TableField("before_current_balance")
    private BigDecimal beforeCurrentBalance;

    /**
     * 变更后可用余额
     */
    @TableField("after_current_balance")
    private BigDecimal afterCurrentBalance;

    /**
     * 变更前转账在途余额
     */
    @TableField("before_transfer_transit_balance")
    private BigDecimal beforeTransferTransitBalance;

    /**
     * 变更后转账在途余额
     */
    @TableField("after_transfer_transit_balance")
    private BigDecimal afterTransferTransitBalance;

    /**
     * 变更前商圈权益卡待结算金额
     */
    @TableField("before_privilege_coupon_balance")
    private BigDecimal beforePrivilegeCouponBalance;

    /**
     * 变更后商圈权益卡待结算金额
     */
    @TableField("after_privilege_coupon_balance")
    private BigDecimal afterPrivilegeCouponBalance;

    /**
     * 变更前营销补差余额
     */
    @TableField("before_supplement_balance")
    private BigDecimal beforeSupplementBalance;

    /**
     * 变更后营销补差余额
     */
    @TableField("after_supplement_balance")
    private BigDecimal afterSupplementBalance;

    /**
     * 变更前冻结余额
     */
    @TableField("before_frozen_balance")
    private BigDecimal beforeFrozenBalance;

    /**
     * 变更后冻结余额
     */
    @TableField("after_frozen_balance")
    private BigDecimal afterFrozenBalance;

    public static final String ID = "id";

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
    public static final String BALANCE_ID = "balance_id";
    public static final String CHANGE_SN = "change_sn";

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }
    public static final String CHANGE_TYPE = "change_type";
    public static final String CHANGE_REMARK = "change_remark";
    public static final String CHANGE_AMOUNT = "change_amount";
    public static final String BEFORE_TOTAL_BALANCE = "before_total_balance";
    public static final String AFTER_TOTAL_BALANCE = "after_total_balance";
    public static final String BEFORE_CURRENT_BALANCE = "before_current_balance";
    public static final String AFTER_CURRENT_BALANCE = "after_current_balance";
    public static final String BEFORE_TRANSFER_TRANSIT_BALANCE = "before_transfer_transit_balance";
    public static final String AFTER_TRANSFER_TRANSIT_BALANCE = "after_transfer_transit_balance";
    public static final String BEFORE_PRIVILEGE_COUPON_BALANCE = "before_privilege_coupon_balance";
    public static final String AFTER_PRIVILEGE_COUPON_BALANCE = "after_privilege_coupon_balance";
    public static final String BEFORE_SUPPLEMENT_BALANCE = "before_supplement_balance";
    public static final String AFTER_SUPPLEMENT_BALANCE = "after_supplement_balance";
    public static final String BEFORE_FROZEN_BALANCE = "before_frozen_balance";
    public static final String AFTER_FROZEN_BALANCE = "after_frozen_balance";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    public static final String BEFORE_TRANSFER_SPLIT_TRANSIT_BALANCE = "before_transfer_split_transit_balance";
    public static final String AFTER_TRANSFER_SPLIT_TRANSIT_BALANCE = "after_transfer_split_transit_balance";
    /**
     * 是否删除: 0-未删除 1-已删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 变更前分账在途金额
     */
    @TableField("before_transfer_split_transit_balance")
    private BigDecimal beforeTransferSplitTransitBalance;
    /**
     * 变更后分账在途金额
     */
    @TableField("after_transfer_split_transit_balance")
    private BigDecimal afterTransferSplitTransitBalance;

}
