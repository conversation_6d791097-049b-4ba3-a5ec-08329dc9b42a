package com.fshows.gosh.dao.repository.impl;

import com.fshows.gosh.dao.domain.result.OperatorBindOrgResultDTO;
import com.fshows.gosh.dao.entity.GoshOperatorAccountBindOrgAccountDO;
import com.fshows.gosh.dao.mapper.GoshOperatorAccountBindOrgAccountMapper;
import com.fshows.gosh.dao.repository.GoshOperatorAccountBindOrgAccountDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.constant.CommonConstant;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 运营账号和集团账号绑定关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class GoshOperatorAccountBindOrgAccountDAOImpl extends ServiceImpl<GoshOperatorAccountBindOrgAccountMapper, GoshOperatorAccountBindOrgAccountDO> implements GoshOperatorAccountBindOrgAccountDAO {

    /**
     * 根据运营账号Id查询绑定集团
     *
     * @param operatorIdList 运营账号id
     * @return
     */
    @Override
    public List<OperatorBindOrgResultDTO> findByOperatorIdList(List<String> operatorIdList) {
        return getBaseMapper().findByOperatorIdList(operatorIdList);
    }

    /**
     * 查询绑定关系数据
     *
     * @param operatorId 运营后台账号id
     * @param blocId     集团id
     * @return
     */
    @Override
    public GoshOperatorAccountBindOrgAccountDO getInfoOperatorIdAndBlocId(String operatorId, String blocId) {
        return query().eq(GoshOperatorAccountBindOrgAccountDO.OPERATOR_ID, operatorId)
                .eq(GoshOperatorAccountBindOrgAccountDO.BLOC_ID, blocId)
                .eq(GoshOperatorAccountBindOrgAccountDO.IS_DEL, CommonConstant.ZERO)
                .last("limit 1").one();
    }
}
