/**
 * <AUTHOR>
 * @date 2024/10/31 15:24
 * @version 1.0 GoshBlocBindGrantDO
 */
package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 运营账号与权限表
 *
 * <AUTHOR>
 * @version GoshBlocBindGrantDO.java, v 0.1 2024-10-31 15:24 tuyuwei
 */
@Data
@TableName("gosh_operator_account_bind_role")
public class GoshOperatorBindRoleDO implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运营id
     */
    @TableField("operator_id")
    private String operatorId;

    /**
     * 权限id
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
}