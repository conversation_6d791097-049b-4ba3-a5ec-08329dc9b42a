package com.fshows.gosh.dao.mapper;

import com.fshows.gosh.dao.domain.result.OperatorBindOrgResultDTO;
import com.fshows.gosh.dao.entity.GoshOperatorAccountBindOrgAccountDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 运营账号和集团账号绑定关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface GoshOperatorAccountBindOrgAccountMapper extends BaseMapper<GoshOperatorAccountBindOrgAccountDO> {
    /**
     * 根据运营账号Id查询绑定集团
     *
     * @param operatorIdList 运营账号id
     * @return
     */
    List<OperatorBindOrgResultDTO> findByOperatorIdList(@Param("operatorIdList") List<String> operatorIdList);
}
