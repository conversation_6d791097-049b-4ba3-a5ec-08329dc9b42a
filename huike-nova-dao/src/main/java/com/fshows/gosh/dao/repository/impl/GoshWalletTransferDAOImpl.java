package com.fshows.gosh.dao.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fshows.gosh.dao.entity.GoshShopWithdrawDO;
import com.fshows.gosh.dao.entity.GoshWalletTransferDO;
import com.fshows.gosh.dao.entity.GoshWalletTransferDO;
import com.fshows.gosh.dao.mapper.GoshWalletTransferMapper;
import com.fshows.gosh.dao.repository.GoshWalletTransferDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.enums.acct.TransferStatusViewEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 钱包转账记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Service
public class GoshWalletTransferDAOImpl extends ServiceImpl<GoshWalletTransferMapper, GoshWalletTransferDO> implements GoshWalletTransferDAO {

    @Override
    public GoshWalletTransferDO getByTransferNo(String transferNo) {
        return query()
                .eq(GoshWalletTransferDO.TRANSFER_NO, transferNo)
                .eq(GoshWalletTransferDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .one();
    }

    @Override
    public boolean updateByTransferNo(GoshWalletTransferDO walletTransferDO) {
        return update()
                .set(GoshWalletTransferDO.TRANSFER_STATUS, walletTransferDO.getTransferStatus())
                .set(GoshWalletTransferDO.TRACK_TRANSFER_STATUS, walletTransferDO.getTrackTransferStatus())
                .set(StrUtil.isNotBlank(walletTransferDO.getPlatformTransferNo()), GoshWalletTransferDO.PLATFORM_TRANSFER_NO, walletTransferDO.getPlatformTransferNo())
                .set(ObjectUtil.isNotNull(walletTransferDO.getReason()), GoshWalletTransferDO.REASON, walletTransferDO.getReason())
                .set(ObjectUtil.isNotNull(walletTransferDO.getFinishTime()), GoshWalletTransferDO.FINISH_TIME, walletTransferDO.getFinishTime())
                .eq(GoshWalletTransferDO.TRANSFER_NO, walletTransferDO.getTransferNo()).update();
    }

    @Override
    public List<GoshWalletTransferDO> getBySpanTransferDate(String transferDateStart, String transferDateEnd) {
        return query()
                .between(GoshWalletTransferDO.TRANSFER_DATE, transferDateStart, transferDateEnd)
                .eq(GoshWalletTransferDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .eq(GoshWalletTransferDO.TRANSFER_STATUS, TransferStatusViewEnum.TRANSFER_PROCESSING.getValue())
                .list();
    }

    @Override
    public Long getBySettleSerialNo(String settleSerialNo) {
        return query()
                .eq(GoshWalletTransferDO.SETTLE_SERIAL_NO, settleSerialNo)
                .notIn(GoshWalletTransferDO.TRANSFER_STATUS, TransferStatusViewEnum.TRANSFER_FAIL.getValue())
                .eq(GoshWalletTransferDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .count();
    }
}
