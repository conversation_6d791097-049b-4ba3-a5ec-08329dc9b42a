package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 来逛呗-商家结算数据汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@TableName("gosh_merchant_settle_day_data")
public class GoshMerchantSettleDayDataDO implements Serializable {

    public static final String ID = "id";
    public static final String APP_ID = "app_id";
    public static final String USER_ID = "user_id";
    public static final String STORE_ID = "store_id";
    public static final String CHANNEL_TYPE = "channel_type";
    public static final String PRODUCT_TYPE = "product_type";
    public static final String VERIFY_DAY = "verify_day";
    public static final String SETTLED_AMOUNT = "settled_amount";
    public static final String COUPON_SALE_AMOUNT = "coupon_sale_amount";
    public static final String SERVICE_FEE = "service_fee";
    public static final String PRE_SETTLED_AMOUNT = "pre_settled_amount";
    public static final String SETTLED_CALCULATE_STATUS = "settled_calculate_status";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 集团AppId
     */
    @TableField("app_id")
    private String appId;
    /**
     * 付呗uid
     */
    @TableField("user_id")
    private String userId;
    /**
     * 门店id
     */
    @TableField("store_id")
    private String storeId;
    /**
     * 平台
     */
    @TableField("channel_type")
    private String channelType;
    /**
     * 商品类型:1-团购券;11-代金券;111-商圈卡
     */
    @TableField("product_type")
    private Integer productType;
    /**
     * 核销日期
     */
    @TableField("verify_day")
    private Integer verifyDay;
    /**
     * 结算金额
     */
    @TableField("settled_amount")
    private BigDecimal settledAmount;
    /**
     * 券售卖金额
     */
    @TableField("coupon_sale_amount")
    private BigDecimal couponSaleAmount;
    /**
     * 服务费
     */
    @TableField("service_fee")
    private BigDecimal serviceFee;
    /**
     * 预计结算金额
     */
    @TableField("pre_settled_amount")
    private BigDecimal preSettledAmount;
    /**
     * 结算计算状态 1：待计算（部分结算也算待计算） 2：计算完成
     */
    @TableField("settled_calculate_status")
    private Integer settledCalculateStatus;
    /**
     * 0正常 1删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

}
