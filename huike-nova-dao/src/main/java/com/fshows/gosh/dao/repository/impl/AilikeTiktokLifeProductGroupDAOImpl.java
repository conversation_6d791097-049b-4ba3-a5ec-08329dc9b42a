package com.fshows.gosh.dao.repository.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.AilikeTiktokLifeProductGroupDO;
import com.fshows.gosh.dao.mapper.AilikeTiktokLifeProductGroupMapper;
import com.fshows.gosh.dao.repository.AilikeTiktokLifeProductGroupDAO;
import com.huike.nova.common.constant.CommonConstant;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抖音来客商品组表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Service
public class AilikeTiktokLifeProductGroupDAOImpl extends ServiceImpl<AilikeTiktokLifeProductGroupMapper, AilikeTiktokLifeProductGroupDO> implements AilikeTiktokLifeProductGroupDAO {

    @DS(CommonConstant.READ_ONLY_DB_DATA_SOURCE)
    @Override
    public AilikeTiktokLifeProductGroupDO getProductGroupByQykMerchantCardId(String merchantCardId) {
        return getBaseMapper().getProductGroupByQykMerchantCardId(merchantCardId);
    }
}
