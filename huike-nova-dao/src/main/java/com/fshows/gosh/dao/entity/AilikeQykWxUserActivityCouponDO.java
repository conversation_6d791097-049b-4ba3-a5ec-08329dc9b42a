package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 微信营销活动-用户活动券表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Data
@TableName("ailike_qyk_wx_user_activity_coupon")
public class AilikeQykWxUserActivityCouponDO implements Serializable {

    public static final String ID = "id";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    public static final String USER_ID = "user_id";
    public static final String PRODUCT_ID = "product_id";
    public static final String ACTIVITY_ID = "activity_id";
    public static final String USER_ACTIVITY_RELATION_ID = "user_activity_relation_id";
    public static final String PAY_TIME = "pay_time";
    public static final String VERIFY_TIME = "verify_time";
    public static final String OUT_ORDER_SN = "out_order_sn";
    public static final String COUPON_CODE = "coupon_code";
    public static final String COUPON_STATUS = "coupon_status";
    public static final String COMMISSION_STATUS = "commission_status";
    public static final String COMMISSION_AMOUNT = "commission_amount";
    public static final String IS_DEL = "is_del";
    public static final String OPEN_ID = "open_id";
    public static final String PURCHASE_USER_ID = "purchase_user_id";
    public static final String PURCHASE_OPEN_ID = "purchase_open_id";
    public static final String IS_SETTLEMENT = "is_settlement";
    public static final String SETTLEMENT_TIME = "settlement_time";
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 团长用户 id
     */
    @TableField("user_id")
    private String userId;
    /**
     * 平台商品 id
     */
    @TableField("product_id")
    private String productId;
    /**
     * 活动 id
     */
    @TableField("activity_id")
    private String activityId;
    /**
     * 团长活动关联 id
     */
    @TableField("user_activity_relation_id")
    private String userActivityRelationId;
    /**
     * 下单时间（时间戳（毫秒））
     */
    @TableField("pay_time")
    private String payTime;
    /**
     * 核销时间（时间戳（毫秒））
     */
    @TableField("verify_time")
    private String verifyTime;
    /**
     * 平台订单号
     */
    @TableField("out_order_sn")
    private String outOrderSn;
    /**
     * 券码
     */
    @TableField("coupon_code")
    private String couponCode;
    /**
     * 券状态 1 未核销 2 已核销 3 已退款
     */
    @TableField("coupon_status")
    private Integer couponStatus;
    /**
     * 佣金状态 1 未到账 2 已到账
     */
    @TableField("commission_status")
    private Integer commissionStatus;
    /**
     * 已赚佣金（元）
     */
    @TableField("commission_amount")
    private BigDecimal commissionAmount;
    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 团长用户 openId
     */
    @TableField("open_id")
    private String openId;
    /**
     * 购买者 userId
     */
    @TableField("purchase_user_id")
    private String purchaseUserId;
    /**
     * 购买者 openId
     */
    @TableField("purchase_open_id")
    private String purchaseOpenId;
    /**
     * 是否可以结算 0 默认 1 可以 2 不可以
     */
    @TableField("is_settlement")
    private Integer isSettlement;
    /**
     * 到账时间（时间戳（毫秒））
     */
    @TableField("settlement_time")
    private String settlementTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

}
