package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshShopFreezeLogDO;
import com.fshows.gosh.dao.mapper.GoshShopFreezeLogMapper;
import com.fshows.gosh.dao.repository.GoshShopFreezeLogDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 来逛呗-商铺表结算冻结操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Service
public class GoshShopFreezeLogDAOImpl extends ServiceImpl<GoshShopFreezeLogMapper, GoshShopFreezeLogDO> implements GoshShopFreezeLogDAO {

    /**
     * @param shopId 门店Id
     * @return 数据
     */
    @Override
    public List<GoshShopFreezeLogDO> getFreezeLogList(String shopId) {
        return query().eq(GoshShopFreezeLogDO.SHOP_ID, shopId)
                .eq(GoshShopFreezeLogDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .orderByDesc(GoshShopFreezeLogDO.CREATE_TIME)
                .list();
    }
}
