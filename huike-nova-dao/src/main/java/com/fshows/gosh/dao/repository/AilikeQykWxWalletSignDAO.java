package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.AilikeQykWxWalletSignDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 钱包签约关系记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
public interface AilikeQykWxWalletSignDAO extends IService<AilikeQykWxWalletSignDO> {

    /**
     * 根据入账钱包Id查询钱包签约关系记录
     *
     * @param inWalletId 入账钱包Id
     * @return
     */
    AilikeQykWxWalletSignDO findByInWalletId(String inWalletId);

    /**
     * 根据业务请求签约Id查询钱包签约关系记录
     *
     * @param businessSignId 业务请求签约Id
     * @return
     */
    AilikeQykWxWalletSignDO findByBusinessSignId(String businessSignId);
}
