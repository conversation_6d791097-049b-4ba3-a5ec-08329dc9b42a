/**
 * <AUTHOR>
 * @date 2024/11/22 18:17
 * @version 1.0 GoshOperatorRoleBindGrantDO
 */
package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 *
 *
 * <AUTHOR>
 * @version GoshOperatorRoleBindGrantDO.java, v 0.1 2024-11-22 18:17 tuyuwei
 */
@Data
@TableName("gosh_operator_role_bind_grant")
public class GoshOperatorRoleBindGrantDO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 权限id
     */
    @TableField("grant_id")
    private String grantId;

    /**
     * 角色id
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
}