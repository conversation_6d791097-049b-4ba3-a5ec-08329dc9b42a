/**
 * <AUTHOR>
 * @date 2024/11/1 9:31
 * @version 1.0 GoshOperatorGrantInfoDAOImpl
 */
package com.fshows.gosh.dao.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshOperatorAccountDO;
import com.fshows.gosh.dao.entity.GoshOperatorGrantInfoDO;
import com.fshows.gosh.dao.mapper.GoshOperatorGrantInfoMapper;
import com.fshows.gosh.dao.repository.GoshOperatorGrantInfoDAO;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version GoshOperatorGrantInfoDAOImpl.java, v 0.1 2024-11-01 9:31 tuyuwei
 */
@Service
public class GoshOperatorGrantInfoDAOImpl extends ServiceImpl<GoshOperatorGrantInfoMapper, GoshOperatorGrantInfoDO> implements GoshOperatorGrantInfoDAO {


    @Override
    public List<GoshOperatorGrantInfoDO> getGrantInfoList(Integer accountType) {
        return query()
                .eq(GoshOperatorGrantInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .eq(ObjectUtil.isNotNull(accountType) && CommonConstant.INTEGER_TWO.equals(accountType), GoshOperatorGrantInfoDO.ACCOUNT_TYPE, accountType)
                .list();

    }
}