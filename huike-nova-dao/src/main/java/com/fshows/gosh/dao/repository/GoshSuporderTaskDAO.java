package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.GoshSuporderTaskDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 补单任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface GoshSuporderTaskDAO extends IService<GoshSuporderTaskDO> {

    /**
     * 查询待处理的任务
     *
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @param taskId        任务id
     * @return
     */
    List<GoshSuporderTaskDO> getListByTimeAndTaskId(String startDateTime, String endDateTime, String taskId);

}
