package com.fshows.gosh.dao.repository.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.settleday.QueryDailySettlementListParamDTO;
import com.fshows.gosh.dao.domain.result.settleday.QueryDailySettlementListResultDTO;
import com.fshows.gosh.dao.entity.GoshMerchantSettleDayDataDO;
import com.fshows.gosh.dao.mapper.GoshMerchantSettleDayDataMapper;
import com.fshows.gosh.dao.repository.GoshMerchantSettleDayDataDAO;
import com.huike.nova.common.constant.CommonConstant;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 来逛呗-商家结算数据汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Service
public class GoshMerchantSettleDayDataDAOImpl extends ServiceImpl<GoshMerchantSettleDayDataMapper, GoshMerchantSettleDayDataDO> implements GoshMerchantSettleDayDataDAO {

    /**
     * 查询未结算的数据
     *
     * @param time
     * @param resultHandler
     */
    @Override
    @DS(CommonConstant.READ_ONLY_DB_DATA_SOURCE)
    public void findUnsettledList(Integer time, ResultHandler<GoshMerchantSettleDayDataDO> resultHandler) {
        getBaseMapper().findUnsettledList(time, resultHandler);
    }

    /**
     * 更新数据
     *
     * @param settleDayDataDO
     * @return
     */
    @Override
    @DS(CommonConstant.MASTER)
    public boolean updateSettleDayData(GoshMerchantSettleDayDataDO settleDayDataDO) {
        settleDayDataDO.setUpdateTime(new Date());
        return updateById(settleDayDataDO);
    }

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    @Override
    public boolean batchSettleDayData(List<GoshMerchantSettleDayDataDO> list) {
        return saveBatch(list);
    }

    /**
     * 查询日结算列表
     *
     * @param paramDTO
     * @return
     */
    @Override
    public List<QueryDailySettlementListResultDTO> queryDailySettlementList(QueryDailySettlementListParamDTO paramDTO) {
        return getBaseMapper().queryDailySettlementList(paramDTO);
    }

    /**
     * 自定义汇总结算查询
     *
     * @param paramDTO
     * @return
     */
    @Override
    public QueryDailySettlementListResultDTO getCustomSettlement(QueryDailySettlementListParamDTO paramDTO) {
        return getBaseMapper().getCustomSettlement(paramDTO);
    }

    /**
     * 月汇总结算列表
     *
     * @param paramDTO
     * @return
     */
    @Override
    public List<QueryDailySettlementListResultDTO> queryMonthSettlementList(QueryDailySettlementListParamDTO paramDTO) {
        return getBaseMapper().queryMonthSettlementList(paramDTO);
    }

    /**
     * 结算详情列表
     *
     * @param paramDTO
     * @return
     */
    @Override
    public List<QueryDailySettlementListResultDTO> getSettlementDetailList(QueryDailySettlementListParamDTO paramDTO) {
        return getBaseMapper().getSettlementDetailList(paramDTO);
    }

    /**
     * 账单导出
     *
     * @param paramDTO
     * @return
     */
    @Override
    public List<QueryDailySettlementListResultDTO> exportMinaFinanceReconciliation(QueryDailySettlementListParamDTO paramDTO) {
        return getBaseMapper().exportMinaFinanceReconciliation(paramDTO);
    }

    /**
     * 根据appId和verifyDay查询数量
     *
     * @param appId
     * @param verifyDay
     * @return
     */
    @Override
    public int countByAppIdAndVerifyDay(String appId, Integer verifyDay) {
        return query()
                .eq(GoshMerchantSettleDayDataDO.APP_ID, appId)
                .eq(GoshMerchantSettleDayDataDO.VERIFY_DAY, verifyDay)
                .list().size();
    }

    /**
     * 根据appId和verifyDay删除数据
     *
     * @param appId
     * @param verifyDay
     * @return
     */
    @Override
    public boolean deleteByAppIdAndDay(String appId, Integer verifyDay) {
        return getBaseMapper().deleteByAppIdAndDay(appId, verifyDay);
    }
}
