package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 抖音来客直播数据记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@TableName("ailike_tiktok_live_data")
public class AilikeTiktokLiveDataDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 直播间ID
     */
    @TableField("live_id")
    private String liveId;

    /**
     * 直播间名称
     */
    @TableField("live_name")
    private String liveName;

    /**
     * 抖音号
     */
    @TableField("aweme_user_id")
    private String awemeUserId;

    /**
     * 累计观看人数
     */
    @TableField("watch_uv")
    private Integer watchUv;

    /**
     * 直播开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 直播结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 直播累计时长 单位：秒
     */
    @TableField("live_time")
    private Integer liveTime;

    /**
     * 每分钟在线人数
     */
    @TableField("acu")
    private Integer acu;

    /**
     * 直播间曝光人数
     */
    @TableField("live_show_uv_td")
    private Integer liveShowUvTd;

    /**
     * 人均观看时长（秒）
     */
    @TableField("complex_watch_duration_per_uv")
    private Integer complexWatchDurationPerUv;

    /**
     * 直播间曝光次数
     */
    @TableField("live_show_cnt_td")
    private Integer liveShowCntTd;

    /**
     * 累计观看次数
     */
    @TableField("watch_pv")
    private Integer watchPv;

    /**
     * 商品曝光次数
     */
    @TableField("complex_live_card_shelf_show_pv_td")
    private Integer complexLiveCardShelfShowPvTd;

    /**
     * 商品转化率
     */
    @TableField("complex_order_percent")
    private Integer complexOrderPercent;

    /**
     * 商品点击率
     */
    @TableField("complex_live_card_percent")
    private Integer complexLiveCardPercent;

    /**
     * 商品点击次数
     */
    @TableField("live_shelf_groupon_click_pv")
    private Integer liveShelfGrouponClickPv;

    /**
     * 带货商品数
     */
    @TableField("groupon_cnt")
    private Integer grouponCnt;

    /**
     * 商品曝光人数
     */
    @TableField("live_card_shelf_show_uv_td")
    private Integer liveCardShelfShowUvTd;

    /**
     * 商品点击人数
     */
    @TableField("live_card_shelf_click_uv_td")
    private Integer liveCardShelfClickUvTd;

    /**
     * 成交金额
     */
    @TableField("order_gmv")
    private Integer orderGmv;

    /**
     * 成交人数
     */
    @TableField("order_uv")
    private Integer orderUv;

    /**
     * 成交券数
     */
    @TableField("cert_nums")
    private Integer certNums;

    /**
     * 客单价
     */
    @TableField("complex_order_gmv_per_uv")
    private Integer complexOrderGmvPerUv;

    /**
     * 直播间转化率
     */
    @TableField("complex_order_uv_percent")
    private Integer complexOrderUvPercent;

    /**
     * 退款券数
     */
    @TableField("room_refund_cert_num_td")
    private Integer roomRefundCertNumTd;

    /**
     * 退款金额
     */
    @TableField("room_refund_order_amt_td")
    private Integer roomRefundOrderAmtTd;

    /**
     * 核销券数
     */
    @TableField("room_verify_cert_num_td")
    private Integer roomVerifyCertNumTd;

    /**
     * 核销金额
     */
    @TableField("room_verify_order_amt_td")
    private Integer roomVerifyOrderAmtTd;

    /**
     * 评论次数
     */
    @TableField("comment_pv")
    private Integer commentPv;

    /**
     * 点赞次数
     */
    @TableField("like_pv")
    private Integer likePv;

    /**
     * 关注次数
     */
    @TableField("follow_anchor_pv")
    private Integer followAnchorPv;

    /**
     * 直播日期
     */
    @TableField("run_date")
    private Integer runDate;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getStartTime() {
        if (this.startTime != null) {
          return new Date(this.startTime.getTime());
        } else {
          return null;
        }
    }

    public void setStartTime(Date startTime) {
        if (startTime != null) {
            this.startTime = new Date(startTime.getTime());
        } else {
            this.startTime = null;
        }
    }
    public Date getEndTime() {
        if (this.endTime != null) {
          return new Date(this.endTime.getTime());
        } else {
          return null;
        }
    }

    public void setEndTime(Date endTime) {
        if (endTime != null) {
            this.endTime = new Date(endTime.getTime());
        } else {
            this.endTime = null;
        }
    }
    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String LIVE_ID = "live_id";

    public static final String LIVE_NAME = "live_name";

    public static final String AWEME_USER_ID = "aweme_user_id";

    public static final String WATCH_UV = "watch_uv";

    public static final String START_TIME = "start_time";

    public static final String END_TIME = "end_time";

    public static final String LIVE_TIME = "live_time";

    public static final String ACU = "acu";

    public static final String LIVE_SHOW_UV_TD = "live_show_uv_td";

    public static final String COMPLEX_WATCH_DURATION_PER_UV = "complex_watch_duration_per_uv";

    public static final String LIVE_SHOW_CNT_TD = "live_show_cnt_td";

    public static final String WATCH_PV = "watch_pv";

    public static final String COMPLEX_LIVE_CARD_SHELF_SHOW_PV_TD = "complex_live_card_shelf_show_pv_td";

    public static final String COMPLEX_ORDER_PERCENT = "complex_order_percent";

    public static final String COMPLEX_LIVE_CARD_PERCENT = "complex_live_card_percent";

    public static final String LIVE_SHELF_GROUPON_CLICK_PV = "live_shelf_groupon_click_pv";

    public static final String GROUPON_CNT = "groupon_cnt";

    public static final String LIVE_CARD_SHELF_SHOW_UV_TD = "live_card_shelf_show_uv_td";

    public static final String LIVE_CARD_SHELF_CLICK_UV_TD = "live_card_shelf_click_uv_td";

    public static final String ORDER_GMV = "order_gmv";

    public static final String ORDER_UV = "order_uv";

    public static final String CERT_NUMS = "cert_nums";

    public static final String COMPLEX_ORDER_GMV_PER_UV = "complex_order_gmv_per_uv";

    public static final String COMPLEX_ORDER_UV_PERCENT = "complex_order_uv_percent";

    public static final String ROOM_REFUND_CERT_NUM_TD = "room_refund_cert_num_td";

    public static final String ROOM_REFUND_ORDER_AMT_TD = "room_refund_order_amt_td";

    public static final String ROOM_VERIFY_CERT_NUM_TD = "room_verify_cert_num_td";

    public static final String ROOM_VERIFY_ORDER_AMT_TD = "room_verify_order_amt_td";

    public static final String COMMENT_PV = "comment_pv";

    public static final String LIKE_PV = "like_pv";

    public static final String FOLLOW_ANCHOR_PV = "follow_anchor_pv";

    public static final String RUN_DATE = "run_date";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
