package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 集团服务商金额配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
@TableName("ailike_tiktok_service_price_config")
public class AilikeTiktokServicePriceConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * appId
     */
    @TableField("app_id")
    private String appId;

    /**
     * 通道
     */
    @TableField("channel_type")
    private String channelType;

    /**
     * 商品类型:1-团购券；11-代金券
     */
    @TableField("product_type")
    private Integer productType;

    /**
     * 类目code
     */
    @TableField("category_code")
    private String categoryCode;

    /**
     * 原始金额最小价格
     */
    @TableField("original_min_amount")
    private BigDecimal originalMinAmount;

    /**
     * 原始金额最大价格
     */
    @TableField("original_max_amount")
    private BigDecimal originalMaxAmount;

    /**
     * 实际支付最新金额
     */
    @TableField("actual_min_amount")
    private BigDecimal actualMinAmount;

    /**
     * 是否删除. 1-未删除 2-已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String APP_ID = "app_id";

    public static final String CHANNEL_TYPE = "channel_type";

    public static final String PRODUCT_TYPE = "product_type";

    public static final String CATEGORY_CODE = "category_code";

    public static final String ORIGINAL_MIN_AMOUNT = "original_min_amount";

    public static final String ORIGINAL_MAX_AMOUNT = "original_max_amount";

    public static final String ACTUAL_MIN_AMOUNT = "actual_min_amount";

    public static final String DEL_FLAG = "del_flag";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
