package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.domain.param.OperationAccountRoleListDTO;
import com.fshows.gosh.dao.entity.GoshOperatorBindRoleDO;
import com.fshows.gosh.dao.entity.GoshOperatorGrantInfoDO;
import com.fshows.gosh.dao.entity.GoshOperatorRoleInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0 GoshOperatorBindGrantMapper
 * @date 2024/11/4 13:43
 */
public interface GoshOperatorBindRoleMapper extends BaseMapper<GoshOperatorBindRoleDO> {

    /**
     * 根据运营id查询权限列表
     * @param operatorId 运营id
     * @return
     */
    List<GoshOperatorGrantInfoDO> getGrantByOperatorId(String operatorId);

    /**
     * 根据运营id获取角色信息
     * @param operatorIds
     * @return
     */
    List<OperationAccountRoleListDTO> getRoleInfo(@Param("operatorIds") List<String> operatorIds);


    /**
     * 根据运营id获取角色信息
     * @param operatorId
     * @return
     */
    List<GoshOperatorRoleInfoDO> getByOperatorId(String operatorId);

}
