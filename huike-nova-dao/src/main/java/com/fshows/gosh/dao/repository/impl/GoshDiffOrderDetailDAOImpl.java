package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fshows.gosh.dao.entity.GoshDiffOrderDetailDO;
import com.fshows.gosh.dao.mapper.GoshDiffOrderDetailMapper;
import com.fshows.gosh.dao.repository.GoshDiffOrderDetailDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 来逛呗订单差异表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Service
public class GoshDiffOrderDetailDAOImpl extends ServiceImpl<GoshDiffOrderDetailMapper, GoshDiffOrderDetailDO> implements GoshDiffOrderDetailDAO {

    @Override
    public void deleteByDiffDateAndType(String diffDate, Integer BillType) {
        remove(new QueryWrapper<GoshDiffOrderDetailDO>()
                .eq(GoshDiffOrderDetailDO.DIFF_DATE, diffDate)
                .eq(GoshDiffOrderDetailDO.BILL_TYPE,BillType));
    }

    @Override
    public List<GoshDiffOrderDetailDO> getByDiffDataAndType(String diffDate, Integer BillType, Integer diffType) {
        return query()
                .eq(GoshDiffOrderDetailDO.DIFF_DATE, diffDate)
                .eq(GoshDiffOrderDetailDO.DIFF_TYPE, diffType)
                .eq(GoshDiffOrderDetailDO.BILL_TYPE, BillType).list();
    }
}
