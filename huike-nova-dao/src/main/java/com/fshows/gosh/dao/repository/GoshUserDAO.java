package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.param.PageAccountListParamDTO;
import com.fshows.gosh.dao.domain.result.AccountDetailResultDTO;
import com.fshows.gosh.dao.domain.result.PageAccountListResultDTO;
import com.fshows.gosh.dao.entity.GoshUserDO;
import com.huike.nova.common.metadata.PageParam;

/**
 * <p>
 * 来逛呗-商家小程序用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshUserDAO extends IService<GoshUserDO> {

    /**
     * 根据手机号查询用户信息
     *
     * @param phone 手机号(加密前)
     * @return
     */
    GoshUserDO checkPhone(String phone);

    /**
     * 新增用户
     *
     * @param goshUserDO
     * @return
     */
    boolean saveUser(GoshUserDO goshUserDO);

    /**
     * 根据手机号查询用户信息
     *
     * @param phoneEncode 手机号(加密后)
     * @return
     */
    GoshUserDO getByPhoneEncode(String phoneEncode);

    /**
     * 分页查询员工列表
     *
     * @param pageParam
     * @return
     */
    Page<PageAccountListResultDTO> pageAccountList(PageParam<PageAccountListParamDTO> pageParam);

    /**
     * 查询门店下的手机号数量
     *
     * @param phoneNumber
     * @param shopId
     * @return
     */
    Integer checkPhoneByShopId(String phoneNumber, String shopId);

    /**
     * 根据身份id查询员工信息
     *
     * @param identityId
     * @param shopId
     * @return
     */
    AccountDetailResultDTO getAccountDetail(String identityId, String shopId);

    /**
     * 根据userId查询用户信息
     *
     * @param userId
     * @return
     */
    GoshUserDO getByUserId(String userId);
}
