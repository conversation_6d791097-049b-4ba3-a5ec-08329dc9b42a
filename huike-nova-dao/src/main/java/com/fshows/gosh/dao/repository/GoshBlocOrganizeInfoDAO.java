package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.param.FindPoiOrgListParamDTO;
import com.fshows.gosh.dao.domain.param.OrganizePageListDTO;
import com.fshows.gosh.dao.domain.param.PlatformRolePoiOrgListParamDTO;
import com.fshows.gosh.dao.domain.result.CountSquareResultDTO;
import com.fshows.gosh.dao.domain.result.FindBlocOrgByOrgIdListResultDTO;
import com.fshows.gosh.dao.domain.result.FindByAccountIdListResultDTO;
import com.fshows.gosh.dao.domain.result.FindProductOrgListResultDTO;
import com.fshows.gosh.dao.domain.result.GetOrderOrgInfoResultDTO;
import com.fshows.gosh.dao.domain.result.PlatformRolePoiOrgResultDTO;
import com.fshows.gosh.dao.domain.result.PoiAndOrgNameResultDTO;
import com.fshows.gosh.dao.domain.result.QuerySquareListDTO;
import com.fshows.gosh.dao.entity.GoshBlocOrganizeInfoDO;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.dao.entity.AilikeMerchantStoreDO;

import java.util.List;

/**
 * <p>
 * 来逛呗集团组织信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshBlocOrganizeInfoDAO extends IService<GoshBlocOrganizeInfoDO> {

    /**
     * 组织分页列表
     *
     * @param pageDTO 入参
     * @return 出参
     */
    Page<GoshBlocOrganizeInfoDO> organizePageList(PageParam<OrganizePageListDTO> pageDTO);

    /**
     * 组织列表
     *
     * @param dto 入参
     * @return 出参
     */
    List<GoshBlocOrganizeInfoDO> organizeList(OrganizePageListDTO dto);


    /**
     * 根据orgId查询组织信息
     *
     * @param orgId orgId
     * @return 组织信息
     */
    GoshBlocOrganizeInfoDO getByOrgId(String orgId);

    /**
     * 根据orgId查询orgName
     *
     * @param orgId orgId
     * @return 名称
     */
    String getOrgNameByOrgId(String orgId);

    /**
     * 根据集团id获取顶级组织
     *
     * @param blocId 集团id
     * @return 出参
     */
    GoshBlocOrganizeInfoDO getTopOrgByBlocId(String blocId);

    /**
     * 根据集团 id 列表获取顶级组织
     *
     * @param blocIdList 入参
     * @return 出参
     */
    List<GoshBlocOrganizeInfoDO> getTopOrgList(List<String> blocIdList);

    /**
     * 根据集团id获取组织id列表
     *
     * @param blocId  集团id
     * @param orgType 1组织 2广场
     * @return 出参
     */
    List<String> findOrgIdListByBlocId(String blocId, Integer orgType);

    /**
     * 根据集团id获取所有组织
     *
     * @param blocId 集团id
     * @return 出参
     */
    List<GoshBlocOrganizeInfoDO> findAllOrgByBlocId(String blocId);

    /**
     * 组织列表
     *
     * @param blocId  集团id
     * @param orgPath 组织路径
     * @return 出参
     */
    List<GoshBlocOrganizeInfoDO> findPoiOrgListByFullPath(String blocId, String orgPath, Integer orgType);

    /**
     * 组织列表
     *
     * @param dto 入参
     * @return 出参
     */
    List<GoshBlocOrganizeInfoDO> findPoiOrgList(FindPoiOrgListParamDTO dto);

    /**
     * 根据组织名称列表查询组织信息
     *
     * @param orgNameList 组织名称列表
     * @param blocId      集团id
     * @return 组织信息
     */
    List<GoshBlocOrganizeInfoDO> findByOrgNameList(List<String> orgNameList, String blocId);

    /**
     * 获取账号下集团的所有组织
     *
     * @param blocId
     * @param accountId
     * @param orgId
     * @return
     */
    List<GoshBlocOrganizeInfoDO> getByAccountId(String blocId, String accountId, String orgId);

    /**
     * 获取账号下绑定的组织
     *
     * @param accountIdList 账号id列表
     * @return 组织列表
     */
    List<FindByAccountIdListResultDTO> findByAccountIdList(List<String> accountIdList);

    /**
     * 获取账号下绑定的组织
     *
     * @param accountId 账号id
     * @return 组织列表
     */
    List<FindByAccountIdListResultDTO> findByAccountId(String accountId);

    /**
     * 根据集团id和组织名称查询组织信息
     *
     * @param blocId  集团id
     * @param orgName 组织名称
     * @return 组织信息
     */
    GoshBlocOrganizeInfoDO getByOrgName(String blocId, String orgName);

    /**
     * 根据组织id删除组织
     *
     * @param orgId 组织id
     */
    void deleteByOrgId(String orgId);

    /**
     * 根据组织id列表查询组织广场数量
     *
     * @param orgIdList 组织id列表
     * @return 组织数量
     */
    List<CountSquareResultDTO> countSquareCountByOrgIdList(List<String> orgIdList);

    /**
     * 根据组织id列表查询组织信息
     *
     * @param orgIdList 组织id列表
     * @return 组织列表
     */
    List<GoshBlocOrganizeInfoDO> findByOrgIdList(List<String> orgIdList);

    /**
     * 根据组织id列表查询组织集团信息
     *
     * @param orgIdList 组织id列表
     * @return 组织列表
     */
    List<FindBlocOrgByOrgIdListResultDTO> findBlocOrgByOrgIdList(List<String> orgIdList);

    /**
     * 根据组织id获取广场列表信息
     *
     * @param orgId 组织id
     * @return 广场信息列表
     */
    List<QuerySquareListDTO> querySquareList(String orgId, String blocId);

    /**
     * 根据组织id获取广场列表信息
     *
     * @param orgId 组织id
     * @return 广场信息列表
     */
    List<GoshBlocOrganizeInfoDO> findSquareList(String orgId, String blocId);


    /**
     * 查询下级组织名称和poiId
     *
     * @param blocId
     * @param orgPath
     * @param orgType
     * @return
     */
    List<PoiAndOrgNameResultDTO> findPoiAndOrgNameByFullPath(String blocId, String orgPath, Integer orgType);

    /**
     * 根据组织id列表查询店铺id列表
     *
     * @param orgIdList 组织id列表
     * @return 店铺id列表
     */
    List<String> getStoreIdListByOrgIdList(List<String> orgIdList);

    /**
     * 根据店铺id列表查询组织信息
     *
     * @param storeIdList 店铺id列表
     * @return 组织信息
     */
    List<GoshBlocOrganizeInfoDO> getByStoreIdList(List<String> storeIdList);

    /**
     * 根据店铺id查询组织信息
     *
     * @param storeId 店铺id
     * @return 组织信息
     */
    GoshBlocOrganizeInfoDO getByStoreId(String storeId);


    /**
     * 查询有平台权限的组织
     *
     * @param dto 入参
     * @return 出参
     */
    List<PlatformRolePoiOrgResultDTO> findPlatformRolePoiOrgList(PlatformRolePoiOrgListParamDTO dto);

    /**
     * 查询有权限的组织列表
     *
     * @param dto 入参
     * @return 出参
     */
    List<PlatformRolePoiOrgResultDTO> findRolePoiOrgList(PlatformRolePoiOrgListParamDTO dto);

    /**
     * 根据组织id查询店铺信息
     *
     * @param orgId
     * @return
     */
    AilikeMerchantStoreDO getMerchantStoreInfoByOrgId(String orgId);

    /**
     * 根据业务商品id查询组织信息
     *
     * @param businessProductIdList 商品id列表
     * @param merchantId
     * @return
     */
    List<FindProductOrgListResultDTO> findProductOrgList(List<String> businessProductIdList, String merchantId);

    /**
     * 根据组织id列表查询商品id列表
     *
     * @param orgIdList 组织id列表
     * @return 商品id列表
     */
    List<String> getProductIdListByPrgIdList(List<String> orgIdList);

    /**
     * 根据商品id查询组织信息
     *
     * @param businessProductId 商品id
     * @return 组织信息
     */
    GetOrderOrgInfoResultDTO getOrderOrgInfo(String businessProductId, String merchantId);
}
