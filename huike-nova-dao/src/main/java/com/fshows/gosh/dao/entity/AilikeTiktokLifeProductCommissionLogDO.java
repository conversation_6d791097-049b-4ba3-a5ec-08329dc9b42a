package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 抖音来客商品佣金变更记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Data
@TableName("ailike_tiktok_life_product_commission_log")
public class AilikeTiktokLifeProductCommissionLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务id
     */
    @TableField("commission_log_id")
    private String commissionLogId;

    /**
     * 代理商与mcn关联ID
     */
    @TableField("agent_mcn_id")
    private String agentMcnId;

    /**
     * 商品id
     */
    @TableField("product_id")
    private String productId;

    /**
     * 变更前佣金
     */
    @TableField("before_commission")
    private Integer beforeCommission;

    /**
     * 变更后佣金
     */
    @TableField("after_commission")
    private Integer afterCommission;

    /**
     * 生效时间
     */
    @TableField("effective_time")
    private Integer effectiveTime;

    /**
     * 佣金状态 1待确认 2已确认 3已拒绝  902平台设置失败
     */
    @TableField("commission_status")
    private Integer commissionStatus;

    /**
     * 操作人id
     */
    @TableField("operator_uid")
    private String operatorUid;

    /**
     * 操作人名称
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 是否删除，1-未删除；2-已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 商品业务id
     */
    @TableField("business_product_id")
    private String businessProductId;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String COMMISSION_LOG_ID = "commission_log_id";

    public static final String AGENT_MCN_ID = "agent_mcn_id";

    public static final String PRODUCT_ID = "product_id";

    public static final String BEFORE_COMMISSION = "before_commission";

    public static final String AFTER_COMMISSION = "after_commission";

    public static final String EFFECTIVE_TIME = "effective_time";

    public static final String COMMISSION_STATUS = "commission_status";

    public static final String OPERATOR_UID = "operator_uid";

    public static final String OPERATOR_NAME = "operator_name";

    public static final String DEL_FLAG = "del_flag";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String BUSINESS_PRODUCT_ID = "business_product_id";

}
