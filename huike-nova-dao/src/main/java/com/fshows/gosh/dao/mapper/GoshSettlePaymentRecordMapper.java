package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.domain.result.SettlePaymentRecordDTO;
import com.fshows.gosh.dao.entity.GoshSettlePaymentRecordDO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 入金记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
public interface GoshSettlePaymentRecordMapper extends BaseMapper<GoshSettlePaymentRecordDO> {

    BigDecimal sumSettlePaymentRecord(@Param("walletId") String walletId, @Param("tradeDate") String tradeDate, @Param("remark") String remark);

    /**
     * 查询集团入金列表
     *
     * @param settleDay    入金日期
     * @param platformType 平台类型
     * @param balanceType  钱包类型
     * @return
     */
    List<SettlePaymentRecordDTO> getSettlePaymentRecord(@Param("settleDay") String settleDay,
                                                        @Param("platformType") String platformType,
                                                        @Param("balanceType") String balanceType);
}
