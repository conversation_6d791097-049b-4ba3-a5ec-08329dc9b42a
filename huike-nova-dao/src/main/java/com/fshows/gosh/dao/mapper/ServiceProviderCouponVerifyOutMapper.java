package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.domain.result.OutProductSettleAmountResultDTO;
import com.fshows.gosh.dao.entity.ServiceProviderCouponVerifyOutDO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 外部商品服务商核销卡券记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
public interface ServiceProviderCouponVerifyOutMapper extends BaseMapper<ServiceProviderCouponVerifyOutDO> {
    /**
     * @param appId       appId
     * @param settleDay   结算日
     * @param poiIdList   广场列表
     * @param channelType 通道
     */
    OutProductSettleAmountResultDTO statisticsSettleAmount(@Param("appId") String appId,
                                                           @Param("settleDay") Integer settleDay,
                                                           @Param("poiIdList") List<String> poiIdList,
                                                           @Param("channelType") String channelType,
                                                           @Param("channelCode") String channelCode);

    /**
     * 根据appid统计差异金额
     *
     * @param appId       appId
     * @param settleDay   结算日
     * @param channelType 通道
     * @param dataType    数据类型
     */
    BigDecimal statisticsDifferenceAmount(@Param("appId") String appId,
                                          @Param("settleDay") Integer settleDay,
                                          @Param("channelType") String channelType,
                                          @Param("channelCode") String channelCode,
                                          @Param("dataType") String dataType,
                                          @Param("orgProductType") Integer orgProductType);

    /**
     * 修改差异数据的结算单号
     *
     * @param appId       appId
     * @param settleDay   结算日
     * @param channelType 通道
     */
    void modifySerialNo(@Param("appId") String appId,
                        @Param("settleDay") Integer settleDay,
                        @Param("channelType") String channelType,
                        @Param("channelCode") String channelCode,
                        @Param("serialNo") String serialNo);
}
