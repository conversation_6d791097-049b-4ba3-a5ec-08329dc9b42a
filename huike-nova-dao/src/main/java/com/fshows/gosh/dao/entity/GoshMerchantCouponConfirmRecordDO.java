package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 商家券（子券）货盘确认记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Data
@TableName("gosh_merchant_coupon_confirm_record")
public class GoshMerchantCouponConfirmRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 商铺id
     */
    @TableField("shop_id")
    private String shopId;


    /**
     * 确认时间（秒级时间戳）
     */
    @TableField("confirm_time")
    private Integer confirmTime;

    /**
     * 确认状态 1待确认 2已确认
     */
    @TableField("confirm_status")
    private Integer confirmStatus;

    /**
     * 业务商品Id(子券)
     */
    @TableField("business_product_id")
    private String businessProductId;

    /**
     * 母券的业务商品Id
     */
    @TableField("parent_business_product_id")
    private String parentBusinessProductId;

    /**
     * 0正常 1删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 1未弹窗 2已弹窗
     */
    @TableField("popup_status")
    private Integer popupStatus;

    /**
     * 组织id（广场id）
     */
    @TableField("org_id")
    private String orgId;

    /**
     * 记录id
     */
    @TableField("record_id")
    private String recordId;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String SHOP_ID = "shop_id";

    public static final String CONFIRM_TIME = "confirm_time";

    public static final String CONFIRM_STATUS = "confirm_status";

    public static final String BUSINESS_PRODUCT_ID = "business_product_id";

    public static final String PARENT_BUSINESS_PRODUCT_ID = "parent_business_product_id";

    public static final String IS_DEL = "is_del";

    public static final String ORG_ID = "org_id";

    public static final String POPUP_STATUS = "popup_status";

    public static final String RECORD_ID = "record_id";

}
