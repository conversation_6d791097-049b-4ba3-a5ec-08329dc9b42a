package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.result.RoleAccountCountResultDTO;
import com.fshows.gosh.dao.entity.GoshBlocAccountBindRoleDO;
import com.fshows.gosh.dao.mapper.GoshBlocAccountBindRoleMapper;
import com.fshows.gosh.dao.repository.GoshBlocAccountBindRoleDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 账号和角色关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class GoshBlocAccountBindRoleDAOImpl extends ServiceImpl<GoshBlocAccountBindRoleMapper, GoshBlocAccountBindRoleDO> implements GoshBlocAccountBindRoleDAO {

    /**
     * 根据组织id和账号查询角色列表
     *
     * @param orgId
     * @param accountId
     * @return
     */
    @Override
    public List<String> getRoleListByAccountAndOrgId(String orgId, String accountId) {
        return getBaseMapper().getRoleListByAccountAndOrgId(orgId, accountId);
    }

    /**
     * 根据角色id列表查询角色下账号数量
     *
     * @param roleIdList
     * @return
     */
    @Override
    public List<RoleAccountCountResultDTO> countByRoleIdList(List<String> roleIdList) {
        return getBaseMapper().countByRoleIdList(roleIdList);
    }

    /**
     * 根据角色id查询角色下账号数量
     *
     * @param roleId
     * @return
     */
    @Override
    public Integer countByRoleId(String roleId) {
        return query().eq(GoshBlocAccountBindRoleDO.ROLE_ID, roleId).eq(GoshBlocAccountBindRoleDO.IS_DEL, 0).count().intValue();
    }

    /**
     * 根据账号id查询角色列表
     *
     * @param accountId
     * @return
     */
    @Override
    public List<GoshBlocAccountBindRoleDO> getByAccountId(String accountId) {
        return query().eq(GoshBlocAccountBindRoleDO.ACCOUNT_ID, accountId).eq(GoshBlocAccountBindRoleDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }

    /**
     * 根据角色id查询角色下账号id
     *
     * @param roleId 角色id
     * @return 账号绑定角色列表
     */
    @Override
    public List<String> getByRoleId(String roleId) {
        return getBaseMapper().getByRoleId(roleId);
    }

    /**
     * 根据账号id删除
     *
     * @param accountId 账号id
     */
    @Override
    public void deleteByAccountId(String accountId) {
        update().set(GoshBlocAccountBindRoleDO.IS_DEL, DelFlagEnum.DEL.getValue()).eq(GoshBlocAccountBindRoleDO.ACCOUNT_ID, accountId).eq(GoshBlocAccountBindRoleDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).update();
    }
}
