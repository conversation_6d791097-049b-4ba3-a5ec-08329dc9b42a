package com.fshows.gosh.dao.repository.impl;

import com.fshows.gosh.dao.entity.GoshSuporderTaskDO;
import com.fshows.gosh.dao.mapper.GoshSuporderTaskMapper;
import com.fshows.gosh.dao.repository.GoshSuporderTaskDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.enums.SupOrderTaskStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 补单任务表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Service
public class GoshSuporderTaskDAOImpl extends ServiceImpl<GoshSuporderTaskMapper, GoshSuporderTaskDO> implements GoshSuporderTaskDAO {

    /**
     * 查询待处理的任务
     *
     * @param startDateTime 开始时间
     * @param endDateTime   结束时间
     * @param taskId        任务id
     * @return
     */
    @Override
    public List<GoshSuporderTaskDO> getListByTimeAndTaskId(String startDateTime, String endDateTime, String taskId) {
        return query().between(StringUtils.isNotBlank(startDateTime) && StringUtils.isNotBlank(endDateTime), GoshSuporderTaskDO.CREATE_TIME, startDateTime, endDateTime)
                .eq(StringUtils.isNotBlank(taskId), GoshSuporderTaskDO.TASK_ID, taskId)
                .in(GoshSuporderTaskDO.TASK_STATUS, Arrays.asList(SupOrderTaskStatusEnum.INIT.getValue(), SupOrderTaskStatusEnum.PROCESSING.getValue())).list();
    }
}
