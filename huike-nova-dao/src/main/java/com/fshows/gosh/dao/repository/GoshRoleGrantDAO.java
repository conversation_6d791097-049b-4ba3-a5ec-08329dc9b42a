package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.result.RoleGrantResultDTO;
import com.fshows.gosh.dao.entity.GoshGrantDO;
import com.fshows.gosh.dao.entity.GoshRoleGrantDO;

import java.util.List;

/**
 * <p>
 * 来逛呗角色权限关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshRoleGrantDAO extends IService<GoshRoleGrantDO> {

    /**
     * 根据角色id查询权限列表
     *
     * @param roleId
     * @return
     */
    List<GoshGrantDO> queryGrantByRoleId(String roleId);

    /**
     * 根据角色id列表查询权限列表
     *
     * @param roleIds
     * @return
     */
    List<RoleGrantResultDTO> findGrantByRoleIds(List<String> roleIds);

    /**
     * 批量添加角色权限信息
     *
     * @param list
     * @return
     */
    boolean batchRoleGrant(List<GoshRoleGrantDO> list);

    /**
     * 删除角色权限
     *
     * @param roleId
     * @return
     */
    boolean deleteRoleGrant(String roleId);
}
