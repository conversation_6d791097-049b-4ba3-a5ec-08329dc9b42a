package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshBlocAlipayAntStoreApplyDO;

import java.util.List;

/**
 * <p>
 * 来逛呗蚂蚁门店申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
public interface GoshBlocAlipayAntStoreApplyDAO extends IService<GoshBlocAlipayAntStoreApplyDO> {

    /**
     * 根据storeId和pid查询蚂蚁门店申请单
     *
     * @param pid
     * @param storeId
     * @return
     */
    GoshBlocAlipayAntStoreApplyDO getAntShopByStoreIdAndPid(String pid, String storeId);

    /**
     * 保存蚂蚁门店申请单
     *
     * @param storeApplyDO
     * @return
     */
    boolean saveAntShopApply(GoshBlocAlipayAntStoreApplyDO storeApplyDO);

    /**
     * 更新蚂蚁门店申请单
     *
     * @param blocAlipayAntStoreApplyDO
     * @return
     */
    boolean updateShopInfoByStoreIdAndPid(GoshBlocAlipayAntStoreApplyDO blocAlipayAntStoreApplyDO);

    /**
     * 根据状态的查询蚂蚁门店申请单
     *
     * @param shopStatus
     * @return
     */
    List<GoshBlocAlipayAntStoreApplyDO> findCreatingAntShopList(Integer shopStatus);
}
