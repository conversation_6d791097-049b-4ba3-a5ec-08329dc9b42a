package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.GoshMotherCouponComplateCommissionDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 母券对应子券消费或过期赚的佣金差记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface GoshMotherCouponComplateCommissionDAO extends IService<GoshMotherCouponComplateCommissionDO> {

    /**
     * 查询母券的利润
     *
     * @param settleDay   结算日
     * @param channelType 平台
     * @return
     */
    List<GoshMotherCouponComplateCommissionDO> getMotherCouponProfit(String appId, Integer settleDay, String channelType);

    /**
     * 根据过期日期删除佣金利润
     *
     * @param expireDay 过期日期
     * @return
     */
    void deleteByJobExpireDay(Integer expireDay);

    /**
     * 查询母券利润表中结算金额为0的数据
     *
     * @param startSettleDay 开始结算日期
     * @param endSettleDay   结束结算日期
     * @return
     */
    List<GoshMotherCouponComplateCommissionDO> getNullSettleAmountMotherCouponProfit(Integer startSettleDay, Integer endSettleDay);

    /**
     * 券码数据
     *
     * @param couponCode 券码
     * @return
     */
    GoshMotherCouponComplateCommissionDO findByCouponCodeCount(String couponCode);
}
