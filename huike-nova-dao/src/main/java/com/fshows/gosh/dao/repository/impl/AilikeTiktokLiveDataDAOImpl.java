package com.fshows.gosh.dao.repository.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.LiveDataParamDTO;
import com.fshows.gosh.dao.domain.result.LiveDataInfoResultDTO;
import com.fshows.gosh.dao.domain.result.LiveDataSummaryResultDTO;
import com.fshows.gosh.dao.entity.AilikeTiktokLiveDataDO;
import com.fshows.gosh.dao.mapper.AilikeTiktokLiveDataMapper;
import com.fshows.gosh.dao.repository.AilikeTiktokLiveDataDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.metadata.PageParam;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抖音来客直播数据记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
public class AilikeTiktokLiveDataDAOImpl extends ServiceImpl<AilikeTiktokLiveDataMapper, AilikeTiktokLiveDataDO> implements AilikeTiktokLiveDataDAO {

    /**
     * 直播数据汇总
     *
     * @param dto
     * @return
     */
    @Override
    @DS(CommonConstant.READ_ONLY_DB_DATA_SOURCE)
    public LiveDataSummaryResultDTO liveDataSummary(LiveDataParamDTO dto) {
        return getBaseMapper().liveDataSummary(dto);
    }

    /**
     * 分页查询直播列表数据
     *
     * @param pageParam
     * @return
     */
    @Override
    @DS(CommonConstant.READ_ONLY_DB_DATA_SOURCE)
    public Page<LiveDataInfoResultDTO> pageLiveData(PageParam<LiveDataParamDTO> pageParam) {
        Page<LiveDataInfoResultDTO> page = new Page<>();
        page.setSize(pageParam.getPageSize());
        page.setCurrent(pageParam.getPage());
        return getBaseMapper().pageLiveData(page, pageParam.getQuery());
    }

}
