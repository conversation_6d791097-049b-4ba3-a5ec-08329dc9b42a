package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshMerchantCouponConfirmRecordDO;
import com.fshows.gosh.dao.mapper.GoshMerchantCouponConfirmRecordMapper;
import com.fshows.gosh.dao.repository.GoshMerchantCouponConfirmRecordDAO;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商家券（子券）货盘确认记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-23
 */
@Service
public class GoshMerchantCouponConfirmRecordDAOImpl extends ServiceImpl<GoshMerchantCouponConfirmRecordMapper, GoshMerchantCouponConfirmRecordDO> implements GoshMerchantCouponConfirmRecordDAO {

    /**
     * 根据商铺id获取最新未确认记录
     *
     * @param shopId 门店id
     * @return 未确认记录
     */
    @Override
    public GoshMerchantCouponConfirmRecordDO getTheLatestNotConfirmRecordByShopId(String shopId) {
        return query()
                .eq(GoshMerchantCouponConfirmRecordDO.SHOP_ID, shopId)
                .eq(GoshMerchantCouponConfirmRecordDO.CONFIRM_STATUS, CommonConstant.ONE)
                .eq(GoshMerchantCouponConfirmRecordDO.IS_DEL, CommonConstant.ZERO)
                .orderByDesc(GoshMerchantCouponConfirmRecordDO.ID)
                .last("limit 1")
                .one();
    }

    /**
     * 根据商铺id获取最新确认记录
     *
     * @param shopId 门店id
     * @return 未确认记录
     */
    @Override
    public GoshMerchantCouponConfirmRecordDO getTheLatestConfirmRecordByShopId(String shopId) {
        return query()
                .eq(GoshMerchantCouponConfirmRecordDO.SHOP_ID, shopId)
                .eq(GoshMerchantCouponConfirmRecordDO.CONFIRM_STATUS, CommonConstant.INTEGER_TWO)
                .eq(GoshMerchantCouponConfirmRecordDO.IS_DEL, CommonConstant.ZERO)
                .orderByDesc(GoshMerchantCouponConfirmRecordDO.ID)
                .last("limit 1")
                .one();
    }

    /**
     * 根据商铺id获取最新弹窗记录
     *
     * @param shopId 门店id
     * @return 未弹窗记录
     */
    @Override
    public GoshMerchantCouponConfirmRecordDO getTheLatestNotPopUpRecordByShopId(String shopId) {
        return query()
                .eq(GoshMerchantCouponConfirmRecordDO.SHOP_ID, shopId)
                .eq(GoshMerchantCouponConfirmRecordDO.IS_DEL, CommonConstant.ZERO)
                .eq(GoshMerchantCouponConfirmRecordDO.POPUP_STATUS, CommonConstant.ONE)
                .orderByDesc(GoshMerchantCouponConfirmRecordDO.ID)
                .last("limit 1")
                .one();
    }

    /**
     * 根据记录id修改记录弹窗状态
     *
     * @param recordId 记录id
     */
    @Override
    public void updatePopUpStatusByRecordId(String recordId) {
        update().set(GoshMerchantCouponConfirmRecordDO.POPUP_STATUS, CommonConstant.INTEGER_TWO)
                .eq(GoshMerchantCouponConfirmRecordDO.IS_DEL, CommonConstant.ZERO)
                .eq(GoshMerchantCouponConfirmRecordDO.RECORD_ID, recordId)
                .update();
    }

    /**
     * 根据记录id修改记录确认状态
     *
     * @param recordId 记录id
     */
    @Override
    public void confirmRecordByRecordId(String recordId, Integer confirmStatus) {
        update().set(GoshMerchantCouponConfirmRecordDO.CONFIRM_STATUS, confirmStatus)
                .eq(GoshMerchantCouponConfirmRecordDO.IS_DEL, CommonConstant.ZERO)
                .eq(GoshMerchantCouponConfirmRecordDO.RECORD_ID, recordId)
                .update();
    }

    /**
     * 根据母券的业务商品Id删除货盘确认记录
     *
     * @param parentBusinessProductId 母券的业务商品Id
     */
    @Override
    public void deleteByParentBusinessProductId(String parentBusinessProductId) {
        update().set(GoshMerchantCouponConfirmRecordDO.IS_DEL, DelFlagEnum.DEL.getValue())
                .eq(GoshMerchantCouponConfirmRecordDO.PARENT_BUSINESS_PRODUCT_ID, parentBusinessProductId)
                .update();
    }

    /**
     * 根据业务商品Id删除货盘确认记录
     *
     * @param businessProductId 业务商品Id
     */
    @Override
    public void deleteByBusinessProductId(String businessProductId) {
        update().set(GoshMerchantCouponConfirmRecordDO.IS_DEL, DelFlagEnum.DEL.getValue())
                .eq(GoshMerchantCouponConfirmRecordDO.BUSINESS_PRODUCT_ID, businessProductId)
                .update();
    }
}
