package com.fshows.gosh.dao.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.OrderDataSummaryPoiProductParamDTO;
import com.fshows.gosh.dao.entity.AilikeTiktokLifeProductPoiDO;
import com.fshows.gosh.dao.mapper.AilikeTiktokLifeProductPoiMapper;
import com.fshows.gosh.dao.repository.AilikeTiktokLifeProductPoiDAO;
import com.huike.nova.common.enums.DelStatusEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 抖音来客商品适用门店列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Service
public class AilikeTiktokLifeProductPoiDAOImpl extends ServiceImpl<AilikeTiktokLifeProductPoiMapper, AilikeTiktokLifeProductPoiDO> implements AilikeTiktokLifeProductPoiDAO {

    @Override
    public List<String> getBusinessProductIdByPoiId(OrderDataSummaryPoiProductParamDTO dto) {
        return getBaseMapper().getBusinessProductIdByPoiId(dto);
    }

    @Override
    public AilikeTiktokLifeProductPoiDO getOneByBusinessProductId(String businessProductId) {
        return lambdaQuery()
                .eq(AilikeTiktokLifeProductPoiDO::getBusinessProductId, businessProductId)
                .eq(AilikeTiktokLifeProductPoiDO::getDelFlag, DelStatusEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 根据业务商品Id列表查询POI信息
     *
     * @param businessProductIdList 商品id 列表
     * @return 结果
     */
    @Override
    public List<AilikeTiktokLifeProductPoiDO> getByBusinessProductIdList(List<String> businessProductIdList) {
        if (CollectionUtil.isEmpty(businessProductIdList)) {
            return CollectionUtil.newArrayList();
        }
        return lambdaQuery().in(AilikeTiktokLifeProductPoiDO::getBusinessProductId, businessProductIdList)
                .list();
    }
}
