package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 角色信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Data
@TableName("gosh_bloc_role_info")
public class GoshBlocRoleInfoDO implements Serializable {

    public static final String ID = "id";
    public static final String BLOC_ID = "bloc_id";
    public static final String ROLE_ID = "role_id";
    public static final String ROLE_NAME = "role_name";
    public static final String ROLE_STATUS = "role_status";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    public static final String CREATE_ACCOUNT = "create_account";
    public static final String UPDATE_ACCOUNT = "update_account";
    public static final String REMARK = "remark";
    public static final String ORG_ID = "org_id";
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 集团id
     */
    @TableField("bloc_id")
    private String blocId;
    /**
     * 组织id
     */
    @TableField("org_id")
    private String orgId;
    /**
     * 角色id
     */
    @TableField("role_id")
    private String roleId;
    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;
    /**
     * 状态:1正常,2禁用
     */
    @TableField("role_status")
    private Integer roleStatus;
    /**
     * 是否删除:0未删除,1已删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 创建者
     */
    @TableField("create_account")
    private String createAccount;
    /**
     * 更新者
     */
    @TableField("update_account")
    private String updateAccount;
    /**
     * 角色备注
     */
    @TableField("remark")
    private String remark;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

}
