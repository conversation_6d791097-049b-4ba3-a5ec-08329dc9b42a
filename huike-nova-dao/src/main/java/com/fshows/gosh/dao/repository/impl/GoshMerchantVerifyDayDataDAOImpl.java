package com.fshows.gosh.dao.repository.impl;

import com.fshows.gosh.dao.domain.param.VerifyDataSummaryParamDTO;
import com.fshows.gosh.dao.domain.result.ChannelTypeVerifySummaryResultDTO;
import com.fshows.gosh.dao.domain.result.ProductTypeVerifySummaryResultDTO;
import com.fshows.gosh.dao.domain.result.VerifyDataSummaryResultDTO;
import com.fshows.gosh.dao.domain.result.VerifyDaySummaryResultDTO;
import com.fshows.gosh.dao.entity.GoshMerchantVerifyDayDataDO;
import com.fshows.gosh.dao.mapper.GoshMerchantVerifyDayDataMapper;
import com.fshows.gosh.dao.repository.GoshMerchantVerifyDayDataDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 来逛呗-商家核销数据汇总 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class GoshMerchantVerifyDayDataDAOImpl extends ServiceImpl<GoshMerchantVerifyDayDataMapper, GoshMerchantVerifyDayDataDO> implements GoshMerchantVerifyDayDataDAO {

    /**
     * 统计汇总记录数
     *
     * @param appId
     * @param verifyDay
     * @return
     */
    @Override
    public int getCountByAppIdAndDay(String appId, Integer verifyDay) {
        return query().eq(GoshMerchantVerifyDayDataDO.APP_ID, appId)
                .eq(GoshMerchantVerifyDayDataDO.VERIFY_DAY, verifyDay)
                .eq(GoshMerchantVerifyDayDataDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .list().size();
    }

    /**
     * 删除日汇总数据
     *
     * @param appId
     * @param verifyDay
     * @return
     */
    @Override
    public boolean deleteByAppIdAndDay(String appId, Integer verifyDay) {
        return getBaseMapper().deleteByAppIdAndDay(appId, verifyDay);
    }

    /**
     * 核销汇总数据
     *
     * @param dto
     */
    @Override
    public VerifyDataSummaryResultDTO verifyTotalSummary(VerifyDataSummaryParamDTO dto) {
        return getBaseMapper().verifyTotalSummary(dto);
    }

    /**
     * 核销日汇总数据
     *
     * @param dto
     */
    @Override
    public List<VerifyDaySummaryResultDTO> verifyDaySummary(VerifyDataSummaryParamDTO dto) {
        return getBaseMapper().verifyDaySummary(dto);
    }

    /**
     * 按通道统计核销汇总数据
     *
     * @param dto
     * @return
     */
    @Override
    public List<ChannelTypeVerifySummaryResultDTO> channelTypeVerifySummary(VerifyDataSummaryParamDTO dto) {
        return getBaseMapper().channelTypeVerifySummary(dto);
    }

    /**
     * 按商品类型统计核销汇总数据
     *
     * @param dto
     * @return
     */
    @Override
    public List<ProductTypeVerifySummaryResultDTO> productTypeVerifySummary(VerifyDataSummaryParamDTO dto) {
        return getBaseMapper().productTypeVerifySummary(dto);
    }
}
