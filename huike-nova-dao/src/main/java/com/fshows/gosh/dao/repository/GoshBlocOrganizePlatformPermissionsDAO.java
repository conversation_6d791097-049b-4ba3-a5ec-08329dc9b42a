package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshBlocOrganizePlatformPermissionsDO;

import java.util.List;

/**
 * <p>
 * 组织平台权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshBlocOrganizePlatformPermissionsDAO extends IService<GoshBlocOrganizePlatformPermissionsDO> {

    /**
     * 根据组织id查询组织平台权限
     *
     * @param orgId 组织id
     * @return 组织平台权限
     */
    List<GoshBlocOrganizePlatformPermissionsDO> findByOrgId(String orgId);

    /**
     * 根据组织id列表查询组织平台权限
     *
     * @param orgIdList 组织id列表
     * @return 组织平台权限
     */
    List<GoshBlocOrganizePlatformPermissionsDO> findByOrgIdList(List<String> orgIdList);

    /**
     * 根据组织id列表删除组织平台权限
     *
     * @param orgIdList 组织id列表
     */
    void deleteByOrgIdList(List<String> orgIdList);
}
