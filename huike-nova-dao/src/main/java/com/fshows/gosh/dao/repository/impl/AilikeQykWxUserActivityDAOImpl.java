package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.AilikeQykWxUserActivityDO;
import com.fshows.gosh.dao.mapper.AilikeQykWxUserActivityMapper;
import com.fshows.gosh.dao.repository.AilikeQykWxUserActivityDAO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 微信营销活动-用户活动表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Service
public class AilikeQykWxUserActivityDAOImpl extends ServiceImpl<AilikeQykWxUserActivityMapper, AilikeQykWxUserActivityDO> implements AilikeQykWxUserActivityDAO {

    /**
     * 推广单商品统计
     *
     * @param userActivityRelationId 用户活动关联 id
     * @return 推广单商品统计信息
     */
    @Override
    public AilikeQykWxUserActivityDO queryUserActivityStatistics(String userActivityRelationId) {
        return getBaseMapper().queryUserActivityStatistics(userActivityRelationId);
    }

}
