package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.FindPoiOrgListParamDTO;
import com.fshows.gosh.dao.domain.param.OrganizePageListDTO;
import com.fshows.gosh.dao.domain.param.PlatformRolePoiOrgListParamDTO;
import com.fshows.gosh.dao.domain.result.CountSquareResultDTO;
import com.fshows.gosh.dao.domain.result.FindBlocOrgByOrgIdListResultDTO;
import com.fshows.gosh.dao.domain.result.FindByAccountIdListResultDTO;
import com.fshows.gosh.dao.domain.result.FindProductOrgListResultDTO;
import com.fshows.gosh.dao.domain.result.GetOrderOrgInfoResultDTO;
import com.fshows.gosh.dao.domain.result.PlatformRolePoiOrgResultDTO;
import com.fshows.gosh.dao.domain.result.PoiAndOrgNameResultDTO;
import com.fshows.gosh.dao.domain.result.QuerySquareListDTO;
import com.fshows.gosh.dao.entity.GoshBlocOrganizeInfoDO;
import com.fshows.gosh.dao.mapper.GoshBlocOrganizeInfoMapper;
import com.fshows.gosh.dao.repository.GoshBlocOrganizeInfoDAO;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.dao.entity.AilikeMerchantStoreDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 来逛呗集团组织信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Service
public class GoshBlocOrganizeInfoDAOImpl extends ServiceImpl<GoshBlocOrganizeInfoMapper, GoshBlocOrganizeInfoDO> implements GoshBlocOrganizeInfoDAO {

    /**
     * 组织分页列表
     *
     * @param pageDTO 入参
     * @return 出参
     */
    @Override
    public Page<GoshBlocOrganizeInfoDO> organizePageList(PageParam<OrganizePageListDTO> pageDTO) {
        Page<OrganizePageListDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());
        return getBaseMapper().organizePageList(page, pageDTO.getQuery());
    }

    /**
     * 组织列表
     *
     * @param dto 入参
     * @return 出参
     */
    @Override
    public List<GoshBlocOrganizeInfoDO> organizeList(OrganizePageListDTO dto) {
        return getBaseMapper().organizeList(dto);
    }

    /**
     * 根据orgId查询组织信息
     *
     * @param orgId orgId
     * @return 组织信息
     */
    @Override
    public GoshBlocOrganizeInfoDO getByOrgId(String orgId) {
        return query().eq(GoshBlocOrganizeInfoDO.ORG_ID, orgId).eq(GoshBlocOrganizeInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).last("limit 1").one();
    }

    /**
     * 根据orgId查询orgName
     *
     * @param orgId orgId
     * @return 名称
     */
    @Override
    public String getOrgNameByOrgId(String orgId) {
        return getBaseMapper().getOrgNameByOrgId(orgId);
    }

    /**
     * 根据集团id获取顶级组织
     *
     * @param blocId 集团id
     * @return 出参
     */
    @Override
    public GoshBlocOrganizeInfoDO getTopOrgByBlocId(String blocId) {
        return query()
                .eq(GoshBlocOrganizeInfoDO.BLOC_ID, blocId)
                .eq(GoshBlocOrganizeInfoDO.PARENT_ID, CommonConstant.STR_ZERO)
                .eq(GoshBlocOrganizeInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1").one();
    }

    /**
     * 根据集团 id 列表获取顶级组织
     *
     * @param blocIdList 入参
     * @return 出参
     */
    @Override
    public List<GoshBlocOrganizeInfoDO> getTopOrgList(List<String> blocIdList) {
        return query().in(GoshBlocOrganizeInfoDO.BLOC_ID, blocIdList).eq(GoshBlocOrganizeInfoDO.PARENT_ID, CommonConstant.STR_ZERO).eq(GoshBlocOrganizeInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }

    /**
     * 根据集团id获取组织id列表
     *
     * @param blocId  集团id
     * @param orgType 1组织 2广场
     * @return 出参
     */
    @Override
    public List<String> findOrgIdListByBlocId(String blocId, Integer orgType) {
        return getBaseMapper().getOrgIdListByBlocId(blocId, orgType);
    }

    /**
     * 根据集团id获取所有组织
     *
     * @param blocId 集团id
     * @return 出参
     */
    @Override
    public List<GoshBlocOrganizeInfoDO> findAllOrgByBlocId(String blocId) {
        return query().eq(GoshBlocOrganizeInfoDO.BLOC_ID, blocId).eq(GoshBlocOrganizeInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }

    /**
     * 组织列表
     *
     * @param blocId  集团id
     * @param orgPath 组织路径
     * @return 出参
     */
    @Override
    public List<GoshBlocOrganizeInfoDO> findPoiOrgListByFullPath(String blocId, String orgPath, Integer orgType) {
        return query().eq(GoshBlocOrganizeInfoDO.BLOC_ID, blocId).like(GoshBlocOrganizeInfoDO.FULL_PATH, orgPath).eq(null != orgType, GoshBlocOrganizeInfoDO.ORG_TYPE, orgType).eq(GoshBlocOrganizeInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }

    /**
     * 组织列表
     *
     * @param dto 入参
     * @return 出参
     */
    @Override
    public List<GoshBlocOrganizeInfoDO> findPoiOrgList(FindPoiOrgListParamDTO dto) {
        return getBaseMapper().findPoiOrgList(dto);
    }

    /**
     * 根据组织名称列表查询组织信息
     *
     * @param orgNameList 组织名称列表
     * @return 组织信息
     */
    @Override
    public List<GoshBlocOrganizeInfoDO> findByOrgNameList(List<String> orgNameList, String blocId) {
        return query()
                .in(GoshBlocOrganizeInfoDO.ORG_NAME, orgNameList)
                .eq(GoshBlocOrganizeInfoDO.ORG_TYPE, 2)
                .eq(GoshBlocOrganizeInfoDO.BLOC_ID, blocId)
                .eq(GoshBlocOrganizeInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .list();
    }

    /**
     * 获取账号下集团的所有组织
     *
     * @param blocId
     * @param accountId
     * @param orgId
     * @return
     */
    @Override
    public List<GoshBlocOrganizeInfoDO> getByAccountId(String blocId, String accountId, String orgId) {
        return getBaseMapper().getByAccountId(blocId, accountId, orgId);
    }

    /**
     * 获取账号下绑定的组织
     *
     * @param accountIdList 账号id列表
     * @return 组织列表
     */
    @Override
    public List<FindByAccountIdListResultDTO> findByAccountIdList(List<String> accountIdList) {
        return getBaseMapper().findByAccountIdList(accountIdList);
    }

    /**
     * 获取账号下绑定的组织
     *
     * @param accountId 账号id
     * @return 组织列表
     */
    @Override
    public List<FindByAccountIdListResultDTO> findByAccountId(String accountId) {
        return getBaseMapper().findByAccountId(accountId);
    }

    /**
     * 根据集团id和组织名称查询组织信息
     *
     * @param blocId  集团id
     * @param orgName 组织名称
     * @return 组织信息
     */
    @Override
    public GoshBlocOrganizeInfoDO getByOrgName(String blocId, String orgName) {
        return query().eq(GoshBlocOrganizeInfoDO.BLOC_ID, blocId).eq(GoshBlocOrganizeInfoDO.ORG_NAME, orgName).eq(GoshBlocOrganizeInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).last("limit 1").one();
    }

    /**
     * 根据组织id删除组织
     *
     * @param orgId 组织id
     */
    @Override
    public void deleteByOrgId(String orgId) {
        update().set(GoshBlocOrganizeInfoDO.IS_DEL, DelFlagEnum.DEL.getValue()).eq(GoshBlocOrganizeInfoDO.ORG_ID, orgId).eq(GoshBlocOrganizeInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).update();
    }

    /**
     * 根据组织id列表查询组织广场数量
     *
     * @param orgIdList 组织id列表
     * @return 组织数量
     */
    @Override
    public List<CountSquareResultDTO> countSquareCountByOrgIdList(List<String> orgIdList) {
        return getBaseMapper().countSquareCountByOrgIdList(orgIdList);
    }

    /**
     * 根据组织id列表查询组织信息
     *
     * @param orgIdList 组织id列表
     * @return 组织列表
     */
    @Override
    public List<GoshBlocOrganizeInfoDO> findByOrgIdList(List<String> orgIdList) {
        return query().in(GoshBlocOrganizeInfoDO.ORG_ID, orgIdList).eq(GoshBlocOrganizeInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }

    /**
     * 根据组织id列表查询组织集团信息
     *
     * @param orgIdList 组织id列表
     * @return 组织列表
     */
    @Override
    public List<FindBlocOrgByOrgIdListResultDTO> findBlocOrgByOrgIdList(List<String> orgIdList) {
        return getBaseMapper().findBlocOrgByOrgIdList(orgIdList);
    }

    /**
     * 根据组织id获取广场列表信息
     *
     * @param orgId 组织id
     * @return 广场信息列表
     */
    @Override
    public List<QuerySquareListDTO> querySquareList(String orgId, String blocId) {
        return getBaseMapper().querySquareList(orgId, blocId);
    }

    /**
     * 根据组织id获取广场列表信息
     *
     * @param orgId  组织id
     * @param blocId
     * @return 广场信息列表
     */
    @Override
    public List<GoshBlocOrganizeInfoDO> findSquareList(String orgId, String blocId) {
        return getBaseMapper().findSquareList(orgId, blocId);
    }

    /**
     * 查询下级组织名称和poiId
     *
     * @param blocId
     * @param orgPath
     * @param orgType
     * @return
     */
    @Override
    public List<PoiAndOrgNameResultDTO> findPoiAndOrgNameByFullPath(String blocId, String orgPath, Integer orgType) {
        return getBaseMapper().findPoiAndOrgNameByFullPath(blocId, orgPath, orgType);
    }

    /**
     * 根据组织id列表查询店铺id列表
     *
     * @param orgIdList 组织id列表
     * @return 店铺id列表
     */
    @Override
    public List<String> getStoreIdListByOrgIdList(List<String> orgIdList) {
        return getBaseMapper().getStoreIdListByOrgIdList(orgIdList);
    }

    /**
     * 根据店铺id列表查询组织信息
     *
     * @param storeIdList 店铺id列表
     * @return 组织信息
     */
    @Override
    public List<GoshBlocOrganizeInfoDO> getByStoreIdList(List<String> storeIdList) {
        return query().in(GoshBlocOrganizeInfoDO.STORE_ID, storeIdList).eq(GoshBlocOrganizeInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }

    /**
     * 根据店铺id查询组织信息
     *
     * @param storeId 店铺id
     * @return 组织信息
     */
    @Override
    public GoshBlocOrganizeInfoDO getByStoreId(String storeId) {
        return query()
                .eq(GoshBlocOrganizeInfoDO.STORE_ID, storeId)
                .eq(GoshBlocOrganizeInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 查询有平台权限的组织
     *
     * @param dto 入参
     * @return 出参
     */
    @Override
    public List<PlatformRolePoiOrgResultDTO> findPlatformRolePoiOrgList(PlatformRolePoiOrgListParamDTO dto) {
        return getBaseMapper().findPlatformRolePoiOrgList(dto);
    }

    /**
     * 查询有权限的组织列表
     *
     * @param dto 入参
     * @return 出参
     */
    @Override
    public List<PlatformRolePoiOrgResultDTO> findRolePoiOrgList(PlatformRolePoiOrgListParamDTO dto) {
        return getBaseMapper().findRolePoiOrgList(dto);
    }

    /**
     * 根据组织id查询店铺信息
     *
     * @param orgId
     * @return
     */
    @Override
    public AilikeMerchantStoreDO getMerchantStoreInfoByOrgId(String orgId) {
        return getBaseMapper().getMerchantStoreInfoByOrgId(orgId);
    }

    /**
     * 根据业务商品id查询组织信息
     *
     * @param businessProductIdList 商品id列表
     * @param merchantId
     * @return
     */
    @Override
    public List<FindProductOrgListResultDTO> findProductOrgList(List<String> businessProductIdList, String merchantId) {
        return getBaseMapper().findProductOrgList(businessProductIdList, merchantId);
    }

    /**
     * 根据组织id列表查询商品id列表
     *
     * @param orgIdList 组织id列表
     * @return 商品id列表
     */
    @Override
    public List<String> getProductIdListByPrgIdList(List<String> orgIdList) {
        return getBaseMapper().getProductIdListByPrgIdList(orgIdList);
    }

    /**
     * 根据商品id查询组织信息
     *
     * @param businessProductId 商品id
     * @param merchantId
     * @return 组织信息
     */
    @Override
    public GetOrderOrgInfoResultDTO getOrderOrgInfo(String businessProductId, String merchantId) {
        return getBaseMapper().getOrderOrgInfo(businessProductId, merchantId);
    }
}
