package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.PageAccountListParamDTO;
import com.fshows.gosh.dao.domain.result.AccountDetailResultDTO;
import com.fshows.gosh.dao.domain.result.PageAccountListResultDTO;
import com.fshows.gosh.dao.entity.GoshUserDO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 来逛呗-商家小程序用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshUserMapper extends BaseMapper<GoshUserDO> {

    /**
     * 分页查询员工列表
     *
     * @param page
     * @param query
     * @return
     */
    Page<PageAccountListResultDTO> pageAccountList(Page<PageAccountListResultDTO> page, @Param("query") PageAccountListParamDTO query);

    /**
     * 查询门店下的手机号数量
     *
     * @param phoneNumber
     * @param shopId
     * @return
     */
    Integer checkPhoneByShopId(@Param("phoneNumber") String phoneNumber, @Param("shopId") String shopId);

    /**
     * 根据身份id查询员工信息
     *
     * @param identityId
     * @param shopId
     * @return
     */
    AccountDetailResultDTO getAccountDetail(@Param("identityId") String identityId, @Param("shopId") String shopId);
}
