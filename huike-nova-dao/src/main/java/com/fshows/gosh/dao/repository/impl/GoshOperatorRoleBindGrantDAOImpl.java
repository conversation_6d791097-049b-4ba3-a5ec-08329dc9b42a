/**
 * <AUTHOR>
 * @date 2024/11/22 18:28
 * @version 1.0 GoshOperatorRoleBindGrantDAOImpl
 */
package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshOperatorRoleBindGrantDO;
import com.fshows.gosh.dao.mapper.GoshOperatorRoleBindGrantMapper;
import com.fshows.gosh.dao.repository.GoshOperatorRoleBindGrantDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @version GoshOperatorRoleBindGrantDAOImpl.java, v 0.1 2024-11-22 18:28 tuyuwei
 */
@Service
public class GoshOperatorRoleBindGrantDAOImpl extends ServiceImpl<GoshOperatorRoleBindGrantMapper, GoshOperatorRoleBindGrantDO> implements GoshOperatorRoleBindGrantDAO {

    @Override
    public void removeByRoleId(String roleId) {
        remove(new LambdaQueryWrapper<GoshOperatorRoleBindGrantDO>()
                .eq(GoshOperatorRoleBindGrantDO::getRoleId, roleId)
                .eq(GoshOperatorRoleBindGrantDO::getIsDel, DelFlagEnum.NOT_DEL.getValue()));
    }

    @Override
    public List<GoshOperatorRoleBindGrantDO> searchByRoleId(String roleId) {
        return getBaseMapper().selectList(new LambdaQueryWrapper<GoshOperatorRoleBindGrantDO>()
                .eq(GoshOperatorRoleBindGrantDO::getRoleId, roleId)
                .eq(GoshOperatorRoleBindGrantDO::getIsDel, DelFlagEnum.NOT_DEL.getValue()));
    }
}