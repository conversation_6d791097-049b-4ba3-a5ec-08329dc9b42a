package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 组织平台权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Data
@TableName("gosh_bloc_organize_platform_permissions")
public class GoshBlocOrganizePlatformPermissionsDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 权限 id
     */
    @TableField("permissions_id")
    private String permissionsId;

    /**
     * 抖音TIKTOK 支付宝ALIPAY 视频号WX
     */
    @TableField("channel_type")
    private String channelType;

    /**
     * 抖音 支付宝 视频号
     */
    @TableField("channel_name")
    private String channelName;

    /**
     * 关联 id 抖音为 poiId 支付宝为 shopId
     */
    @TableField("belong_id")
    private String belongId;

    /**
     * 广场id
     */
    @TableField("org_id")
    private String orgId;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 集团id
     */
    @TableField("bloc_id")
    private String blocId;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String PERMISSIONS_ID = "permissions_id";

    public static final String CHANNEL_TYPE = "channel_type";

    public static final String CHANNEL_NAME = "channel_name";

    public static final String BELONG_ID = "belong_id";

    public static final String IS_DEL = "is_del";

    public static final String ORG_ID = "org_id";

    public static final String BLOC_ID = "bloc_id";


}
