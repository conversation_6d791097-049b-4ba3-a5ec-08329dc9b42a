package com.fshows.gosh.dao.mapper;

import com.fshows.gosh.dao.entity.GoshBlocBalanceDO;
import com.fshows.gosh.dao.entity.GoshMotherCouponComplateCommissionDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 母券对应子券消费或过期赚的佣金差记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface GoshMotherCouponComplateCommissionMapper extends BaseMapper<GoshMotherCouponComplateCommissionDO> {
    /**
     * 根据过期日期删除佣金利润
     *
     * @param expireDay
     * @return
     */
    void deleteByJobExpireDay(@Param("expireDay") Integer expireDay);


    /**
     * 查询母券利润表中结算金额为0的数据
     *
     * @param startSettleDay 开始结算日期
     * @param endSettleDay   结束结算日期
     * @return
     */
    List<GoshMotherCouponComplateCommissionDO> getNullSettleAmountMotherCouponProfit(@Param("startSettleDay") Integer startSettleDay, @Param("endSettleDay") Integer endSettleDay);
}
