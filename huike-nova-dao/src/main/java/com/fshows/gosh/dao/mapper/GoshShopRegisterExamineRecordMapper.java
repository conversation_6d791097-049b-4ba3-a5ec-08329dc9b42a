package com.fshows.gosh.dao.mapper;
 
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.entity.GoshShopRegisterExamineRecordDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 入网审核操作记录表(GoshShopRegisterExamineRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-06 11:25:33
 */
public interface GoshShopRegisterExamineRecordMapper extends BaseMapper<GoshShopRegisterExamineRecordDO> {

    /**
     * 查寻最新入网个人信息
     * @param createTime
     * @param shopId
     * @return
     */
    GoshShopRegisterExamineRecordDO getRecordByTime(@Param("shopId")String shopId, @Param("createTime")Date createTime);


    /**
     * 查询最新申请成功的记录
     * @param shopId
     * @return
     */
    GoshShopRegisterExamineRecordDO getSuccessApply(@Param("shopId")String shopId);

    /**
     * 查询驳回前最新的生效数据
     * @param shopId
     * @param createTime
     * @return
     */
    GoshShopRegisterExamineRecordDO getRecordByTimeAndStatus(@Param("shopId")String shopId, @Param("createTime")Date createTime);
 
}

