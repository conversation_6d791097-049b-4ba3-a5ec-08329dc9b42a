package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.SearchOperationAccountParamDTO;
import com.fshows.gosh.dao.domain.result.SearchOperationAccountResultDTO;
import com.fshows.gosh.dao.entity.GoshOperatorAccountDO;
import com.fshows.gosh.dao.mapper.GoshOperatorAccountMapper;
import com.fshows.gosh.dao.repository.GoshOperatorAccountDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 来逛呗运营后台账号 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Service
public class GoshOperatorAccountDAOImpl extends ServiceImpl<GoshOperatorAccountMapper, GoshOperatorAccountDO> implements GoshOperatorAccountDAO {

    /**
     * 获取运营账号信息
     *
     * @param account
     * @return
     */
    @Override
    public GoshOperatorAccountDO getOperatorAccount(String account) {
        return query()
                .eq(GoshOperatorAccountDO.ACCOUNT, account)
                .last("limit 1")
                .one();
    }

    /**
     * 更新账号最后登录时间
     *
     * @param operatorId
     * @param lastLoginTime
     * @return
     */
    @Override
    public boolean updateLastLoginTime(String operatorId, Integer lastLoginTime) {
        return update()
                .set(GoshOperatorAccountDO.LAST_LOGIN_TIME, lastLoginTime)
                .eq(GoshOperatorAccountDO.OPERATOR_ID, operatorId)
                .update();
    }

    @Override
    public GoshOperatorAccountDO queryByAccount(String account) {
        return query()
                .eq(GoshOperatorAccountDO.ACCOUNT, account)
                .eq(GoshOperatorAccountDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    @Override
    public Page<SearchOperationAccountResultDTO> queryAllAccount(PageParam<SearchOperationAccountParamDTO> pageDTO) {
        Page<SearchOperationAccountResultDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());
        return getBaseMapper().queryAccountList(page, pageDTO.getQuery());
    }

    @Override
    public void updateAccountStatus(Integer status, String operatorId) {
        update().set(GoshOperatorAccountDO.ACCOUNT_STATUS, status)
                .eq(GoshOperatorAccountDO.OPERATOR_ID, operatorId)
                .eq(GoshOperatorAccountDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .update();
    }

    @Override
    public void deleteAccount(String operatorId) {
        update().set(GoshOperatorAccountDO.IS_DEL, DelFlagEnum.DEL.getValue())
                .eq(GoshOperatorAccountDO.OPERATOR_ID, operatorId)
                .update();
    }

    @Override
    public GoshOperatorAccountDO searchByOperatorId(String operatorId) {
        return query().eq(GoshOperatorAccountDO.OPERATOR_ID, operatorId)
                .eq(GoshOperatorAccountDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .one();
    }

    @Override
    public GoshOperatorAccountDO searchOperatorWithoutDel(String operatorId) {
        return query().eq(GoshOperatorAccountDO.OPERATOR_ID, operatorId)
                .one();
    }
}
