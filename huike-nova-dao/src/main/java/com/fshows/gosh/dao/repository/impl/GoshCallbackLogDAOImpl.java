package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshCallbackLogDO;
import com.fshows.gosh.dao.mapper.GoshCallbackLogMapper;
import com.fshows.gosh.dao.repository.GoshCallbackLogDAO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 来逛呗-回调记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Service
public class GoshCallbackLogDAOImpl extends ServiceImpl<GoshCallbackLogMapper, GoshCallbackLogDO> implements GoshCallbackLogDAO {


    /**
     * 根据关联id查询回调信息
     *
     * @param relationId
     * @param handleType
     * @return
     */
    @Override
    public GoshCallbackLogDO getLogByRelationId(String relationId, String handleType) {
        return query()
                .eq(GoshCallbackLogDO.RELATION_ID, relationId)
                .eq(GoshCallbackLogDO.HANDLE_TYPE, handleType)
                .last("limit 1")
                .one();
    }
}
