package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.BlocQueryParamDTO;
import com.fshows.gosh.dao.domain.result.BlocQueryResultDTO;
import com.fshows.gosh.dao.domain.result.MerchantMinaLoginResultDTO;
import com.fshows.gosh.dao.entity.GoshBlocDO;
import com.fshows.gosh.dao.mapper.GoshBlocMapper;
import com.fshows.gosh.dao.repository.GoshBlocDAO;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.metadata.PageParam;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 来逛呗集团表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class GoshBlocDAOImpl extends ServiceImpl<GoshBlocMapper, GoshBlocDO> implements GoshBlocDAO {

    /**
     * 获取服务商appId
     *
     * @param orgId
     * @return
     */
    @Override
    public MerchantMinaLoginResultDTO getServiceAppId(String orgId) {
        return getBaseMapper().getServiceAppId(orgId);
    }

    /**
     * 根据集团id获取集团信息
     *
     * @param blocId
     * @return
     */
    @Override
    public GoshBlocDO getBlocInfo(String blocId) {
        return query()
                .eq(GoshBlocDO.BLOC_ID, blocId)
                .last("limit 1")
                .one();
    }

    /**
     * 根据appId获取集团信息
     *
     * @param blocName 集团名称
     * @return GoshBlocDO
     */
    @Override
    public GoshBlocDO getBlocByBlocName(String blocName) {
        return query()
                .eq(GoshBlocDO.BLOC_NAME, blocName)
                .last("limit 1")
                .one();
    }

    /**
     * 根据appId获取集团信息
     *
     * @param appId
     * @return
     */
    @Override
    public GoshBlocDO getBlocByAppId(String appId) {
        return query()
                .eq(GoshBlocDO.AILIKE_APP_ID, appId)
                .last("limit 1")
                .one();
    }

    /**
     * 查询所有集团
     *
     * @return
     */
    @Override
    public List<GoshBlocDO> findAllBloc() {
        return query()
                .list();
    }

    /**
     * 查询部分集团账号有权限的集团列表
     *
     * @param operatorId 运营后台账号Id
     * @return
     */
    @Override
    public List<GoshBlocDO> findBlocListByOperatorId(String operatorId) {
        return getBaseMapper().findBlocListByOperatorId(operatorId);
    }

    /**
     * 分页查询集团列表数据
     *
     * @param pageDTO
     * @return
     */
    @Override
    public Page<BlocQueryResultDTO> pageBlocList(PageParam<BlocQueryParamDTO> pageDTO) {
        Page<BlocQueryResultDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());
        return getBaseMapper().pageBlocList(page, pageDTO.getQuery());
    }

    /**
     * 更新集团状态
     *
     * @param blocId     集团 id
     * @param blocStatus 集团状态
     */
    @Override
    public void updateBlocStatus(String blocId, Integer blocStatus) {
        update().set(GoshBlocDO.BLOC_STATUS, blocStatus)
                .eq(GoshBlocDO.BLOC_ID, blocId)
                .update();
    }
}
