package com.fshows.gosh.dao.repository.impl;

import com.fshows.gosh.dao.entity.AilikeProductOperatorLogDO;
import com.fshows.gosh.dao.mapper.AilikeProductOperatorLogMapper;
import com.fshows.gosh.dao.repository.AilikeProductOperatorLogDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.enums.DelStatusEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 商品操作日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Service
public class AilikeProductOperatorLogDAOImpl extends ServiceImpl<AilikeProductOperatorLogMapper, AilikeProductOperatorLogDO> implements AilikeProductOperatorLogDAO {

    /**
     * 查询商品的操作日志记录列表
     *
     * @param businessProductId
     * @return
     */
    @Override
    public List<AilikeProductOperatorLogDO> findProductOperatorLogList(String businessProductId) {
        return query().eq(AilikeProductOperatorLogDO.BUSINESS_PRODUCT_ID, businessProductId)
                .eq(AilikeProductOperatorLogDO.DEL_FLAG, DelStatusEnum.NOT_DEL.getValue())
                .orderByDesc(AilikeProductOperatorLogDO.ID)
                .list();
    }

    /**
     * 查询日志明细
     *
     * @param logId
     * @return
     */
    @Override
    public AilikeProductOperatorLogDO findProductOperatorLog(String logId) {
        return query().eq(AilikeProductOperatorLogDO.LOG_ID, logId)
                .last("limit 1")
                .one();
    }
}
