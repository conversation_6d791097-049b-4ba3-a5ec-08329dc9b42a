package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.GoshDiffOrderDetailDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 来逛呗订单差异表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
public interface GoshDiffOrderDetailDAO extends IService<GoshDiffOrderDetailDO> {


    /**
     * 根据日期和对账单类型删除
     * @param diffDate
     * @param BillType
     */
    void deleteByDiffDateAndType(String diffDate, Integer BillType);


    /**
     * 根据日期和对账单类型查询差异数据
     * @param diffDate
     * @param BillType
     * @return {@link List}<{@link GoshDiffOrderDetailDO}>
     */
    List<GoshDiffOrderDetailDO> getByDiffDataAndType(String diffDate, Integer BillType,Integer diffType);
}
