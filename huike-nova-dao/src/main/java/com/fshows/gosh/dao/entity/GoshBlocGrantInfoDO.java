package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 后台权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Data
@TableName("gosh_bloc_grant_info")
public class GoshBlocGrantInfoDO implements Serializable {

    public static final String ID = "id";
    public static final String GRANT_ID = "grant_id";
    public static final String GRANT_CODE = "grant_code";
    public static final String GRANT_NAME = "grant_name";
    public static final String PARENT_GRANT_ID = "parent_grant_id";
    public static final String GRANT_TYPE = "grant_type";
    public static final String SORT = "sort";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 权限id
     */
    @TableField("grant_id")
    private String grantId;
    /**
     * 权限编码
     */
    @TableField("grant_code")
    private String grantCode;
    /**
     * 权限名称
     */
    @TableField("grant_name")
    private String grantName;
    /**
     * 上级
     */
    @TableField("parent_grant_id")
    private String parentGrantId;
    /**
     * 权限类型(1菜单,2功能)
     */
    @TableField("grant_type")
    private Integer grantType;
    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;
    /**
     * 是否删除:0未删除,1已删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

}
