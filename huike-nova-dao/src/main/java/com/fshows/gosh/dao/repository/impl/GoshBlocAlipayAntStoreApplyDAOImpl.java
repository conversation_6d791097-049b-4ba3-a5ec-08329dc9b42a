package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshBlocAlipayAntStoreApplyDO;
import com.fshows.gosh.dao.mapper.GoshBlocAlipayAntStoreApplyMapper;
import com.fshows.gosh.dao.repository.GoshBlocAlipayAntStoreApplyDAO;
import com.huike.nova.common.constant.StringPool;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 来逛呗蚂蚁门店申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Service
public class GoshBlocAlipayAntStoreApplyDAOImpl extends ServiceImpl<GoshBlocAlipayAntStoreApplyMapper, GoshBlocAlipayAntStoreApplyDO> implements GoshBlocAlipayAntStoreApplyDAO {

    /**
     * 根据storeId和pid查询蚂蚁门店申请单
     *
     * @param pid
     * @param storeId
     * @return
     */
    @Override
    public GoshBlocAlipayAntStoreApplyDO getAntShopByStoreIdAndPid(String pid, String storeId) {
        return query()
                .eq(GoshBlocAlipayAntStoreApplyDO.STORE_ID, storeId)
                .eq(GoshBlocAlipayAntStoreApplyDO.IP_ROLE_ID, pid)
                .last("limit 1")
                .one();
    }

    /**
     * 保存蚂蚁门店申请单
     *
     * @param storeApplyDO
     * @return
     */
    @Override
    public boolean saveAntShopApply(GoshBlocAlipayAntStoreApplyDO storeApplyDO) {
        return save(storeApplyDO);
    }

    /**
     * 更新蚂蚁门店申请单
     *
     * @param blocAlipayAntStoreApplyDO
     * @return
     */
    @Override
    public boolean updateShopInfoByStoreIdAndPid(GoshBlocAlipayAntStoreApplyDO blocAlipayAntStoreApplyDO) {
        String shopId = blocAlipayAntStoreApplyDO.getShopId();
        Integer shopStatus = blocAlipayAntStoreApplyDO.getShopStatus();
        String failType = blocAlipayAntStoreApplyDO.getFailType();
        String failReason = blocAlipayAntStoreApplyDO.getFailReason();
        String applyStoreId = blocAlipayAntStoreApplyDO.getApplyStoreId();
        String applyStoreName = blocAlipayAntStoreApplyDO.getApplyStoreName();
        String orderId = blocAlipayAntStoreApplyDO.getOrderId();
        return update()
                .set(StringUtils.isNotBlank(shopId), GoshBlocAlipayAntStoreApplyDO.SHOP_ID, shopId)
                .set(StringUtils.isNotBlank(orderId), GoshBlocAlipayAntStoreApplyDO.ORDER_ID, orderId)
                .set(Objects.nonNull(shopStatus), GoshBlocAlipayAntStoreApplyDO.SHOP_STATUS, shopStatus)
                .set(StringUtils.isNotBlank(failType), GoshBlocAlipayAntStoreApplyDO.FAIL_TYPE, failType)
                .set(StringUtils.isNotBlank(failReason), GoshBlocAlipayAntStoreApplyDO.FAIL_REASON, failReason)
                .set(StringUtils.isNotBlank(applyStoreId), GoshBlocAlipayAntStoreApplyDO.APPLY_STORE_ID, applyStoreId)
                .set(StringUtils.isNotBlank(applyStoreName), GoshBlocAlipayAntStoreApplyDO.APPLY_STORE_NAME, applyStoreName)
                .eq(GoshBlocAlipayAntStoreApplyDO.STORE_ID, blocAlipayAntStoreApplyDO.getStoreId())
                .eq(GoshBlocAlipayAntStoreApplyDO.IP_ROLE_ID, blocAlipayAntStoreApplyDO.getIpRoleId())
                .update();
    }

    /**
     * 根据状态的查询蚂蚁门店申请单
     *
     * @param shopStatus
     * @return
     */
    @Override
    public List<GoshBlocAlipayAntStoreApplyDO> findCreatingAntShopList(Integer shopStatus) {
        return query()
                .eq(GoshBlocAlipayAntStoreApplyDO.SHOP_ID, StringPool.EMPTY)
                .eq(GoshBlocAlipayAntStoreApplyDO.SHOP_STATUS, shopStatus)
                .list();
    }
}
