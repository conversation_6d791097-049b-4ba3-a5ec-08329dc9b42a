package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.entity.AilikeQykWxUserActivityDO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 微信营销活动-用户活动表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
public interface AilikeQykWxUserActivityMapper extends BaseMapper<AilikeQykWxUserActivityDO> {

    /**
     * 推广单商品统计
     *
     * @param userActivityRelationId 用户活动关联 id
     * @return 推广单商品统计信息
     */
    AilikeQykWxUserActivityDO queryUserActivityStatistics(@Param("userActivityRelationId") String userActivityRelationId);

}
