package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 抖音线下退款申请
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@TableName("gosh_offline_refund_apply")
public class GoshOfflineRefundApplyDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 打款Id
     */
    @TableField("payment_id")
    private String paymentId;

    /**
     * 集团id
     */
    @TableField("app_id")
    private String appId;

    /**
     * 订单号
     */
    @TableField("order_sn")
    private String orderSn;

    /**
     * 订单核销状态1-未核销；2-已核销（有一张券撤销核销都是未核销）
     */
    @TableField("order_status")
    private Integer orderStatus;

    /**
     * 券码(多个券码,隔开)
     */
    @TableField("coupon_code")
    private String couponCode;

    /**
     * 核销门店ID（抖音）
     */
    @TableField("verification_store_id")
    private String verificationStoreId;

    /**
     * 核销门店（抖音）
     */
    @TableField("verification_store_name")
    private String verificationStoreName;

    /**
     * 最近核销时间
     */
    @TableField("last_verification_time")
    private Date lastVerificationTime;

    /**
     * 抖音来客商品Id
     */
    @TableField("tiktok_product_id")
    private String tiktokProductId;

    /**
     * 抖音来客商品名称
     */
    @TableField("tiktok_product_name")
    private String tiktokProductName;

    /**
     * 用户实付
     */
    @TableField("cash_fee")
    private BigDecimal cashFee;

    /**
     * 券售卖金额
     */
    @TableField("coupon_sale_amount")
    private BigDecimal couponSaleAmount;

    /**
     * 退款原因
     */
    @TableField("refund_reason")
    private String refundReason;

    /**
     * 退款券数
     */
    @TableField("refund_count")
    private Integer refundCount;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 剩余金额
     */
    @TableField("remain_amount")
    private BigDecimal remainAmount;

    /**
     * 商家券状态:1-已使用;2-未使用
     */
    @TableField("merchant_coupon_status")
    private Integer merchantCouponStatus;

    /**
     * 顾客电话
     */
    @TableField("customer_phone")
    private String customerPhone;

    /**
     * 提交人
     */
    @TableField("apply_name")
    private String applyName;

    /**
     * 支付宝账号
     */
    @TableField("alipay_account")
    private String alipayAccount;

    /**
     * 支付宝姓名
     */
    @TableField("alipay_name")
    private String alipayName;

    /**
     * 打款人
     */
    @TableField("payment_name")
    private String paymentName;

    /**
     * 打款时间
     */
    @TableField("payment_time")
    private Date paymentTime;

    /**
     * 打款状态：1-未打款; 2-已打款; 3-不打款
     */
    @TableField("payment_status")
    private Integer paymentStatus;

    /**
     * 退款图片说明
     */
    @TableField("refund_img")
    private String refundImg;

    /**
     * 备注说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否删除: 0-未删除 1-已删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 平台
     */
    @TableField("channel_type")
    private String channelType;

    /**
     * 打款失败原因
     */
    @TableField("fail_reason")
    private String failReason;

    /**
     * 打款失败时间
     */
    @TableField("fail_time")
    private Date failTime;

    /**
     * 申请退款的类型：0-线下退款 1-线上退款
     */
    @TableField("apply_refund_type")
    private Integer applyRefundType;

    public Date getLastVerificationTime() {
        if (this.lastVerificationTime != null) {
          return new Date(this.lastVerificationTime.getTime());
        } else {
          return null;
        }
    }

    public void setLastVerificationTime(Date lastVerificationTime) {
        if (lastVerificationTime != null) {
            this.lastVerificationTime = new Date(lastVerificationTime.getTime());
        } else {
            this.lastVerificationTime = null;
        }
    }
    public Date getPaymentTime() {
        if (this.paymentTime != null) {
          return new Date(this.paymentTime.getTime());
        } else {
          return null;
        }
    }

    public void setPaymentTime(Date paymentTime) {
        if (paymentTime != null) {
            this.paymentTime = new Date(paymentTime.getTime());
        } else {
            this.paymentTime = null;
        }
    }
    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }
    public Date getFailTime() {
        if (this.failTime != null) {
          return new Date(this.failTime.getTime());
        } else {
          return null;
        }
    }

    public void setFailTime(Date failTime) {
        if (failTime != null) {
            this.failTime = new Date(failTime.getTime());
        } else {
            this.failTime = null;
        }
    }


    public static final String ID = "id";

    public static final String PAYMENT_ID = "payment_id";

    public static final String APP_ID = "app_id";

    public static final String ORDER_SN = "order_sn";

    public static final String ORDER_STATUS = "order_status";

    public static final String COUPON_CODE = "coupon_code";

    public static final String VERIFICATION_STORE_ID = "verification_store_id";

    public static final String VERIFICATION_STORE_NAME = "verification_store_name";

    public static final String LAST_VERIFICATION_TIME = "last_verification_time";

    public static final String TIKTOK_PRODUCT_ID = "tiktok_product_id";

    public static final String TIKTOK_PRODUCT_NAME = "tiktok_product_name";

    public static final String CASH_FEE = "cash_fee";

    public static final String COUPON_SALE_AMOUNT = "coupon_sale_amount";

    public static final String REFUND_REASON = "refund_reason";

    public static final String REFUND_COUNT = "refund_count";

    public static final String REFUND_AMOUNT = "refund_amount";

    public static final String REMAIN_AMOUNT = "remain_amount";

    public static final String MERCHANT_COUPON_STATUS = "merchant_coupon_status";

    public static final String CUSTOMER_PHONE = "customer_phone";

    public static final String APPLY_NAME = "apply_name";

    public static final String ALIPAY_ACCOUNT = "alipay_account";

    public static final String ALIPAY_NAME = "alipay_name";

    public static final String PAYMENT_NAME = "payment_name";

    public static final String PAYMENT_TIME = "payment_time";

    public static final String PAYMENT_STATUS = "payment_status";

    public static final String REFUND_IMG = "refund_img";

    public static final String REMARK = "remark";

    public static final String IS_DEL = "is_del";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String CHANNEL_TYPE = "channel_type";

    public static final String FAIL_REASON = "fail_reason";

    public static final String FAIL_TIME = "fail_time";

    public static final String APPLY_REFUND_TYPE = "apply_refund_type";

}
