package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * <p>
 * 核销结算明细数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@TableName("gosh_verify_settle_detail")
public class GoshVerifySettleDetailDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 结算批次号（对应gosh_settle_form表同字段）
     */
    @TableField("serial_no")
    private String serialNo;

    /**
     * 商铺id
     */
    @TableField("shop_id")
    private String shopId;

    /**
     * 付呗侧account_id
     */
    @TableField("account_id")
    private String accountId;

    /**
     * 交易/退款时间（核销时间）**************
     */
    @TableField("trade_time")
    private String tradeTime;

    /**
     * 交易类型;SINGLE_PAY-支付明细;REFUND-退款明细
     */
    @TableField("trade_type")
    private String tradeType;

    /**
     * 外部交易单号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 平台方的购买券单号
     */
    @TableField("channel_trade_no")
    private String channelTradeNo;

    /**
     * 来逛呗退款订单号
     */
    @TableField("refund_no")
    private String refundNo;

    /**
     * 平台方的退券单号
     */
    @TableField("channel_refund_no")
    private String channelRefundNo;

    /**
     * 实际结算给商户/佣金金额，单位：分
     */
    @TableField("actual_amount")
    private Integer actualAmount;

    /**
     * 平台类型 TIKTOK-抖音 ALIPAY-支付宝 MEITUAN-美团
     */
    @TableField("platform_type")
    private String platformType;

    /**
     * 结算日期（yyyyMMdd）
     */
    @TableField("settled_day")
    private Integer settledDay;

    /**
     * 结算类型 0:商户结算款 1:佣金结算款(商圈卡赚的钱)
     */
    @TableField("settle_type")
    private Integer settleType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 0 非商圈卡 1 商圈母卡 2商圈子卡
     */
    @TableField("org_product_type")
    private Integer orgProductType;

    /**
     * 门店的结算单号
     */
    @TableField("shop_serial_no")
    private String shopSerialNo;

    /**
     * 券名称
     */
    @TableField("body")
    private String body;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String SERIAL_NO = "serial_no";

    public static final String SHOP_ID = "shop_id";

    public static final String ACCOUNT_ID = "account_id";

    public static final String TRADE_TIME = "trade_time";

    public static final String TRADE_TYPE = "trade_type";

    public static final String TRADE_NO = "trade_no";

    public static final String CHANNEL_TRADE_NO = "channel_trade_no";

    public static final String REFUND_NO = "refund_no";

    public static final String CHANNEL_REFUND_NO = "channel_refund_no";

    public static final String ACTUAL_AMOUNT = "actual_amount";

    public static final String PLATFORM_TYPE = "platform_type";

    public static final String SETTLE_DATE = "settle_date";

    public static final String SETTLE_TYPE = "settle_type";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String ORG_PRODUCT_TYPE = "org_product_type";

    public static final String SHOP_SERIAL_NO = "shop_serial_no";

}
