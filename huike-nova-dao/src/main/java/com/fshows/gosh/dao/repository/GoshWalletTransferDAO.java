package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.GoshWalletTransferDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 钱包转账记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
public interface GoshWalletTransferDAO extends IService<GoshWalletTransferDO> {

    /**
     * 根据转账单号查询
     *
     * @param transferNo
     * @return
     */
    GoshWalletTransferDO getByTransferNo(String transferNo);

    /**
     * 根据转账单号更新
     *
     * @param walletTransferDO
     */
    boolean updateByTransferNo(GoshWalletTransferDO walletTransferDO);

    /**
     * 根据转账日期查询初始化状态
     *
     * @param transferDateStart
     * @param transferDateEnd
     * @return
     */
    List<GoshWalletTransferDO> getBySpanTransferDate(String transferDateStart, String transferDateEnd);

    /**
     * 根据结算单号查询非提现失败订单条数
     *
     * @param settleSerialNo
     * @return
     */
    Long getBySettleSerialNo(String settleSerialNo);
}
