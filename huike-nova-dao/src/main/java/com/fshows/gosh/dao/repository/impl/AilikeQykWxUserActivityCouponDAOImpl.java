package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.AilikeQykWxUserActivityCouponDO;
import com.fshows.gosh.dao.mapper.AilikeQykWxUserActivityCouponMapper;
import com.fshows.gosh.dao.repository.AilikeQykWxUserActivityCouponDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 微信营销活动-用户活动券表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Service
public class AilikeQykWxUserActivityCouponDAOImpl extends ServiceImpl<AilikeQykWxUserActivityCouponMapper, AilikeQykWxUserActivityCouponDO> implements AilikeQykWxUserActivityCouponDAO {

    /**
     * 根据券码查询活动券
     *
     * @param couponCode 券码
     * @return 活动券
     */
    @Override
    public AilikeQykWxUserActivityCouponDO getByCouponCode(String couponCode) {
        return query().eq(AilikeQykWxUserActivityCouponDO.COUPON_CODE, couponCode)
                .eq(AilikeQykWxUserActivityCouponDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("LIMIT 1")
                .one();
    }

}
