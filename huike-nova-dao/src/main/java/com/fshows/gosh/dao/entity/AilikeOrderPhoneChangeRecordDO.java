package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 订单手机号变更记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
@TableName("ailike_order_phone_change_record")
public class AilikeOrderPhoneChangeRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    @TableField("out_order_sn")
    private String outOrderSn;

    /**
     * 操作人 id   （gosh_operator_account 表operator_id）
     */
    @TableField("operator_id")
    private String operatorId;

    /**
     * 操作人账号（gosh_operator_account 表account）
     */
    @TableField("operator_account")
    private String operatorAccount;

    /**
     * 变更前手机号
     */
    @TableField("original_phone")
    private String originalPhone;

    /**
     * 变更后手机号
     */
    @TableField("new_phone")
    private String newPhone;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String OUT_ORDER_SN = "out_order_sn";

    public static final String OPERATOR_ID = "operator_id";

    public static final String OPERATOR_ACCOUNT = "operator_account";

    public static final String ORIGINAL_PHONE = "original_phone";

    public static final String NEW_PHONE = "new_phone";

    public static final String REMARK = "remark";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
