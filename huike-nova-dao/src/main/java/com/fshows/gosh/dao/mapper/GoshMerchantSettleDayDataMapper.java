package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.domain.param.settleday.QueryDailySettlementListParamDTO;
import com.fshows.gosh.dao.domain.result.settleday.QueryDailySettlementListResultDTO;
import com.fshows.gosh.dao.entity.GoshMerchantSettleDayDataDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.mapping.ResultSetType;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/**
 * <p>
 * 来逛呗-商家结算数据汇总 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
public interface GoshMerchantSettleDayDataMapper extends BaseMapper<GoshMerchantSettleDayDataDO> {

    /**
     * 查询未结算的数据
     *
     * @param time
     * @param resultHandler
     */
    @Select("select * from gosh_merchant_settle_day_data where verify_day >= #{time,jdbcType=INTEGER} and settled_calculate_status = 1 and is_del = 0")
    @Options(resultSetType = ResultSetType.FORWARD_ONLY, fetchSize = Integer.MIN_VALUE)
    @ResultType(GoshMerchantSettleDayDataDO.class)
    void findUnsettledList(@Param("time") Integer time, ResultHandler<GoshMerchantSettleDayDataDO> resultHandler);

    /**
     * 查询日结算列表
     *
     * @param paramDTO
     * @return
     */
    List<QueryDailySettlementListResultDTO> queryDailySettlementList(@Param("dto") QueryDailySettlementListParamDTO paramDTO);

    /**
     * 自定义汇总结算查询
     *
     * @param paramDTO
     * @return
     */
    QueryDailySettlementListResultDTO getCustomSettlement(@Param("dto") QueryDailySettlementListParamDTO paramDTO);

    /**
     * 月汇总结算列表
     *
     * @param paramDTO
     * @return
     */
    List<QueryDailySettlementListResultDTO> queryMonthSettlementList(@Param("dto") QueryDailySettlementListParamDTO paramDTO);

    /**
     * 结算详情列表
     *
     * @param paramDTO
     * @return
     */
    List<QueryDailySettlementListResultDTO> getSettlementDetailList(@Param("dto") QueryDailySettlementListParamDTO paramDTO);

    /**
     * 账单导出
     *
     * @param paramDTO
     * @return
     */
    List<QueryDailySettlementListResultDTO> exportMinaFinanceReconciliation(@Param("dto") QueryDailySettlementListParamDTO paramDTO);

    /**
     * 根据appId和verifyDay删除数据
     *
     * @param appId
     * @param verifyDay
     * @return
     */
    @Delete("DELETE FROM gosh_merchant_settle_day_data where app_id = #{appId} and verify_day =#{verifyDay} and is_del = 0")
    boolean deleteByAppIdAndDay(@Param("appId") String appId, @Param("verifyDay") Integer verifyDay);
}
