package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fshows.gosh.dao.entity.GoshMotherCouponComplateCommissionDO;
import com.fshows.gosh.dao.entity.GoshVerifySettleDetailDO;
import com.fshows.gosh.dao.mapper.GoshMotherCouponComplateCommissionMapper;
import com.fshows.gosh.dao.repository.GoshMotherCouponComplateCommissionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.constant.CommonConstant;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 母券对应子券消费或过期赚的佣金差记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Service
public class GoshMotherCouponComplateCommissionDAOImpl extends ServiceImpl<GoshMotherCouponComplateCommissionMapper, GoshMotherCouponComplateCommissionDO> implements GoshMotherCouponComplateCommissionDAO {

    /**
     * 查询母券的利润
     *
     * @param settleDay   结算日
     * @param channelType 平台
     * @return
     */
    @Override
    public List<GoshMotherCouponComplateCommissionDO> getMotherCouponProfit(String appId, Integer settleDay, String channelType) {
        return query().eq(GoshMotherCouponComplateCommissionDO.SETTLE_DAY, settleDay)
                .eq(GoshMotherCouponComplateCommissionDO.APP_ID, appId)
                .eq(GoshMotherCouponComplateCommissionDO.CHANNEL_TYPE, channelType).list();
    }

    /**
     * 根据过期日期删除佣金利润
     *
     * @param expireDay 过期日期
     * @return
     */
    @Override
    public void deleteByJobExpireDay(Integer expireDay) {
        getBaseMapper().deleteByJobExpireDay(expireDay);
    }

    /**
     * 查询母券利润表中结算金额为0的数据
     *
     * @param startSettleDay 开始结算日期
     * @param endSettleDay   结束结算日期
     * @return
     */
    @Override
    public List<GoshMotherCouponComplateCommissionDO> getNullSettleAmountMotherCouponProfit(Integer startSettleDay, Integer endSettleDay) {
        return getBaseMapper().getNullSettleAmountMotherCouponProfit(startSettleDay, endSettleDay);
    }

    /**
     * 券码数据
     *
     * @param couponCode 券码
     * @return
     */
    @Override
    public GoshMotherCouponComplateCommissionDO findByCouponCodeCount(String couponCode) {
        return query().eq(GoshMotherCouponComplateCommissionDO.COUPON_CODE, couponCode).last("limit 1").one();
    }
}
