package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.param.settleday.QueryDailySettlementListParamDTO;
import com.fshows.gosh.dao.domain.result.settleday.QueryDailySettlementListResultDTO;
import com.fshows.gosh.dao.entity.GoshMerchantSettleDayDataDO;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/**
 * <p>
 * 来逛呗-商家结算数据汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
public interface GoshMerchantSettleDayDataDAO extends IService<GoshMerchantSettleDayDataDO> {

    /**
     * 查询未结算的数据
     *
     * @param time
     * @param resultHandler
     */
    void findUnsettledList(Integer time, ResultHandler<GoshMerchantSettleDayDataDO> resultHandler);

    /**
     * 更新数据
     *
     * @param settleDayDataDO
     * @return
     */
    boolean updateSettleDayData(GoshMerchantSettleDayDataDO settleDayDataDO);

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    boolean batchSettleDayData(List<GoshMerchantSettleDayDataDO> list);


    /**
     * 查询日结算列表
     *
     * @param paramDTO
     * @return
     */
    List<QueryDailySettlementListResultDTO> queryDailySettlementList(QueryDailySettlementListParamDTO paramDTO);

    /**
     * 自定义汇总结算查询
     *
     * @param paramDTO
     * @return
     */
    QueryDailySettlementListResultDTO getCustomSettlement(QueryDailySettlementListParamDTO paramDTO);

    /**
     * 月汇总结算列表
     *
     * @param paramDTO
     * @return
     */
    List<QueryDailySettlementListResultDTO> queryMonthSettlementList(QueryDailySettlementListParamDTO paramDTO);

    /**
     * 结算详情列表
     *
     * @param paramDTO
     * @return
     */
    List<QueryDailySettlementListResultDTO> getSettlementDetailList(QueryDailySettlementListParamDTO paramDTO);

    /**
     * 账单导出
     *
     * @param paramDTO
     * @return
     */
    List<QueryDailySettlementListResultDTO> exportMinaFinanceReconciliation(QueryDailySettlementListParamDTO paramDTO);

    /**
     * 根据appId和verifyDay查询数量
     *
     * @param appId
     * @param verifyDay
     * @return
     */
    int countByAppIdAndVerifyDay(String appId, Integer verifyDay);

    /**
     * 根据appId和verifyDay删除数据
     *
     * @param appId
     * @param verifyDay
     * @return
     */
    boolean deleteByAppIdAndDay(String appId, Integer verifyDay);
}
