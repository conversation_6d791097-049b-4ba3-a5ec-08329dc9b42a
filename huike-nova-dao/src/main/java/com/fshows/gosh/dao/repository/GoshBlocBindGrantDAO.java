package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshBlocBindGrantDO;

import java.util.List;

/**
 * <p>
 * 集团权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
public interface GoshBlocBindGrantDAO extends IService<GoshBlocBindGrantDO> {

    /**
     * 根据集团id查询集团权限id集合
     *
     * @param blocId 集团id
     * @return 集团权限id集合
     */
    List<String> findBlocGrantIdListByBlocId(String blocId);

    /**
     * 根据集团 id 删除集团权限
     *
     * @param blocId 集团 id
     */
    void deleteByBlocId(String blocId);
}
