package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 来逛呗集团组织信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Data
@TableName("gosh_bloc_organize_info")
public class GoshBlocOrganizeInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 集团id
     */
    @TableField("bloc_id")
    private String blocId;

    /**
     * 组织id
     */
    @TableField("org_id")
    private String orgId;

    /**
     * 组织名称
     */
    @TableField("org_name")
    private String orgName;

    public static final String ORG_HIERARCHY = "org_hierarchy";

    /**
     * 上级
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 完整路径前后都带/
     */
    @TableField("full_path")
    private String fullPath;

    /**
     * 完整路径前后都带/
     */
    @TableField("full_name_path")
    private String fullNamePath;

    /**
     * 省 code
     */
    @TableField("province")
    private String province;

    /**
     * 市code
     */
    @TableField("city")
    private String city;

    /**
     * 区code
     */
    @TableField("area")
    private String area;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 1付呗 2new集团后台
     */
    @TableField("org_source")
    private Integer orgSource;

    public static final String POI_ACCOUNT_ID = "poi_account_id";

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    public static final String STORE_ID = "store_id";
    public static final String ORG_TYPE = "org_type";
    public static final String ORG_SOURCE = "org_source";
    /**
     * 当前组织层级
     */
    @TableField("org_hierarchy")
    private Integer orgHierarchy;

    /**
     * 省市区地址 拼接后
     */
    @TableField("position_name")
    private String positionName;

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }


    public static final String ID = "id";

    public static final String BLOC_ID = "bloc_id";

    public static final String ORG_ID = "org_id";

    public static final String ORG_NAME = "org_name";
    public static final String POSITION_NAME = "position_name";
    /**
     * ailike 门店id
     */
    @TableField("store_id")
    private String storeId;

    public static final String PARENT_ID = "parent_id";

    public static final String FULL_PATH = "full_path";

    public static final String FULL_NAME_PATH = "full_name_path";

    public static final String PROVINCE = "province";

    public static final String CITY = "city";

    public static final String AREA = "area";

    public static final String ADDRESS = "address";

    public static final String IS_DEL = "is_del";

    public static final String UPDATE_TIME = "update_time";

    public static final String CREATE_TIME = "create_time";
    /**
     * 广场life_account_id
     */
    @TableField("poi_account_id")
    private String poiAccountId;

    /**
     * 组织类型  1 组织 2广场
     */
    @TableField("org_type")
    private Integer orgType;

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

}
