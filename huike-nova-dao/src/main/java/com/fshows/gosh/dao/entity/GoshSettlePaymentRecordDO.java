package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 入金记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
@TableName("gosh_settle_payment_record")
public class GoshSettlePaymentRecordDO implements Serializable {

    public static final String ID = "id";
    public static final String TRACE_NO = "trace_no";
    public static final String PLATFORM_NOTIFY_NO = "platform_notify_no";
    public static final String PLATFORM_WALLET_ID = "platform_wallet_id";
    public static final String AMOUNT = "amount";
    public static final String TRADE_DATE = "trade_date";
    public static final String REMARK = "remark";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 通知流水号
     */
    @TableField("trace_no")
    private String traceNo;
    /**
     * 平台通知流水号
     */
    @TableField("platform_notify_no")
    private String platformNotifyNo;
    /**
     * 平台钱包id
     */
    @TableField("platform_wallet_id")
    private String platformWalletId;
    /**
     * 入金金额
     */
    @TableField("amount")
    private BigDecimal amount;
    /**
     * 收到入金的系统日期（yyyyMMdd）
     */
    @TableField("trade_date")
    private String tradeDate;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
    /**
     * 是否删除: 0-未删除 1-已删除
     */
    @TableField("is_del")
    private Integer isDel;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

}
