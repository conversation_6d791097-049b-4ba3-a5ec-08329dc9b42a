package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.GoshMinaAdvertRecordLogDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 来逛呗商家版小程序广告商户日访问记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
public interface GoshMinaAdvertRecordLogDAO extends IService<GoshMinaAdvertRecordLogDO> {

    /**
     * 根据日期、广告id、openId、类型查询记录
     *
     * @param visitDay 日期
     * @param advertId 广告id
     * @param openId   微信用户openId
     * @param type     类型
     * @return 返回类型
     */
    GoshMinaAdvertRecordLogDO findByDayAndAdvertId(Integer visitDay, String advertId, String openId, Integer type);
}
