package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 账户组件授权表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-05
 */
@Data
@TableName("gosh_component_auth")
public class GoshComponentAuthDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 账户编号
     */
    @TableField("account_id")
    private String accountId;

    /**
     * 组件类型
     */
    @TableField("component_type")
    private String componentType;

    /**
     * 授权凭证
     */
    @TableField("access_token")
    private String accessToken;

    /**
     * 授权凭证过期时间（时间戳，秒级精度）
     */
    @TableField("expire_timestamp")
    private Integer expireTimestamp;

    /**
     * 授权状态. 1-启用 2-关闭
     */
    @TableField("auth_status")
    private Integer authStatus;

    /**
     * 登出配置信息
     */
    @TableField("logout_path_config_info")
    private String logoutPathConfigInfo;

    /**
     * 扩展参数信息
     */
    @TableField("extend_info")
    private String extendInfo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 授权地址
     */
    @TableField("auth_url")
    private String authUrl;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String ACCOUNT_ID = "account_id";

    public static final String COMPONENT_TYPE = "component_type";

    public static final String ACCESS_TOKEN = "access_token";

    public static final String EXPIRE_TIMESTAMP = "expire_timestamp";

    public static final String AUTH_STATUS = "auth_status";

    public static final String LOGOUT_PATH_CONFIG_INFO = "logout_path_config_info";

    public static final String EXTEND_INFO = "extend_info";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String AUTH_URL = "auth_url";

}
