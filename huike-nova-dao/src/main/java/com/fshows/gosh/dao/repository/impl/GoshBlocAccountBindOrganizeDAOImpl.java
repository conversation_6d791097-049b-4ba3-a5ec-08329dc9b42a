package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshBlocAccountBindOrganizeDO;
import com.fshows.gosh.dao.mapper.GoshBlocAccountBindOrganizeMapper;
import com.fshows.gosh.dao.repository.GoshBlocAccountBindOrganizeDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 集团账号和组织关系 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class GoshBlocAccountBindOrganizeDAOImpl extends ServiceImpl<GoshBlocAccountBindOrganizeMapper, GoshBlocAccountBindOrganizeDO> implements GoshBlocAccountBindOrganizeDAO {

    /**
     * 根据账号id查询
     *
     * @param accountIdList
     * @return
     */
    @Override
    public List<GoshBlocAccountBindOrganizeDO> findByAccountIdList(List<String> accountIdList) {
        return query().in(GoshBlocAccountBindOrganizeDO.ACCOUNT_ID, accountIdList).eq(GoshBlocAccountBindOrganizeDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }

    /**
     * 根据账号id查询
     *
     * @param accountId
     * @return
     */
    @Override
    public List<GoshBlocAccountBindOrganizeDO> getByAccountId(String accountId) {
        return query().eq(GoshBlocAccountBindOrganizeDO.ACCOUNT_ID, accountId).eq(GoshBlocAccountBindOrganizeDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }

    /**
     * 根据账号id查询关联组织id列表
     *
     * @param accountId 账号id
     * @return orgIdList
     */
    @Override
    public List<String> getOrgIdListByAccountId(String accountId) {
        return query().eq(GoshBlocAccountBindOrganizeDO.ACCOUNT_ID, accountId).eq(GoshBlocAccountBindOrganizeDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list().stream().map(GoshBlocAccountBindOrganizeDO::getOrgId).collect(Collectors.toList());
    }

    /**
     * 根据账号id删除
     *
     * @param accountId
     */
    @Override
    public void deleteByAccountId(String accountId) {
        update().set(GoshBlocAccountBindOrganizeDO.IS_DEL, DelFlagEnum.DEL.getValue()).eq(GoshBlocAccountBindOrganizeDO.ACCOUNT_ID, accountId).eq(GoshBlocAccountBindOrganizeDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).update();
    }

    /**
     * 根据组织id删除
     *
     * @param orgId
     */
    @Override
    public void deleteByOrgId(String orgId) {
        update().set(GoshBlocAccountBindOrganizeDO.IS_DEL, DelFlagEnum.DEL.getValue()).eq(GoshBlocAccountBindOrganizeDO.ORG_ID, orgId).update();
    }

    /**
     * 根据组织id查询
     *
     * @param orgId
     * @return
     */
    @Override
    public List<GoshBlocAccountBindOrganizeDO> getByOrgId(String orgId) {
        return query().eq(GoshBlocAccountBindOrganizeDO.ORG_ID, orgId).eq(GoshBlocAccountBindOrganizeDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }
}
