package com.fshows.gosh.dao.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.BlocRolePageListDTO;
import com.fshows.gosh.dao.domain.result.AccountRoleResultDTO;
import com.fshows.gosh.dao.domain.result.BlocRolePageListResultDTO;
import com.fshows.gosh.dao.entity.GoshBlocRoleInfoDO;
import com.fshows.gosh.dao.mapper.GoshBlocRoleInfoMapper;
import com.fshows.gosh.dao.repository.GoshBlocRoleInfoDAO;
import com.google.common.collect.Lists;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.metadata.PageParam;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 角色信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class GoshBlocRoleInfoDAOImpl extends ServiceImpl<GoshBlocRoleInfoMapper, GoshBlocRoleInfoDO> implements GoshBlocRoleInfoDAO {

    /**
     * 根据角色列表查询角色信息
     *
     * @param roleIds
     * @param orgId
     * @return
     */
    @Override
    public List<GoshBlocRoleInfoDO> findByRoleIdList(List<String> roleIds, String orgId) {
        if (CollectionUtil.isEmpty(roleIds)) {
            return Lists.newArrayList();
        }
        return query()
                .in(GoshBlocRoleInfoDO.ROLE_ID, roleIds)
                .eq(GoshBlocRoleInfoDO.ORG_ID, orgId)
                .list();
    }


    /**
     * 根据角色列表查询角色信息
     *
     * @param pageDTO
     * @return
     */
    @Override
    public Page<BlocRolePageListResultDTO> blocRolePageList(PageParam<BlocRolePageListDTO> pageDTO) {
        Page<BlocRolePageListDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());
        return getBaseMapper().blocRolePageList(page, pageDTO.getQuery());
    }

    /**
     * 根据角色名称查询角色信息
     *
     * @param roleName
     * @param orgId
     * @return
     */
    @Override
    public GoshBlocRoleInfoDO getRoleByRoleName(String roleName, String orgId, String roleId) {
        return getBaseMapper().getRoleByRoleName(roleName, orgId, roleId);
    }

    /**
     * 根据角色id查询角色信息
     *
     * @param roleId
     * @return
     */
    @Override
    public GoshBlocRoleInfoDO getRoleByRoleId(String roleId) {
        return query().eq(GoshBlocRoleInfoDO.ROLE_ID, roleId).eq(GoshBlocRoleInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).last("limit 1").one();
    }

    /**
     * 根据角色id修改角色状态
     *
     * @param roleId
     * @param roleStatus
     * @return
     */
    @Override
    public void updateRoleStatus(String roleId, Integer roleStatus) {
        update().set(GoshBlocRoleInfoDO.ROLE_STATUS, roleStatus).eq(GoshBlocRoleInfoDO.ROLE_ID, roleId).eq(GoshBlocRoleInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).update();
    }

    /**
     * 根据角色id删除角色信息
     *
     * @param roleId
     * @return
     */
    @Override
    public void deleteByRoleId(String roleId) {
        update().set(GoshBlocRoleInfoDO.IS_DEL, DelFlagEnum.DEL.getValue()).eq(GoshBlocRoleInfoDO.ROLE_ID, roleId).eq(GoshBlocRoleInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).update();
    }

    /**
     * 根据组织id查询角色列表
     *
     * @param orgPath
     * @return
     */
    @Override
    public List<GoshBlocRoleInfoDO> blocRoleListByOrgId(String orgPath,String orgId) {
        return getBaseMapper().blocRoleListByOrgId(orgPath,orgId);
    }

    /**
     * 根据账号id列表查询角色列表
     *
     * @param accountIdList
     * @return
     */
    @Override
    public List<AccountRoleResultDTO> blocRoleListByAccountIdList(List<String> accountIdList) {
        return getBaseMapper().blocRoleListByAccountIdList(accountIdList);
    }

    /**
     * 根据账号id查询角色列表
     *
     * @param accountId
     * @return
     */
    @Override
    public List<AccountRoleResultDTO> blocRoleListByAccountId(String accountId) {
        return getBaseMapper().blocRoleListByAccountId(accountId);
    }
}
