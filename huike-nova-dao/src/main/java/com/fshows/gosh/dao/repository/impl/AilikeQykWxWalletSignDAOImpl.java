package com.fshows.gosh.dao.repository.impl;

import com.fshows.gosh.dao.entity.AilikeQykWxWalletSignDO;
import com.fshows.gosh.dao.mapper.AilikeQykWxWalletSignMapper;
import com.fshows.gosh.dao.repository.AilikeQykWxWalletSignDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.constant.CommonConstant;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 钱包签约关系记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@Service
public class AilikeQykWxWalletSignDAOImpl extends ServiceImpl<AilikeQykWxWalletSignMapper, AilikeQykWxWalletSignDO> implements AilikeQykWxWalletSignDAO {

    /**
     * 根据入账钱包Id查询钱包签约关系记录
     *
     * @param inWalletId 入账钱包Id
     * @return
     */
    @Override
    public AilikeQykWxWalletSignDO findByInWalletId(String inWalletId) {
        return query().eq(AilikeQykWxWalletSignDO.IN_WALLET_ID, inWalletId)
                .eq(AilikeQykWxWalletSignDO.IS_DEL, CommonConstant.NUMBER_ZERO)
                .last("limit 1").one();
    }

    /**
     * 根据业务请求签约Id查询钱包签约关系记录
     *
     * @param businessSignId 业务请求签约Id
     * @return
     */
    @Override
    public AilikeQykWxWalletSignDO findByBusinessSignId(String businessSignId) {
        return query().eq(AilikeQykWxWalletSignDO.BUSINESS_SIGN_ID, businessSignId)
                .eq(AilikeQykWxWalletSignDO.IS_DEL, CommonConstant.NUMBER_ZERO)
                .last("limit 1").one();
    }
}
