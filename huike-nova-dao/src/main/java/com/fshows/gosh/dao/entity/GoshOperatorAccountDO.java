package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 来逛呗运营后台账号
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@Data
@TableName("gosh_operator_account")
public class GoshOperatorAccountDO implements Serializable {

    public static final String ID = "id";
    public static final String OPERATOR_ID = "operator_id";
    public static final String ACCOUNT = "account";
    public static final String PASSWORD = "password";
    public static final String SALT = "salt";
    public static final String ACCOUNT_STATUS = "account_status";
    public static final String IS_ADMIN = "is_admin";
    public static final String LAST_LOGIN_TIME = "last_login_time";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    public static final String PERSON_NAME = "person_name";
    public static final String PHONE = "phone";
    public static final String PLAIN_TEXT = "plain_text";

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 运营用户id
     */
    @TableField("operator_id")
    private String operatorId;
    /**
     * 账号
     */
    @TableField("account")
    private String account;
    /**
     * 密码
     */
    @TableField("password")
    private String password;
    /**
     * 密码盐
     */
    @TableField("salt")
    private String salt;
    /**
     * 状态:1-正常 2-禁用
     */
    @TableField("account_status")
    private Integer accountStatus;
    /**
     * 是否为管理员 1-是 2-否
     */
    @TableField("is_admin")
    private Integer isAdmin;
    /**
     * 最后一次登录时间
     */
    @TableField("last_login_time")
    private Integer lastLoginTime;
    /**
     * 0正常 1删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 姓名
     */
    @TableField("person_name")
    private String personName;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 明文权限 0-关闭 1-开启
     */
    @TableField("plain_text")
    private Integer plainText;

    /**
     * 账号类型:1-全集团账号；2-部分集团权限账号
     */
    @TableField("account_type")
    private Integer accountType;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

}
