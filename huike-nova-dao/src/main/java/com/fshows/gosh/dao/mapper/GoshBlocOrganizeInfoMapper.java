package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.FindPoiOrgListParamDTO;
import com.fshows.gosh.dao.domain.param.OrganizePageListDTO;
import com.fshows.gosh.dao.domain.param.PlatformRolePoiOrgListParamDTO;
import com.fshows.gosh.dao.domain.result.CountSquareResultDTO;
import com.fshows.gosh.dao.domain.result.FindBlocOrgByOrgIdListResultDTO;
import com.fshows.gosh.dao.domain.result.FindByAccountIdListResultDTO;
import com.fshows.gosh.dao.domain.result.FindProductOrgListResultDTO;
import com.fshows.gosh.dao.domain.result.GetOrderOrgInfoResultDTO;
import com.fshows.gosh.dao.domain.result.PlatformRolePoiOrgResultDTO;
import com.fshows.gosh.dao.domain.result.PoiAndOrgNameResultDTO;
import com.fshows.gosh.dao.domain.result.QuerySquareListDTO;
import com.fshows.gosh.dao.entity.GoshBlocOrganizeInfoDO;
import com.huike.nova.dao.entity.AilikeMerchantStoreDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 来逛呗集团组织信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshBlocOrganizeInfoMapper extends BaseMapper<GoshBlocOrganizeInfoDO> {

    /**
     * 组织列表
     *
     * @param page
     * @param organizePageListDTO
     * @return
     */
    Page<GoshBlocOrganizeInfoDO> organizePageList(Page<OrganizePageListDTO> page, @Param("dto") OrganizePageListDTO organizePageListDTO);

    /**
     * 组织列表
     *
     * @param dto 入参
     * @return 组织列表
     */
    List<GoshBlocOrganizeInfoDO> organizeList(@Param("dto") OrganizePageListDTO dto);

    /**
     * 根据组织id获取组织名称
     *
     * @param orgId 组织id
     * @return 组织名称
     */
    String getOrgNameByOrgId(@Param("orgId") String orgId);

    /**
     * 获取账号下集团的所有组织
     *
     * @param blocId
     * @param accountId
     * @param orgId
     * @return
     */
    List<GoshBlocOrganizeInfoDO> getByAccountId(@Param("blocId") String blocId, @Param("accountId") String accountId, @Param("orgId") String orgId);

    /**
     * 根据账号列表获取组织列表
     *
     * @param accountIdList 账号列表
     * @return 组织列表
     */
    List<FindByAccountIdListResultDTO> findByAccountIdList(@Param("accountIdList") List<String> accountIdList);

    /**
     * 根据账号获取组织列表
     *
     * @param accountId 账号
     * @return 组织列表
     */
    List<FindByAccountIdListResultDTO> findByAccountId(@Param("accountId") String accountId);

    /**
     * 根据组织id列表查询组织广场数量
     *
     * @param orgIdList 组织id列表
     * @return 组织数量
     */
    List<CountSquareResultDTO> countSquareCountByOrgIdList(@Param("orgIdList") List<String> orgIdList);

    /**
     * 查询下级组织名称和poiId
     *
     * @param blocId
     * @param orgPath
     * @param orgType
     * @return
     */
    List<PoiAndOrgNameResultDTO> findPoiAndOrgNameByFullPath(@Param("blocId") String blocId, @Param("orgPath") String orgPath, @Param("orgType") Integer orgType);

    /**
     * 根据orgIdList获取storeIdList
     *
     * @return 组织信息
     */
    List<String> getStoreIdListByOrgIdList(@Param("orgIdList") List<String> orgIdList);

    /**
     * 组织列表
     *
     * @param dto 入参
     * @return 出参
     */
    List<GoshBlocOrganizeInfoDO> findPoiOrgList(@Param("dto") FindPoiOrgListParamDTO dto);

    /**
     * 查询有平台权限的组织
     *
     * @param dto 入参
     * @return 出参
     */
    List<PlatformRolePoiOrgResultDTO> findPlatformRolePoiOrgList(@Param("dto") PlatformRolePoiOrgListParamDTO dto);


    /**
     * 查询有权限的组织
     *
     * @param dto 入参
     * @return 出参
     */
    List<PlatformRolePoiOrgResultDTO> findRolePoiOrgList(@Param("dto") PlatformRolePoiOrgListParamDTO dto);

    /**
     * 根据组织id查询店铺信息
     *
     * @param orgId
     * @return
     */
    AilikeMerchantStoreDO getMerchantStoreInfoByOrgId(String orgId);

    /**
     * 根据业务商品id查询组织信息
     *
     * @param businessProductIdList 商品id列表
     * @param merchantId
     * @return
     */
    List<FindProductOrgListResultDTO> findProductOrgList(@Param("businessProductIdList") List<String> businessProductIdList, @Param("merchantId") String merchantId);

    /**
     * 根据集团id获取组织id列表
     *
     * @param blocId  集团id
     * @param orgType 1组织 2广场
     * @return 出参
     */
    List<String> getOrgIdListByBlocId(@Param("blocId") String blocId, @Param("orgType") Integer orgType);

    /**
     * 根据组织id列表查询广场信息列表
     *
     * @param orgId 组织id
     * @return 组织信息列表
     */
    List<QuerySquareListDTO> querySquareList(@Param("orgId") String orgId, @Param("blocId") String blocId);

    /**
     * 根据组织id列表查询商品id列表
     *
     * @param orgIdList 组织id列表
     * @return 商品id列表
     */
    List<String> getProductIdListByPrgIdList(@Param("orgIdList") List<String> orgIdList);

    /**
     * 根据商品id查询组织信息
     *
     * @param businessProductId 商品id
     * @param merchantId
     * @return 组织信息
     */
    GetOrderOrgInfoResultDTO getOrderOrgInfo(@Param("businessProductId") String businessProductId, @Param("merchantId") String merchantId);

    /**
     * 根据组织id列表查询组织集团信息
     *
     * @param orgIdList 组织id列表
     * @return 组织列表
     */
    List<FindBlocOrgByOrgIdListResultDTO> findBlocOrgByOrgIdList(@Param("orgIdList") List<String> orgIdList);

    /**
     * 根据组织id获取广场列表信息
     *
     * @param orgId  组织id
     * @param blocId
     * @return 广场信息列表
     */
    List<GoshBlocOrganizeInfoDO> findSquareList(@Param("orgId") String orgId, @Param("blocId") String blocId);
}
