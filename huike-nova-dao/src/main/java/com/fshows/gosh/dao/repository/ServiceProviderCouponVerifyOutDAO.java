package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.result.OutProductSettleAmountResultDTO;
import com.fshows.gosh.dao.entity.ServiceProviderCouponVerifyOutDO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 外部商品服务商核销卡券记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
public interface ServiceProviderCouponVerifyOutDAO extends IService<ServiceProviderCouponVerifyOutDO> {
    /**
     * @param appId       appId
     * @param settleDay   结算日
     * @param poiIdList   广场列表
     * @param channelType 通道
     */
    OutProductSettleAmountResultDTO statisticsSettleAmount(String appId, Integer settleDay, List<String> poiIdList, String channelType, String channelCode);

    /**
     * 根据appid统计差异金额
     *
     * @param appId       appId
     * @param settleDay   结算日
     * @param channelType 通道
     * @param dataType    数据类型
     */
    BigDecimal statisticsDifferenceAmount(String appId, Integer settleDay, String channelType, String channelCode, String dataType, Integer orgProductType);

    /**
     * 修改差异数据的结算单号
     *
     * @param appId       appId
     * @param settleDay   结算日
     * @param channelType 通道
     */
    void modifySerialNo(String appId, Integer settleDay, String channelType, String channelCode, String serialNo);
}
