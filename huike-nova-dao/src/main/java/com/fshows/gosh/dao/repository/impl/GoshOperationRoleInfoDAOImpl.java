/**
 * <AUTHOR>
 * @date 2024/11/13 19:09
 * @version 1.0 GoshOperationRoleInfoDAOImpl
 */
package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.param.SearchOperationRoleParamDTO;
import com.fshows.gosh.dao.domain.result.SearchOperationAccountResultDTO;
import com.fshows.gosh.dao.domain.result.SearchOperationRoleResultDTO;
import com.fshows.gosh.dao.entity.GoshOperatorRoleInfoDO;
import com.fshows.gosh.dao.mapper.GoshOperationRoleInfoMapper;
import com.fshows.gosh.dao.repository.GoshOperationRoleInfoDAO;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import org.springframework.stereotype.Service;

/**
 *
 *
 * <AUTHOR>
 * @version GoshOperationRoleInfoDAOImpl.java, v 0.1 2024-11-13 19:09 tuyuwei
 */
@Service
public class GoshOperationRoleInfoDAOImpl extends ServiceImpl<GoshOperationRoleInfoMapper, GoshOperatorRoleInfoDO> implements GoshOperationRoleInfoDAO {


    @Override
    public void updateByRoleId(GoshOperatorRoleInfoDO goshOperatorRoleInfoDO) {
        update().set(GoshOperatorRoleInfoDO.ROLE_NAME, goshOperatorRoleInfoDO.getRoleName())
                .set(GoshOperatorRoleInfoDO.REMARK, goshOperatorRoleInfoDO.getRemark())
                .eq(GoshOperatorRoleInfoDO.ROLE_ID, goshOperatorRoleInfoDO.getRoleId())
                .eq(GoshOperatorRoleInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .update();
    }

    @Override
    public void updateStatusByRoleId(GoshOperatorRoleInfoDO goshOperatorRoleInfoDO) {
        update().set(GoshOperatorRoleInfoDO.ROLE_STATUS, goshOperatorRoleInfoDO.getRoleStatus())
                .eq(GoshOperatorRoleInfoDO.ROLE_ID, goshOperatorRoleInfoDO.getRoleId())
                .eq(GoshOperatorRoleInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .update();
    }

    @Override
    public Page<SearchOperationRoleResultDTO> searchRoleList(PageParam<SearchOperationRoleParamDTO> pageDTO) {
        Page<SearchOperationRoleParamDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());
        return getBaseMapper().searchRoleList(page, pageDTO.getQuery());
    }

    @Override
    public GoshOperatorRoleInfoDO searchRoleInfo(String roleId) {
        return query().eq(GoshOperatorRoleInfoDO.ROLE_ID, roleId)
                .eq(GoshOperatorRoleInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .one();
    }

    @Override
    public void deleteRoleInfo(String roleId) {
        getBaseMapper().update(null, new LambdaUpdateWrapper<GoshOperatorRoleInfoDO>()
                .eq(GoshOperatorRoleInfoDO::getRoleId, roleId)
                .set(GoshOperatorRoleInfoDO::getIsDel, CommonConstant.INTEGER_ONE));
    }
}