package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 抖音服务商配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Data
@TableName("ailike_tiktok_service_provider")
public class AilikeTiktokServiceProviderDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用appId
     */
    @TableField("app_id")
    private String appId;

    /**
     * 抖音来客Id
     */
    @TableField("tiktok_life_id")
    private String tiktokLifeId;

    /**
     * 商户id
     */
    @TableField("merchant_agent_id")
    private String merchantAgentId;

    /**
     * SPI的地址信息
     */
    @TableField("spi_host_config")
    private String spiHostConfig;

    /**
     * 删除标记:1-未删除  2-已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 佣金费率
     */
    @TableField("commission_rate")
    private Integer commissionRate;

    /**
     * 抖音商品审核机制 1 商家审核 2 商家免审
     */
    @TableField("dy_merchant_audit_type")
    private Integer dyMerchantAuditType;

    /**
     * 白名单类目
     */
    @TableField("white_category_code")
    private String whiteCategoryCode;

    /**
     * 抖音结算周期
     */
    @TableField("settlement_cycle")
    private String settlementCycle;

    /**
     * 支持的商品类型1-团购券 11-代金券  12-预授权  15-次卡
     */
    @TableField("product_type")
    private String productType;

    /**
     * 降级开关:1-开启降级  0-关闭降级
     */
    @TableField("downgrading_switch")
    private Integer downgradingSwitch;

    /**
     * 是否为品牌客户类型：1-是；0-否
     */
    @TableField("brand_type")
    private Integer brandType;

    /**
     * poi库存管理权限: 1-有权限;0-没有权限 （宝龙场景）
     */
    @TableField("poi_stock_role")
    private Integer poiStockRole;

    /**
     * 组织机构层级 万达3级；宝龙2级
     */
    @TableField("org_level")
    private Integer orgLevel;

    /**
     * 代金券支持最大的面值
     */
    @TableField("coupon_max_amount")
    private BigDecimal couponMaxAmount;

    /**
     * 商户归属的服务商名称
     */
    @TableField("service_name")
    private String serviceName;

    /**
     * 商品批量上传门店权限:0-没权限；1-有权限
     */
    @TableField("upload_store_auth")
    private Integer uploadStoreAuth;

    /**
     * 降级文案
     */
    @TableField("downgrading_doc")
    private String downgradingDoc;

    /**
     * 支付宝团购权限:1-有权限;2-没有权限;3-开启后又关闭
     */
    @TableField("alipay_auth")
    private Integer alipayAuth;

    /**
     * 支付宝支持的商品类型1-团购;11-代金券
     */
    @TableField("alipay_product_type")
    private String alipayProductType;

    /**
     * 支付宝结算周期
     */
    @TableField("alipay_settlement_cycle")
    private String alipaySettlementCycle;

    /**
     * 支付宝小程序appid
     */
    @TableField("alipay_mina_app_id")
    private String alipayMinaAppId;

    /**
     * 支付宝小程序公钥
     */
    @TableField("alipay_mina_public_key")
    private String alipayMinaPublicKey;

    /**
     * 支付宝小程序私钥
     */
    @TableField("alipay_mina_private_key")
    private String alipayMinaPrivateKey;

    /**
     * 支付宝平台公钥
     */
    @TableField("alipay_public_key")
    private String alipayPublicKey;

    /**
     * 支付宝商家名称
     */
    @TableField("alipay_merchant_name")
    private String alipayMerchantName;

    /**
     * 支付宝类目白名单
     */
    @TableField("alipay_category_white")
    private String alipayCategoryWhite;

    /**
     * 支付宝poi库存管理权限: 1-有权限;0-没有权限 （宝龙场景）
     */
    @TableField("alipay_poi_stock_role")
    private Integer alipayPoiStockRole;

    /**
     * AES私钥
     */
    @TableField("alipay_aes_private_key")
    private String alipayAesPrivateKey;

    /**
     * 抖音团购权限:1-有权限;2-没有权限;3-开启后又关闭
     */
    @TableField("tiktok_auth")
    private Integer tiktokAuth;

    /**
     * 支付宝券最低限额
     */
    @TableField("alipay_coupon_min_amount")
    private BigDecimal alipayCouponMinAmount;

    /**
     * 权益卡权限 0关闭 1开启 2灰度
     */
    @TableField("qyk_switch")
    private Integer qykSwitch;

    /**
     * 商品收款方式：1-总店结算； 2-分店结算； 3-区域结算
     */
    @TableField("settle_type")
    private Integer settleType;

    /**
     * 抖音来客的cookie
     */
    @TableField("life_cookie")
    private String lifeCookie;

    /**
     * 集团名称
     */
    @TableField("org_name")
    private String orgName;

    /**
     * 主题颜色配置
     */
    @TableField("theme_config")
    private String themeConfig;

    /**
     * 权益卡类目白名单
     */
    @TableField("qyk_category_white")
    private String qykCategoryWhite;

    /**
     * 最高抽佣比例
     */
    @TableField("max_commission_rate")
    private Integer maxCommissionRate;

    /**
     * 对应的商家Id
     */
    @TableField("merchant_id")
    private String merchantId;

    /**
     * 结算方式：0-正常结算；1-100%佣金结算 （塘栖古镇100%抽佣场景）
     */
    @TableField("settlement_type")
    private Integer settlementType;

    /**
     * 对账单开关 0开启 1关闭
     */
    @TableField("reconciliation_file_switch")
    private Integer reconciliationFileSwitch;

    /**
     * 主号来客Id（门店代运营后爬数据使用）
     */
    @TableField("life_account_id")
    private String lifeAccountId;

    /**
     * 广场来客id（用于财务对账数据爬取）
     */
    @TableField("poi_account_id")
    private String poiAccountId;

    /**
     * 是否同步订单：0-不同步 1-同步
     */
    @TableField("auto_sync_orders")
    private Integer autoSyncOrders;

    /**
     * 同步订单的POI门店，支持多门店，不填默认同步accountId下的所有门店
     */
    @TableField("sync_orders_poi_list")
    private String syncOrdersPoiList;

    /**
     * 外部商品结算的门店Id(来逛呗的门店id)
     */
    @TableField("out_product_settle_shop_id")
    private String outProductSettleShopId;

    /**
     * 外部商品指定poiId出款
     */
    @TableField("out_product_poi_id")
    private String outProductPoiId;

    /**
     * 差异对账下载源1 对账单 2提现记录
     */
    @TableField("reconciliation_variance_download_source")
    private Integer reconciliationVarianceDownloadSource;

    /**
     * 最后的订单同步的时间
     */
    @TableField("sync_orders_time")
    private String syncOrdersTime;

    /**
     * 同步订单额外参数（预留字段）
     */
    @TableField("sync_orders_extra")
    private String syncOrdersExtra;

    /**
     * 删除来客下载对账单文件标识 1-删除；2-不删除
     */
    @TableField("del_file_flag")
    private Integer delFileFlag;

    /**
     * 是否使用RPA接口核券：1-是 0-否
     */
    @TableField("rpa_verify_flag")
    private Integer rpaVerifyFlag;

    /**
     * 使用RPA核销时的POI ID
     */
    @TableField("rpa_verify_poi")
    private String rpaVerifyPoi;

    /**
     * 使用RPA核销时的POI Account ID
     */
    @TableField("rpa_verify_poi_account_id")
    private String rpaVerifyPoiAccountId;

    /**
     * 使用RPA查询的AccountId
     */
    @TableField("rpa_query_order_account_id")
    private String rpaQueryOrderAccountId;

    /**
     * 是否开启核销工具：1-开启 0-关闭
     */
    @TableField("open_verify_tool")
    private Integer openVerifyTool;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String APP_ID = "app_id";

    public static final String TIKTOK_LIFE_ID = "tiktok_life_id";

    public static final String MERCHANT_AGENT_ID = "merchant_agent_id";

    public static final String SPI_HOST_CONFIG = "spi_host_config";

    public static final String DEL_FLAG = "del_flag";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String COMMISSION_RATE = "commission_rate";

    public static final String DY_MERCHANT_AUDIT_TYPE = "dy_merchant_audit_type";

    public static final String WHITE_CATEGORY_CODE = "white_category_code";

    public static final String SETTLEMENT_CYCLE = "settlement_cycle";

    public static final String PRODUCT_TYPE = "product_type";

    public static final String DOWNGRADING_SWITCH = "downgrading_switch";

    public static final String BRAND_TYPE = "brand_type";

    public static final String POI_STOCK_ROLE = "poi_stock_role";

    public static final String ORG_LEVEL = "org_level";

    public static final String COUPON_MAX_AMOUNT = "coupon_max_amount";

    public static final String SERVICE_NAME = "service_name";

    public static final String UPLOAD_STORE_AUTH = "upload_store_auth";

    public static final String DOWNGRADING_DOC = "downgrading_doc";

    public static final String ALIPAY_AUTH = "alipay_auth";

    public static final String ALIPAY_PRODUCT_TYPE = "alipay_product_type";

    public static final String ALIPAY_SETTLEMENT_CYCLE = "alipay_settlement_cycle";

    public static final String ALIPAY_MINA_APP_ID = "alipay_mina_app_id";

    public static final String ALIPAY_MINA_PUBLIC_KEY = "alipay_mina_public_key";

    public static final String ALIPAY_MINA_PRIVATE_KEY = "alipay_mina_private_key";

    public static final String ALIPAY_PUBLIC_KEY = "alipay_public_key";

    public static final String ALIPAY_MERCHANT_NAME = "alipay_merchant_name";

    public static final String ALIPAY_CATEGORY_WHITE = "alipay_category_white";

    public static final String ALIPAY_POI_STOCK_ROLE = "alipay_poi_stock_role";

    public static final String ALIPAY_AES_PRIVATE_KEY = "alipay_aes_private_key";

    public static final String TIKTOK_AUTH = "tiktok_auth";

    public static final String ALIPAY_COUPON_MIN_AMOUNT = "alipay_coupon_min_amount";

    public static final String QYK_SWITCH = "qyk_switch";

    public static final String SETTLE_TYPE = "settle_type";

    public static final String LIFE_COOKIE = "life_cookie";

    public static final String ORG_NAME = "org_name";

    public static final String THEME_CONFIG = "theme_config";

    public static final String QYK_CATEGORY_WHITE = "qyk_category_white";

    public static final String MAX_COMMISSION_RATE = "max_commission_rate";

    public static final String MERCHANT_ID = "merchant_id";

    public static final String SETTLEMENT_TYPE = "settlement_type";

    public static final String RECONCILIATION_FILE_SWITCH = "reconciliation_file_switch";

    public static final String LIFE_ACCOUNT_ID = "life_account_id";

    public static final String POI_ACCOUNT_ID = "poi_account_id";

    public static final String AUTO_SYNC_ORDERS = "auto_sync_orders";

    public static final String SYNC_ORDERS_POI_LIST = "sync_orders_poi_list";

    public static final String OUT_PRODUCT_SETTLE_SHOP_ID = "out_product_settle_shop_id";

    public static final String OUT_PRODUCT_POI_ID = "out_product_poi_id";

    public static final String RECONCILIATION_VARIANCE_DOWNLOAD_SOURCE = "reconciliation_variance_download_source";

    public static final String SYNC_ORDERS_TIME = "sync_orders_time";

    public static final String SYNC_ORDERS_EXTRA = "sync_orders_extra";

    public static final String DEL_FILE_FLAG = "del_file_flag";

    public static final String RPA_VERIFY_FLAG = "rpa_verify_flag";

    public static final String RPA_VERIFY_POI = "rpa_verify_poi";

    public static final String RPA_VERIFY_POI_ACCOUNT_ID = "rpa_verify_poi_account_id";

    public static final String RPA_QUERY_ORDER_ACCOUNT_ID = "rpa_query_order_account_id";

    public static final String OPEN_VERIFY_TOOL = "open_verify_tool";

}
