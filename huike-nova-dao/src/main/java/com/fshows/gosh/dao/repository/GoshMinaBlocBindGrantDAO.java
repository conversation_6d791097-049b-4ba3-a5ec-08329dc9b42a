package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshMinaBlocBindGrantDO;

import java.util.List;

/**
 * <p>
 * 商家版小程序-集团绑定权限 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface GoshMinaBlocBindGrantDAO extends IService<GoshMinaBlocBindGrantDO> {

    /**
     * 根据集团id查询集团小程序绑定权限
     *
     * @param blocId 集团id
     * @return 集团绑定权限
     */
    List<String> findBlocMinaGrantIdListByBlocId(String blocId);

    /**
     * 根据集团 id 删除集团权限
     *
     * @param blocId 集团 id
     */
    void deleteByBlocId(String blocId);
}
