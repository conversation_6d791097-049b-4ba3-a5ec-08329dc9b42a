package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 商户转账表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
@TableName("gosh_shop_transfer")
public class GoshShopTransferDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商铺id
     */
    @TableField("shop_id")
    private String shopId;

    /**
     * 账户id
     */
    @TableField("account_id")
    private String accountId;

    /**
     * 商铺结算单号 gosh_shop_settle_form shop_serial_no
     */
    @TableField("shop_serial_no")
    private String shopSerialNo;

    /**
     * 转账订单号
     */
    @TableField("transfer_no")
    private String transferNo;

    /**
     * 原始转账订单号
     */
    @TableField("original_transfer_no")
    private String originalTransferNo;

    /**
     * 平台方转账订单号
     */
    @TableField("platform_transfer_no")
    private String platformTransferNo;

    /**
     * 转账日期(yyyymmdd)
     */
    @TableField("transfer_date")
    private String transferDate;

    /**
     * 出账方钱包id
     */
    @TableField("out_wallet_id")
    private String outWalletId;

    /**
     * 提现金额
     */
    @TableField("transfer_amount")
    private BigDecimal transferAmount;

    /**
     * 提现手续费，单位：元
     */
    @TableField("transfer_fee")
    private BigDecimal transferFee;

    /**
     * 转账状态  TRANSFER_INIT-未转账;TRANSFER_PROCESSING-转账中;TRANSFER_SUCCESS-转账成功;TRANSFER_FAIL-转账失败
     */
    @TableField("transfer_status")
    private String transferStatus;

    /**
     * 异常原因
     */
    @TableField("rejected_reason")
    private String rejectedReason;

    /**
     * 来源 BATCH-脚本
     */
    @TableField("source")
    private String source;

    /**
     * 平台类型 TIKTOK-抖音 ALIPAY-支付宝 MEITUAN-美团
     */
    @TableField("platform_type")
    private String platformType;

    /**
     * 转账完成时间
     */
    @TableField("finish_time")
    private Date finishTime;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 关联提现单号
     */
    @TableField("withdraw_no")
    private String withdrawNo;

    public Date getFinishTime() {
        if (this.finishTime != null) {
          return new Date(this.finishTime.getTime());
        } else {
          return null;
        }
    }

    public void setFinishTime(Date finishTime) {
        if (finishTime != null) {
            this.finishTime = new Date(finishTime.getTime());
        } else {
            this.finishTime = null;
        }
    }
    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String SHOP_ID = "shop_id";

    public static final String ACCOUNT_ID = "account_id";

    public static final String SHOP_SERIAL_NO = "shop_serial_no";

    public static final String TRANSFER_NO = "transfer_no";

    public static final String PLATFORM_TRANSFER_NO = "platform_transfer_no";

    public static final String TRANSFER_DATE = "transfer_date";

    public static final String OUT_WALLET_ID = "out_wallet_id";

    public static final String TRANSFER_AMOUNT = "transfer_amount";

    public static final String TRANSFER_FEE = "transfer_fee";

    public static final String TRANSFER_STATUS = "transfer_status";

    public static final String REJECTED_REASON = "rejected_reason";

    public static final String SOURCE = "source";

    public static final String PLATFORM_TYPE = "platform_type";

    public static final String FINISH_TIME = "finish_time";

    public static final String IS_DEL = "is_del";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String WITHDRAW_NO = "withdraw_no";

}
