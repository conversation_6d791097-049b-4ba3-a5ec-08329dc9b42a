package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshBlocBalanceChangeRecordDO;
import com.fshows.gosh.dao.mapper.GoshBlocBalanceChangeRecordMapper;
import com.fshows.gosh.dao.repository.GoshBlocBalanceChangeRecordDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 账户余额变更记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Service
public class GoshBlocBalanceChangeRecordDAOImpl extends ServiceImpl<GoshBlocBalanceChangeRecordMapper, GoshBlocBalanceChangeRecordDO> implements GoshBlocBalanceChangeRecordDAO {

    /**
     * 查下余额变更记录是否存在
     *
     * @param changeSn
     * @param balanceId
     * @param changeType
     * @return
     */
    @Override
    public GoshBlocBalanceChangeRecordDO getByChangeSn(String changeSn, String balanceId, String changeType) {
        return query()
                .eq(GoshBlocBalanceChangeRecordDO.CHANGE_SN, changeSn)
                .eq(GoshBlocBalanceChangeRecordDO.BALANCE_ID, balanceId)
                .eq(GoshBlocBalanceChangeRecordDO.CHANGE_TYPE, changeType)
                .eq(GoshBlocBalanceChangeRecordDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }
}
