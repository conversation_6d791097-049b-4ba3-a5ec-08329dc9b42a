package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.refund.PageRefundParamDTO;
import com.fshows.gosh.dao.domain.result.refund.PageRefundResultDTO;
import com.fshows.gosh.dao.entity.GoshRpaDouyinLifeRefundApplyDO;
import com.fshows.gosh.dao.mapper.GoshRpaDouyinLifeRefundApplyMapper;
import com.fshows.gosh.dao.repository.GoshRpaDouyinLifeRefundApplyDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.dao.entity.QykAppUserMerchantCardDO;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 抖音RPA退款订单申请 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Service
public class GoshRpaDouyinLifeRefundApplyDAOImpl extends ServiceImpl<GoshRpaDouyinLifeRefundApplyMapper, GoshRpaDouyinLifeRefundApplyDO> implements GoshRpaDouyinLifeRefundApplyDAO {

    /**
     * 分页查询线上退款列表
     *
     * @param paramDTO 参数
     * @return 结果
     */
    @Override
    public Page<PageRefundResultDTO> pageRefundList(PageParam<PageRefundParamDTO> paramDTO) {
        Page<PageRefundResultDTO> page = new Page<>();
        page.setCurrent(paramDTO.getPage());
        page.setSize(paramDTO.getPageSize());
        return getBaseMapper().pageRefundList(page, paramDTO.getQuery());
    }

    /**
     * 导出线上退款申请数据
     *
     * @param dto           参数
     * @param resultHandler 数据
     */
    @Override
    public void exportRefundData(PageRefundParamDTO dto, ResultHandler<PageRefundResultDTO> resultHandler) {
        getBaseMapper().pageRefundList(dto, resultHandler);
    }

    /**
     * 根据售后id查询数据
     *
     * @param afterSaleId 售后id
     * @return 数据
     */
    @Override
    public GoshRpaDouyinLifeRefundApplyDO findByAfterSaleId(String afterSaleId) {
        return query().eq(GoshRpaDouyinLifeRefundApplyDO.AFTER_SALE_ID, afterSaleId)
                .eq(GoshRpaDouyinLifeRefundApplyDO.IS_DEL, CommonConstant.NUMBER_ZERO)
                .last("limit 1").one();
    }

    /**
     * 根据订单号查询线上售后订单
     *
     * @param outOrderSn 平台订单号
     * @return 数据
     */
    @Override
    public List<GoshRpaDouyinLifeRefundApplyDO> findListByOutOrderSn(String outOrderSn) {
        return query().eq(GoshRpaDouyinLifeRefundApplyDO.OUT_ORDER_SN, outOrderSn)
                .eq(GoshRpaDouyinLifeRefundApplyDO.IS_DEL, CommonConstant.NUMBER_ZERO)
                .list();
    }
}
