package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshShopSettleFormDO;

import java.util.List;

/**
 * <p>
 * 商铺结算单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface GoshShopSettleFormDAO extends IService<GoshShopSettleFormDO> {

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    boolean batchShopSettleForm(List<GoshShopSettleFormDO> list);

    /**
     * 根据serialNo删除
     *
     * @param serialNo
     * @return
     */
    boolean removeBySerialNo(String serialNo);


    /**
     * 根据结算单号查询结算明细
     *
     * @param serialNo
     * @return {@link List}<{@link GoshShopSettleFormDO}>
     */
    List<GoshShopSettleFormDO> getListBySerialNo(String serialNo);


    /**
     * 根据商户结算单号获取唯一商户结算单
     *
     * @param shopSerialNo
     * @return {@link GoshShopSettleFormDO}
     */
    GoshShopSettleFormDO getByShopSerialNo(String shopSerialNo);

    /**
     * 修改结算单状态
     *
     * @param shopSerialNo
     * @param settleFormStatus
     * @return boolean
     */
    boolean updateSettleFormStatusAndReason(String shopSerialNo, Integer settleFormStatus, String reason);

    /**
     * 根据状态查询列表
     *
     * @param accountId
     * @param settleFormStatus
     * @return
     */
    List<GoshShopSettleFormDO> getDealListByAccountId(String accountId, Integer settleFormStatus);


    /**
     * 更新状态和原因
     *
     * @param shopSerialNo
     * @param settleFormStatus
     * @return boolean
     */
    boolean updateStatusAndResetReason(String shopSerialNo, Integer settleFormStatus);

    /**
     * 更新补单任务Id
     *
     * @param shopSerialNoList 门店结算单号
     * @param taskId           补单任务Id
     * @return
     */
    boolean updateTaskIdByShopSerialNoList(List<String> shopSerialNoList, String taskId);

    /**
     * 根据补单任务id查询门店结算数据
     *
     * @param taskId 补单任务id
     * @return
     */
    List<GoshShopSettleFormDO> getListByTaskId(String taskId);

    /**
     * 根据结算单号将失败的数据进行初始化
     *
     * @param serialNo                 结算单号
     * @param shopSettleFormStatusList 门店结算状态
     * @return
     */
    boolean updateInitBySerialNo(String serialNo, List<Integer> shopSettleFormStatusList);

    /**
     * 根据门店结算单号数据进行初始化
     *
     * @param shopSerialNoList 门店结算单号
     * @return
     */
    boolean updateInitByShopSerialNoList(List<String> shopSerialNoList);

    /**
     * 根据门店结算单号查询列表数据
     *
     * @param shopSerialNoList 门店结算单号
     * @return
     */
    List<GoshShopSettleFormDO> getListByShopSerialNoList(List<String> shopSerialNoList);

    /**
     * 查询处理中的门店结算数据
     *
     * @param shopId 门店Id
     * @return
     */
    List<GoshShopSettleFormDO> getProcessingListByShopId(String shopId);

    /**
     * 查询门店结算失败的数量
     *
     * @param shopId 门店id
     * @return 结算失败的数量
     */
    Integer failCountByShopId(String shopId);
}
