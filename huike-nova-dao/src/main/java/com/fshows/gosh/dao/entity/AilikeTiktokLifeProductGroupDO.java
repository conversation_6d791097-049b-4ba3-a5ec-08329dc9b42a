package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 抖音来客商品组表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Data
@TableName("ailike_tiktok_life_product_group")
public class AilikeTiktokLifeProductGroupDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务商品Id
     */
    @TableField("business_product_id")
    private String businessProductId;

    /**
     * 业务商品组Id
     */
    @TableField("business_group_id")
    private String businessGroupId;

    /**
     * 组名
     */
    @TableField("group_name")
    private String groupName;

    /**
     * 组选项，比如组中有3个选项  3选1:1; 3选2:2; 全部选择:3
     */
    @TableField("option_count")
    private Integer optionCount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除标记   1未删除   2已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 选项名称
     */
    @TableField("option_name")
    private String optionName;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String BUSINESS_PRODUCT_ID = "business_product_id";

    public static final String BUSINESS_GROUP_ID = "business_group_id";

    public static final String GROUP_NAME = "group_name";

    public static final String OPTION_COUNT = "option_count";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String DEL_FLAG = "del_flag";

    public static final String OPTION_NAME = "option_name";

}
