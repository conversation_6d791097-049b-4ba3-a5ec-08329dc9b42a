package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.GoshOfflineRefundOperationRecordDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 线下退款操作记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
public interface GoshOfflineRefundOperationRecordDAO extends IService<GoshOfflineRefundOperationRecordDO> {

    /**
     * 根据paymentId查询操作记录列表
     *
     * @param paymentId 打款id
     * @return 操作记录列表
     */
    List<GoshOfflineRefundOperationRecordDO> findByPaymentId(String paymentId);

    /**
     * 根据recordId查询最新的操作记录
     *
     * @param recordId 记录ID
     * @return 最新的操作记录
     */
    GoshOfflineRefundOperationRecordDO findLatestByRecordId(String recordId);
}
