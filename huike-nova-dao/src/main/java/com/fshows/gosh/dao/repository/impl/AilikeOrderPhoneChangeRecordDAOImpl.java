package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.AilikeOrderPhoneChangeRecordDO;
import com.fshows.gosh.dao.mapper.AilikeOrderPhoneChangeRecordMapper;
import com.fshows.gosh.dao.repository.AilikeOrderPhoneChangeRecordDAO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 订单手机号变更记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Service
public class AilikeOrderPhoneChangeRecordDAOImpl extends ServiceImpl<AilikeOrderPhoneChangeRecordMapper, AilikeOrderPhoneChangeRecordDO> implements AilikeOrderPhoneChangeRecordDAO {

    /**
     * 根据外部订单号查询手机号变更记录
     *
     * @param outOrderSn 外部订单号
     * @return 手机号变更记录列表
     */
    @Override
    public List<AilikeOrderPhoneChangeRecordDO> findByOutOrderSn(String outOrderSn) {
        return query()
                .eq(AilikeOrderPhoneChangeRecordDO.OUT_ORDER_SN, outOrderSn)
                .orderByDesc(AilikeOrderPhoneChangeRecordDO.ID)
                .list();
    }
}
