package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 钱包转账记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Data
@TableName("gosh_wallet_transfer")
public class GoshWalletTransferDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 入款集团id
     */
    @TableField("bloc_id")
    private String blocId;

    /**
     * 转账订单号
     */
    @TableField("transfer_no")
    private String transferNo;

    /**
     * 外部转账订单号
     */
    @TableField("platform_transfer_no")
    private String platformTransferNo;

    /**
     * 转账日期(yyyymmdd)
     */
    @TableField("transfer_date")
    private String transferDate;

    /**
     * 转账金额，单位：元
     */
    @TableField("transfer_price")
    private BigDecimal transferPrice;

    /**
     * 转账手续费，单位：元
     */
    @TableField("transfer_fee")
    private BigDecimal transferFee;

    /**
     * 转账状态 TRANSFER_PROCESSING-转账中;TRANSFER_SUCCESS-转账成功;TRANSFER_FAIL-转账失败
     */
    @TableField("transfer_status")
    private String transferStatus;

    /**
     * 实际转账状态 TRANSFER_INIT-初始化;TRANSFER_PROCESSING-转账中;TRANSFER_SUCCESS-转账成功;TRANSFER_FAIL-转账失败
     */
    @TableField("track_transfer_status")
    private String trackTransferStatus;

    /**
     * 出款账户钱包ID
     */
    @TableField("out_wallet_id")
    private String outWalletId;

    /**
     * 入款账户钱包ID
     */
    @TableField("in_wallet_id")
    private String inWalletId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 转账失败原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 转账完成时间
     */
    @TableField("finish_time")
    private Date finishTime;

    /**
     * 是否删除: 0-未删除 1-已删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 结算单号. 关联gosh_settle_form.serial_no
     */
    @TableField("settle_serial_no")
    private String settleSerialNo;

    /**
     * 合并转款标识, 0-普通出款 1-合并转款
     */
    @TableField("merge_transfer_flag")
    private Integer mergeTransferFlag;

    public Date getFinishTime() {
        if (this.finishTime != null) {
          return new Date(this.finishTime.getTime());
        } else {
          return null;
        }
    }

    public void setFinishTime(Date finishTime) {
        if (finishTime != null) {
            this.finishTime = new Date(finishTime.getTime());
        } else {
            this.finishTime = null;
        }
    }
    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String BLOC_ID = "bloc_id";

    public static final String TRANSFER_NO = "transfer_no";

    public static final String PLATFORM_TRANSFER_NO = "platform_transfer_no";

    public static final String TRANSFER_DATE = "transfer_date";

    public static final String TRANSFER_PRICE = "transfer_price";

    public static final String TRANSFER_FEE = "transfer_fee";

    public static final String TRANSFER_STATUS = "transfer_status";

    public static final String TRACK_TRANSFER_STATUS = "track_transfer_status";

    public static final String OUT_WALLET_ID = "out_wallet_id";

    public static final String IN_WALLET_ID = "in_wallet_id";

    public static final String REMARK = "remark";

    public static final String REASON = "reason";

    public static final String FINISH_TIME = "finish_time";

    public static final String IS_DEL = "is_del";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String SETTLE_SERIAL_NO = "settle_serial_no";

    public static final String MERGE_TRANSFER_FLAG = "merge_transfer_flag";

}
