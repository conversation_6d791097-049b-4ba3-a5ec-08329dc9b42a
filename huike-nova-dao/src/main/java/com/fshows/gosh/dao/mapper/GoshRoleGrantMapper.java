package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.domain.result.RoleGrantResultDTO;
import com.fshows.gosh.dao.entity.GoshGrantDO;
import com.fshows.gosh.dao.entity.GoshRoleGrantDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 来逛呗角色权限关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshRoleGrantMapper extends BaseMapper<GoshRoleGrantDO> {

    /**
     * 根据角色id查询权限列表
     *
     * @param roleId
     * @return
     */
    List<GoshGrantDO> queryGrantByRoleId(String roleId);

    /**
     * 根据角色id列表查询权限列表
     *
     * @param roleIds
     * @return
     */
    List<RoleGrantResultDTO> findGrantByRoleIds(@Param("list") List<String> roleIds);
}
