package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.result.RoleAccountCountResultDTO;
import com.fshows.gosh.dao.entity.GoshBlocAccountBindRoleDO;

import java.util.List;

/**
 * <p>
 * 账号和角色关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshBlocAccountBindRoleDAO extends IService<GoshBlocAccountBindRoleDO> {

    /**
     * 根据组织id和账号查询角色列表
     *
     * @param orgId
     * @param accountId
     * @return
     */
    List<String> getRoleListByAccountAndOrgId(String orgId, String accountId);

    /**
     * 根据角色id列表查询角色下账号数量
     *
     * @param roleIdList
     * @return
     */
    List<RoleAccountCountResultDTO> countByRoleIdList(List<String> roleIdList);

    /**
     * 根据角色id查询角色下账号数量
     *
     * @param roleId
     * @return
     */
    Integer countByRoleId(String roleId);

    /**
     * 根据账号id查询角色列表
     *
     * @param accountId
     * @return
     */
    List<GoshBlocAccountBindRoleDO> getByAccountId(String accountId);

    /**
     * 根据角色id查询角色下账号id
     *
     * @param roleId 角色id
     * @return 账号绑定角色列表
     */
    List<String> getByRoleId(String roleId);

    /**
     * 根据账号id删除
     *
     * @param accountId 账号id
     */
    void deleteByAccountId(String accountId);
}
