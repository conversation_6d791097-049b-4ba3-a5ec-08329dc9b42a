/**
 * <AUTHOR>
 * @date 2024/11/12 19:56
 * @version 1.0 GoshOperationRoleInfoDO
 */
package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 运营后台角色信息
 *
 * <AUTHOR>
 * @version GoshOperationRoleInfoDO.java, v 0.1 2024-11-12 19:56 tuyuwei
 */
@Data
@TableName("gosh_operator_role_info")
public class GoshOperatorRoleInfoDO implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色id
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;

    /**
     * 角色备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 角色类型(0超管,1运营)
     */
    @TableField("role_type")
    private Integer roleType;

    /**
     * 状态:1正常,2禁用
     */
    @TableField("role_status")
    private Integer roleStatus;

    /**
     * 0正常 1删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 账号类型:1-全集团账号；2-部分集团权限账号
     */
    @TableField("account_type")
    private Integer accountType;
    public static final String ACCOUNT_TYPE = "account_type";


    public static final String ID = "id";
    public static final String ROLE_ID = "role_id";
    public static final String ROLE_NAME = "role_name";
    public static final String REMARK = "remark";
    public static final String ROLE_STATUS = "role_status";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    private static final long serialVersionUID = 1L;

}