package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshBlocGrantInfoDO;

import java.util.List;

/**
 * <p>
 * 后台权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshBlocGrantInfoDAO extends IService<GoshBlocGrantInfoDO> {

    /**
     * 获取集团所有权限
     *
     * @param blocId
     * @return
     */
    List<GoshBlocGrantInfoDO> findByBlocId(String blocId);

    /**
     * 获取角色所有权限
     *
     * @param roleIds
     * @return
     */
    List<GoshBlocGrantInfoDO> findByRoleIds(List<String> roleIds);

    /**
     * 获取权限列表
     *
     * @param grantIdList
     * @return
     */
    List<GoshBlocGrantInfoDO> findByGrantIdList(List<String> grantIdList);

    /**
     * 获取集团后台所有权限列表
     *
     * @return 集团后台所有权限列表
     */
    List<GoshBlocGrantInfoDO> findAll();
}
