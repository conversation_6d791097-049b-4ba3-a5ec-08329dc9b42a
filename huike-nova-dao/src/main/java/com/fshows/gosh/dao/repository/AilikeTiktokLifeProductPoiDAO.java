package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.param.OrderDataSummaryPoiProductParamDTO;
import com.fshows.gosh.dao.entity.AilikeTiktokLifeProductPoiDO;

import java.util.List;

/**
 * <p>
 * 抖音来客商品适用门店列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
public interface AilikeTiktokLifeProductPoiDAO extends IService<AilikeTiktokLifeProductPoiDO> {

    /**
     * 根据商品Id
     *
     * @param dto 参数
     * @return 符合条件的bizProductId列表
     */
    List<String> getBusinessProductIdByPoiId(OrderDataSummaryPoiProductParamDTO dto);

    /**
     * 根据业务商品Id获得POI信息 （huike copy）
     * 如果是跨广场POI，仅显示第一个POI地址
     *
     * @param businessProductId 业务商品Id
     * @return POI记录
     */
    AilikeTiktokLifeProductPoiDO getOneByBusinessProductId(String businessProductId);

    /**
     * 根据业务商品Id列表查询POI信息
     *
     * @param businessProductIdList 商品id 列表
     * @return 结果
     */
    List<AilikeTiktokLifeProductPoiDO> getByBusinessProductIdList(List<String> businessProductIdList);

}
