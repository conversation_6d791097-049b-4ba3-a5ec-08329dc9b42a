package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.AilikeQykWxUserActivityDO;

/**
 * <p>
 * 微信营销活动-用户活动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
public interface AilikeQykWxUserActivityDAO extends IService<AilikeQykWxUserActivityDO> {

    /**
     * 推广单商品统计
     *
     * @param userActivityRelationId 用户活动关联 id
     * @return 推广单商品统计信息
     */
    AilikeQykWxUserActivityDO queryUserActivityStatistics(String userActivityRelationId);

}
