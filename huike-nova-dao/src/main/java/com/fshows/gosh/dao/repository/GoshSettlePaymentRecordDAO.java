package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.result.SettlePaymentRecordDTO;
import com.fshows.gosh.dao.entity.GoshSettlePaymentRecordDO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 入金记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
public interface GoshSettlePaymentRecordDAO extends IService<GoshSettlePaymentRecordDO> {

    /**
     * 根据外部单号查询是否存在记录
     *
     * @param platformNotifyNo
     * @return
     */
    GoshSettlePaymentRecordDO getByPlatformNotifyNo(String platformNotifyNo);

    /**
     * 根据traceNo查询是否存在记录
     *
     * @param traceNo
     * @return
     */
    GoshSettlePaymentRecordDO getByTraceNo(String traceNo);

    /**
     * 查询入金汇总
     *
     * @param walletId
     * @param tradeDate
     * @return
     */
    BigDecimal sumSettlePaymentRecord(String walletId, String tradeDate, String remark);

    /**
     * 查询集团入金列表
     *
     * @param settleDay    入金日期
     * @param platformType 平台类型
     * @param balanceType  钱包类型
     * @return
     */
    List<SettlePaymentRecordDTO> getSettlePaymentRecord(String settleDay, String platformType, String balanceType);
}
