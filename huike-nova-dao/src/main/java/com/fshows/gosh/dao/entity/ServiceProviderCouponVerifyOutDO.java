package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 外部商品服务商核销卡券记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Data
@TableName("ailike_service_provider_coupon_verify_out")
public class ServiceProviderCouponVerifyOutDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 劵码
     */
    @TableField("coupon_code")
    private String couponCode;

    /**
     * 抖音订单号
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 商家与代理商关联id
     */
    @TableField("merchant_agent_id")
    private String merchantAgentId;

    /**
     * 抖音来客id
     */
    @TableField("tiktok_life_id")
    private String tiktokLifeId;

    /**
     * 服务商appId
     */
    @TableField("app_id")
    private String appId;

    /**
     * 核销门店ID（抖音）
     */
    @TableField("verification_store_id")
    private String verificationStoreId;

    /**
     * 核销门店（抖音）
     */
    @TableField("verification_store_name")
    private String verificationStoreName;

    /**
     * 劵码状态 1-未核销 2-已核销
     */
    @TableField("coupon_status")
    private Integer couponStatus;

    /**
     * 核销时间
     */
    @TableField("verification_time")
    private Date verificationTime;

    /**
     * 抖音来客商品Id
     */
    @TableField("tiktok_product_id")
    private String tiktokProductId;

    /**
     * 抖音来客商品名称
     */
    @TableField("tiktok_product_name")
    private String tiktokProductName;

    /**
     * 商品类型 1团购券  11代金券
     */
    @TableField("product_type")
    private Integer productType;

    /**
     * 券售卖金额
     */
    @TableField("coupon_sale_amount")
    private BigDecimal couponSaleAmount;

    /**
     * 商家补贴
     */
    @TableField("merchant_subsidy")
    private BigDecimal merchantSubsidy;

    /**
     * 抖音支付优惠金额
     */
    @TableField("tiktok_discount")
    private BigDecimal tiktokDiscount;

    /**
     * 平台补贴
     */
    @TableField("platform_subsidy")
    private BigDecimal platformSubsidy;

    /**
     * 用户实付
     */
    @TableField("cash_fee")
    private BigDecimal cashFee;

    /**
     * 订单实收（各类服务费率基数）
     */
    @TableField("order_sumprice")
    private BigDecimal orderSumprice;

    /**
     * 软件服务费费率
     */
    @TableField("service_rate")
    private BigDecimal serviceRate;

    /**
     * 软件服务费
     */
    @TableField("service_fee")
    private BigDecimal serviceFee;

    /**
     * 支付服务费费率
     */
    @TableField("trade_rate")
    private BigDecimal tradeRate;

    /**
     * 支付服务费
     */
    @TableField("trade_fee")
    private BigDecimal tradeFee;

    /**
     * 达人佣金比例
     */
    @TableField("star_rate")
    private BigDecimal starRate;

    /**
     * 达人佣金
     */
    @TableField("star_fee")
    private BigDecimal starFee;

    /**
     * 达人昵称
     */
    @TableField("star_name")
    private String starName;

    /**
     * 达人uid
     */
    @TableField("star_tiktok_code")
    private String starTiktokCode;

    /**
     * 服务商佣金比例
     */
    @TableField("service_provider_rate")
    private BigDecimal serviceProviderRate;

    /**
     * 服务商佣金
     */
    @TableField("service_provider_fee")
    private BigDecimal serviceProviderFee;

    /**
     * 撮合经纪服务费
     */
    @TableField("brokerage_fee")
    private BigDecimal brokerageFee;

    /**
     * 保险费用
     */
    @TableField("insurance_fee")
    private BigDecimal insuranceFee;

    /**
     * 商家应得金额
     */
    @TableField("merchant_net_amount")
    private BigDecimal merchantNetAmount;

    /**
     * 结算状态 1待提现 2已结算
     */
    @TableField("settled_status")
    private Integer settledStatus;

    /**
     * 结算日期 例如：20220830
     */
    @TableField("settled_day")
    private Integer settledDay;

    /**
     * 结算金额
     */
    @TableField("settled_amount")
    private BigDecimal settledAmount;

    /**
     * 用户支付时间
     */
    @TableField("pay_time")
    private Date payTime;

    /**
     * 渠道类型 TIKTOK：抖音 ALIPAY：支付宝
     */
    @TableField("channel_type")
    private String channelType;

    /**
     * 原价
     */
    @TableField("coupon_origin_price")
    private BigDecimal couponOriginPrice;

    /**
     * 抖音来客消费者uid
     */
    @TableField("tiktok_consumer_uid")
    private String tiktokConsumerUid;

    /**
     * 删除标记:1-未删除  2-已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 数据类型:OUT-外部商品核销数据 DAY_REFUND-跨日退款数据
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 结算单号
     */
    @TableField("serial_no")
    private String serialNo;

    public Date getVerificationTime() {
        if (this.verificationTime != null) {
          return new Date(this.verificationTime.getTime());
        } else {
          return null;
        }
    }

    public void setVerificationTime(Date verificationTime) {
        if (verificationTime != null) {
            this.verificationTime = new Date(verificationTime.getTime());
        } else {
            this.verificationTime = null;
        }
    }
    public Date getPayTime() {
        if (this.payTime != null) {
          return new Date(this.payTime.getTime());
        } else {
          return null;
        }
    }

    public void setPayTime(Date payTime) {
        if (payTime != null) {
            this.payTime = new Date(payTime.getTime());
        } else {
            this.payTime = null;
        }
    }
    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String COUPON_CODE = "coupon_code";

    public static final String ORDER_ID = "order_id";

    public static final String MERCHANT_AGENT_ID = "merchant_agent_id";

    public static final String TIKTOK_LIFE_ID = "tiktok_life_id";

    public static final String APP_ID = "app_id";

    public static final String VERIFICATION_STORE_ID = "verification_store_id";

    public static final String VERIFICATION_STORE_NAME = "verification_store_name";

    public static final String COUPON_STATUS = "coupon_status";

    public static final String VERIFICATION_TIME = "verification_time";

    public static final String TIKTOK_PRODUCT_ID = "tiktok_product_id";

    public static final String TIKTOK_PRODUCT_NAME = "tiktok_product_name";

    public static final String PRODUCT_TYPE = "product_type";

    public static final String COUPON_SALE_AMOUNT = "coupon_sale_amount";

    public static final String MERCHANT_SUBSIDY = "merchant_subsidy";

    public static final String TIKTOK_DISCOUNT = "tiktok_discount";

    public static final String PLATFORM_SUBSIDY = "platform_subsidy";

    public static final String CASH_FEE = "cash_fee";

    public static final String ORDER_SUMPRICE = "order_sumprice";

    public static final String SERVICE_RATE = "service_rate";

    public static final String SERVICE_FEE = "service_fee";

    public static final String TRADE_RATE = "trade_rate";

    public static final String TRADE_FEE = "trade_fee";

    public static final String STAR_RATE = "star_rate";

    public static final String STAR_FEE = "star_fee";

    public static final String STAR_NAME = "star_name";

    public static final String STAR_TIKTOK_CODE = "star_tiktok_code";

    public static final String SERVICE_PROVIDER_RATE = "service_provider_rate";

    public static final String SERVICE_PROVIDER_FEE = "service_provider_fee";

    public static final String BROKERAGE_FEE = "brokerage_fee";

    public static final String INSURANCE_FEE = "insurance_fee";

    public static final String MERCHANT_NET_AMOUNT = "merchant_net_amount";

    public static final String SETTLED_STATUS = "settled_status";

    public static final String SETTLED_DAY = "settled_day";

    public static final String SETTLED_AMOUNT = "settled_amount";

    public static final String PAY_TIME = "pay_time";

    public static final String CHANNEL_TYPE = "channel_type";

    public static final String COUPON_ORIGIN_PRICE = "coupon_origin_price";

    public static final String TIKTOK_CONSUMER_UID = "tiktok_consumer_uid";

    public static final String DEL_FLAG = "del_flag";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String DATA_TYPE = "data_type";

}
