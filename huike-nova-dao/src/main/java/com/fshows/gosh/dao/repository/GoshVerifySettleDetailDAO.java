package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.GoshVerifySettleDetailDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 核销结算明细数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface GoshVerifySettleDetailDAO extends IService<GoshVerifySettleDetailDO> {

    /**
     * 根据结算单号查询补单明细
     *
     * @param serialNo 结算单号
     * @param dataId   数据Id
     * @return
     */
    List<GoshVerifySettleDetailDO> getListBySerialNo(String serialNo, long dataId);

    /**
     * 根据门店Id查询补单明细
     *
     * @param shopId 门店Id
     * @return
     */
    List<GoshVerifySettleDetailDO> getListByShopId(String shopId);

    /**
     * 根据门店结算单号查询补单明细
     *
     * @param shopSerialNo 门店结算单号
     * @return
     */
    List<GoshVerifySettleDetailDO> getListByShopSerialNo(String shopSerialNo);

    /**
     * 结算单号
     *
     * @param serialNo 结算单号
     */
    boolean removeBySerialNo(String serialNo);

}
