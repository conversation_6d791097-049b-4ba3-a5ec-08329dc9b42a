package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 来逛呗-商家小程序用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@Data
@TableName("gosh_user")
public class GoshUserDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 用户id（业务主键）
     */
    @TableField("user_id")
    private String userId;

    /**
     * 手机号（密文）
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 手机号码加密扩展字段用于加密后的模糊查询
     */
    @TableField("phone_number_encrypt_ext")
    private String phoneNumberEncryptExt;

    /**
     * 用户昵称（冗余字段）
     */
    @TableField("user_name")
    private String userName;

    /**
     * 用户密码（冗余字段）
     */
    @TableField("password")
    private String password;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String USER_ID = "user_id";

    public static final String PHONE_NUMBER = "phone_number";

    public static final String PHONE_NUMBER_ENCRYPT_EXT = "phone_number_encrypt_ext";

    public static final String USER_NAME = "user_name";

    public static final String PASSWORD = "password";

    public static final String IS_DEL = "is_del";

}
