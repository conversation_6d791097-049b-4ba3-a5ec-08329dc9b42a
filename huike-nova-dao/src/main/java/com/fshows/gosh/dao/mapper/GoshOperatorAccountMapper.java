package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.BlocQueryParamDTO;
import com.fshows.gosh.dao.domain.param.SearchOperationAccountParamDTO;
import com.fshows.gosh.dao.domain.result.SearchOperationAccountResultDTO;
import com.fshows.gosh.dao.entity.GoshOperatorAccountDO;
import com.huike.nova.common.metadata.PageResult;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 来逛呗运营后台账号 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
public interface GoshOperatorAccountMapper extends BaseMapper<GoshOperatorAccountDO> {

    /**
     * 查询运营账号列表
     * @param page
     * @param query
     * @return
     */
    Page<SearchOperationAccountResultDTO> queryAccountList(Page<SearchOperationAccountResultDTO> page, @Param("query") SearchOperationAccountParamDTO query);

}
