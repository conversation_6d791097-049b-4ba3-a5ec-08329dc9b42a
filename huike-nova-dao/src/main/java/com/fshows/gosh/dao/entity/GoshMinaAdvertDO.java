package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 来逛呗商家版小程序广告
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Data
@TableName("gosh_mina_advert")
public class GoshMinaAdvertDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 广告id
     */
    @TableField("adv_id")
    private String advId;

    /**
     * 广告名称
     */
    @TableField("adv_name")
    private String advName;

    /**
     * 广告位置:HOME
     */
    @TableField("adv_location")
    private String advLocation;

    /**
     * 广告图片地址
     */
    @TableField("img_url")
    private String imgUrl;

    /**
     * 跳转小程序appId
     */
    @TableField("app_id")
    private String appId;

    /**
     * 广告跳转地址
     */
    @TableField("nav_url")
    private String navUrl;

    /**
     * 投放开始时间
     */
    @TableField("begin_time")
    private Integer beginTime;

    /**
     * 投放结束时间
     */
    @TableField("end_time")
    private Integer endTime;

    /**
     * 0正常 1删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String ADV_ID = "adv_id";

    public static final String ADV_NAME = "adv_name";

    public static final String ADV_LOCATION = "adv_location";

    public static final String IMG_URL = "img_url";

    public static final String NAV_URL = "nav_url";

    public static final String BEGIN_TIME = "begin_time";

    public static final String END_TIME = "end_time";

    public static final String IS_DEL = "is_del";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
