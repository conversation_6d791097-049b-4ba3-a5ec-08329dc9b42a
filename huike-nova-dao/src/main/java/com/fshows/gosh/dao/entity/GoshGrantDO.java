package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 来逛呗权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-11
 */
@Data
@TableName("gosh_grant")
public class GoshGrantDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 权限 id
     */
    @TableField("grant_id")
    private String grantId;

    /**
     * 权限名称
     */
    @TableField("grant_name")
    private String grantName;

    /**
     * 排序值 默认 999
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 0正常 1删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    public static final String GRANT_CODE = "grant_code";
    public static final String BELONG_TYPE = "belong_type";

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String GRANT_ID = "grant_id";

    public static final String GRANT_NAME = "grant_name";

    public static final String SORT = "sort";

    public static final String IS_DEL = "is_del";
    /**
     * 权限code
     */
    @TableField("grant_code")
    private String grantCode;
    /**
     * 归属类型 1-商家版小程序
     */
    @TableField("belong_type")
    private Integer belongType;

}
