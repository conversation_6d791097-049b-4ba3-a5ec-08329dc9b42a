package com.fshows.gosh.dao.repository.impl;

import com.fshows.gosh.dao.entity.GoshProductTalentUidVerifyBlackListDO;
import com.fshows.gosh.dao.mapper.GoshProductTalentUidVerifyBlackListMapper;
import com.fshows.gosh.dao.repository.GoshProductTalentUidVerifyBlackListDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 来逛呗商品-达人uid核销黑名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Service
public class GoshProductTalentUidVerifyBlackListDAOImpl extends ServiceImpl<GoshProductTalentUidVerifyBlackListMapper, GoshProductTalentUidVerifyBlackListDO> implements GoshProductTalentUidVerifyBlackListDAO {
    /**
     * 根据商品和达人查询查询黑名单记录
     *
     * @param productId 商品Id
     * @param uid 达人Uid
     * @return 黑名单记录
     */
    @Override
    public GoshProductTalentUidVerifyBlackListDO getRecord(String productId, String uid) {
        return query()
                .eq(GoshProductTalentUidVerifyBlackListDO.PRODUCT_ID, productId)
                .eq(GoshProductTalentUidVerifyBlackListDO.TALENT_UID, uid)
                .eq(GoshProductTalentUidVerifyBlackListDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }
}
