package com.fshows.gosh.dao.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.result.RoleGrantResultDTO;
import com.fshows.gosh.dao.entity.GoshGrantDO;
import com.fshows.gosh.dao.entity.GoshRoleGrantDO;
import com.fshows.gosh.dao.mapper.GoshRoleGrantMapper;
import com.fshows.gosh.dao.repository.GoshRoleGrantDAO;
import com.google.common.collect.Lists;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 来逛呗角色权限关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Service
public class GoshRoleGrantDAOImpl extends ServiceImpl<GoshRoleGrantMapper, GoshRoleGrantDO> implements GoshRoleGrantDAO {

    /**
     * 根据角色id查询权限列表
     *
     * @param roleId
     * @return
     */
    @Override
    public List<GoshGrantDO> queryGrantByRoleId(String roleId) {
        return getBaseMapper().queryGrantByRoleId(roleId);
    }

    /**
     * 根据角色id列表查询权限列表
     *
     * @param roleIds
     * @return
     */
    @Override
    public List<RoleGrantResultDTO> findGrantByRoleIds(List<String> roleIds) {
        if (CollectionUtil.isEmpty(roleIds)) {
            return Lists.newArrayList();
        }
        return getBaseMapper().findGrantByRoleIds(roleIds);
    }

    /**
     * 批量添加角色权限信息
     *
     * @param list
     * @return
     */
    @Override
    public boolean batchRoleGrant(List<GoshRoleGrantDO> list) {
        return saveBatch(list);
    }

    /**
     * 删除角色权限
     *
     * @param roleId
     * @return
     */
    @Override
    public boolean deleteRoleGrant(String roleId) {
        return update()
                .set(GoshRoleGrantDO.IS_DEL, DelFlagEnum.DEL.getValue())
                .eq(GoshRoleGrantDO.ROLE_ID, roleId)
                .update();
    }
}
