package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.domain.param.VerifyDataSummaryParamDTO;
import com.fshows.gosh.dao.domain.result.ChannelTypeVerifySummaryResultDTO;
import com.fshows.gosh.dao.domain.result.ProductTypeVerifySummaryResultDTO;
import com.fshows.gosh.dao.domain.result.VerifyDataSummaryResultDTO;
import com.fshows.gosh.dao.domain.result.VerifyDaySummaryResultDTO;
import com.fshows.gosh.dao.entity.GoshMerchantVerifyDayDataDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 来逛呗-商家核销数据汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshMerchantVerifyDayDataDAO extends IService<GoshMerchantVerifyDayDataDO> {

    /**
     * 统计汇总记录数
     *
     * @param appId
     * @param verifyDay
     * @return
     */
    int getCountByAppIdAndDay(String appId, Integer verifyDay);


    /**
     * 删除数据
     *
     * @param appId
     * @param verifyDay
     * @return
     */
    boolean deleteByAppIdAndDay(String appId, Integer verifyDay);

    /**
     * 核销总汇总数据
     *
     * @param dto
     */
    VerifyDataSummaryResultDTO verifyTotalSummary(VerifyDataSummaryParamDTO dto);

    /**
     * 核销日汇总数据
     *
     * @param dto
     */
    List<VerifyDaySummaryResultDTO> verifyDaySummary(VerifyDataSummaryParamDTO dto);

    /**
     * 按通道统计核销汇总数据
     *
     * @param dto
     * @return
     */
    List<ChannelTypeVerifySummaryResultDTO> channelTypeVerifySummary(VerifyDataSummaryParamDTO dto);

    /**
     * 按商品类型统计核销汇总数据
     *
     * @param dto
     * @return
     */
    List<ProductTypeVerifySummaryResultDTO> productTypeVerifySummary(VerifyDataSummaryParamDTO dto);
}
