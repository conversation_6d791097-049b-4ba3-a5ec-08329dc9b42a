package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.entity.GoshBlocGrantInfoDO;

import java.util.List;

/**
 * <p>
 * 后台权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshBlocGrantInfoMapper extends BaseMapper<GoshBlocGrantInfoDO> {

    /**
     * 获取集团所有权限
     *
     * @param blocId
     * @return
     */
    List<GoshBlocGrantInfoDO> findByBlocId(String blocId);

    /**
     * 获取角色所有权限
     *
     * @param list
     * @return
     */
    List<GoshBlocGrantInfoDO> findByRoleIds(List<String> list);
}
