package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshGrantDO;

import java.util.List;

/**
 * <p>
 * 来逛呗权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshGrantDAO extends IService<GoshGrantDO> {

    /**
     * 查询所有的权限
     *
     * @param belongType 归属类型 1-商家版小程序
     * @param blocId     集团id
     * @return 权限列表
     */
    List<GoshGrantDO> queryAll(Integer belongType, String blocId);

    /**
     * 查询所有的权限
     *
     * @param belongType 归属类型 1-商家版小程序
     * @return 权限列表
     */
    List<GoshGrantDO> findAll(Integer belongType);
}
