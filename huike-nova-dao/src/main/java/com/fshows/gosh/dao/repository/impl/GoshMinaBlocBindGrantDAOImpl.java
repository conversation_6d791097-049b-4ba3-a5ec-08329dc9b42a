package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshMinaBlocBindGrantDO;
import com.fshows.gosh.dao.mapper.GoshMinaBlocBindGrantMapper;
import com.fshows.gosh.dao.repository.GoshMinaBlocBindGrantDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 商家版小程序-集团绑定权限 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Service
public class GoshMinaBlocBindGrantDAOImpl extends ServiceImpl<GoshMinaBlocBindGrantMapper, GoshMinaBlocBindGrantDO> implements GoshMinaBlocBindGrantDAO {

    /**
     * 根据集团id查询集团小程序绑定权限
     *
     * @param blocId 集团id
     * @return 集团绑定权限
     */
    @Override
    public List<String> findBlocMinaGrantIdListByBlocId(String blocId) {
        return getBaseMapper().findBlocMinaGrantIdListByBlocId(blocId);
    }

    /**
     * 根据集团 id 删除集团权限
     *
     * @param blocId 集团 id
     */
    @Override
    public void deleteByBlocId(String blocId) {
        update().set(GoshMinaBlocBindGrantDO.IS_DEL, DelFlagEnum.DEL.getValue()).eq(GoshMinaBlocBindGrantDO.BLOC_ID, blocId).update();
    }
}
