package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 来逛呗订单差异表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Data
@TableName("gosh_diff_order_detail")
public class GoshDiffOrderDetailDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增 ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 对账标识，来逛呗单号
     */
    @TableField("row_key")
    private String rowKey;

    /**
     * 差异日期 eg.********
     */
    @TableField("diff_date")
    private Integer diffDate;

    /**
     * 订单号
     */
    @TableField("order_sn")
    private String orderSn;

    /**
     * 外部订单号
     */
    @TableField("out_order_sn")
    private String outOrderSn;

    /**
     * 订单金额
     */
    @TableField("order_money")
    private BigDecimal orderMoney;

    /**
     * 账户id
     */
    @TableField("account_id")
    private String accountId;

    /**
     * 订单状态
     */
    @TableField("order_status")
    private String orderStatus;

    /**
     * 异常数据来源：LAIGUANBEI:来逛呗, FUBEI:付呗
     */
    @TableField("diff_source")
    private String diffSource;

    /**
     * 0 商户提现单
     */
    @TableField("bill_type")
    private Integer billType;

    /**
     * 0 未知 1 掉单 2 订单状态不一致 3 订单金额不一致 4 日切
     */
    @TableField("diff_type")
    private Integer diffType;

    /**
     * 平台方原始账单数据行
     */
    @TableField("bill_data")
    private String billData;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String ROW_KEY = "row_key";

    public static final String DIFF_DATE = "diff_date";

    public static final String ORDER_SN = "order_sn";

    public static final String OUT_ORDER_SN = "out_order_sn";

    public static final String ORDER_MONEY = "order_money";

    public static final String ACCOUNT_ID = "account_id";

    public static final String ORDER_STATUS = "order_status";

    public static final String DIFF_SOURCE = "diff_source";

    public static final String BILL_TYPE = "bill_type";

    public static final String DIFF_TYPE = "diff_type";

    public static final String BILL_DATA = "bill_data";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
