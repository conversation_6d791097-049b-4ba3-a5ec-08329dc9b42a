package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.domain.result.SettlePaymentRecordDTO;
import com.fshows.gosh.dao.entity.GoshSettlePaymentRecordDO;
import com.fshows.gosh.dao.mapper.GoshSettlePaymentRecordMapper;
import com.fshows.gosh.dao.repository.GoshSettlePaymentRecordDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 入金记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Service
public class GoshSettlePaymentRecordDAOImpl extends ServiceImpl<GoshSettlePaymentRecordMapper, GoshSettlePaymentRecordDO> implements GoshSettlePaymentRecordDAO {

    /**
     * 根据外部单号查询是否存在记录
     *
     * @param platformNotifyNo
     * @return
     */
    @Override
    public GoshSettlePaymentRecordDO getByPlatformNotifyNo(String platformNotifyNo) {
        return query()
                .eq(GoshSettlePaymentRecordDO.PLATFORM_NOTIFY_NO, platformNotifyNo)
                .eq(GoshSettlePaymentRecordDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 根据traceNo查询是否存在记录
     *
     * @param traceNo
     * @return
     */
    @Override
    public GoshSettlePaymentRecordDO getByTraceNo(String traceNo) {
        return query()
                .eq(GoshSettlePaymentRecordDO.TRACE_NO, traceNo)
                .eq(GoshSettlePaymentRecordDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 查询入金汇总
     *
     * @param walletId
     * @param tradeDate
     * @return
     */
    @Override
    public BigDecimal sumSettlePaymentRecord(String walletId, String tradeDate, String remark) {
        return baseMapper.sumSettlePaymentRecord(walletId, tradeDate, remark);
    }

    /**
     * 查询集团入金列表
     *
     * @param settleDay    入金日期
     * @param platformType 平台类型
     * @param balanceType  钱包类型
     * @return
     */
    @Override
    public List<SettlePaymentRecordDTO> getSettlePaymentRecord(String settleDay, String platformType, String balanceType) {
        return baseMapper.getSettlePaymentRecord(settleDay, platformType, balanceType);
    }
}
