package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.AilikeQykWxUserStatisticsDO;

/**
 * <p>
 * 微信营销活动-用户数据汇总 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
public interface AilikeQykWxUserStatisticsDAO extends IService<AilikeQykWxUserStatisticsDO> {

    /**
     * 根据用户id查询
     *
     * @param userId
     * @return
     */
    AilikeQykWxUserStatisticsDO getByUserId(String userId);

}
