package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 微信营销活动-用户数据汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Data
@TableName("ailike_qyk_wx_user_statistics")
public class AilikeQykWxUserStatisticsDO implements Serializable {

    public static final String ID = "id";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    public static final String USER_ID = "user_id";
    public static final String TOTAL_INCOME = "total_income";
    public static final String AVAILABLE_BALANCE = "available_balance";
    public static final String WITHDRAWAL_FROZEN_BALANCE = "withdrawal_frozen_balance";
    public static final String PARTICIPATION_USER_COUNT = "participation_user_count";
    public static final String ORDER_PAY_USER_COUNT = "order_pay_user_count";
    public static final String COUPON_PAY_COUNT = "coupon_pay_count";
    public static final String COUPON_VERIFY_COUNT = "coupon_verify_count";
    public static final String VERIFY_TOTAL_AMOUNT = "verify_total_amount";
    public static final String IS_DEL = "is_del";
    public static final String VISITS_COUNT = "visits_count";
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 用户 id
     */
    @TableField("user_id")
    private String userId;
    /**
     * 总收益
     */
    @TableField("total_income")
    private BigDecimal totalIncome;
    /**
     * 可用余额
     */
    @TableField("available_balance")
    private BigDecimal availableBalance;
    /**
     * 提现冻结余额
     */
    @TableField("withdrawal_frozen_balance")
    private BigDecimal withdrawalFrozenBalance;
    /**
     * 参与用户数（推广人数）
     */
    @TableField("participation_user_count")
    private Integer participationUserCount;
    /**
     * 订单支付用户数量（下单人数）
     */
    @TableField("order_pay_user_count")
    private Integer orderPayUserCount;
    /**
     * 券支付数量（下单券数）
     */
    @TableField("coupon_pay_count")
    private Integer couponPayCount;
    /**
     * 券核销数量（核销券数）
     */
    @TableField("coupon_verify_count")
    private Integer couponVerifyCount;
    /**
     * 核销总金额(核销金额（元）)
     */
    @TableField("verify_total_amount")
    private BigDecimal verifyTotalAmount;
    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 访问数量
     */
    @TableField("visits_count")
    private Integer visitsCount;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

}
