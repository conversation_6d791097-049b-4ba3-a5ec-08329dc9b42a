package com.fshows.gosh.dao.repository.impl;

import com.fshows.gosh.dao.domain.param.ServicePriceConfigParamDTO;
import com.fshows.gosh.dao.entity.AilikeTiktokServicePriceConfigDO;
import com.fshows.gosh.dao.mapper.AilikeTiktokServicePriceConfigMapper;
import com.fshows.gosh.dao.repository.AilikeTiktokServicePriceConfigDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.enums.DelStatusEnum;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 集团服务商金额配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-03
 */
@Service
public class AilikeTiktokServicePriceConfigDAOImpl extends ServiceImpl<AilikeTiktokServicePriceConfigMapper, AilikeTiktokServicePriceConfigDO> implements AilikeTiktokServicePriceConfigDAO {

    /**
     * 查询配置
     *
     * @param dto
     * @return
     */
    @Override
    public AilikeTiktokServicePriceConfigDO findConfig(ServicePriceConfigParamDTO dto) {
        return query().eq(AilikeTiktokServicePriceConfigDO.APP_ID, dto.getAppId())
                .eq(AilikeTiktokServicePriceConfigDO.CHANNEL_TYPE, dto.getChannelType())
                .eq(AilikeTiktokServicePriceConfigDO.PRODUCT_TYPE, dto.getProductType())
                .eq(AilikeTiktokServicePriceConfigDO.CATEGORY_CODE, dto.getCategoryCode())
                .eq(AilikeTiktokServicePriceConfigDO.DEL_FLAG, DelStatusEnum.NOT_DEL.getValue())
                .last(" limit 1").one();
    }
}
