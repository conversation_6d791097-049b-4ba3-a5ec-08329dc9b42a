package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.LiveDataParamDTO;
import com.fshows.gosh.dao.domain.result.LiveDataInfoResultDTO;
import com.fshows.gosh.dao.domain.result.LiveDataSummaryResultDTO;
import com.fshows.gosh.dao.entity.AilikeTiktokLiveDataDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huike.nova.common.metadata.PageParam;

/**
 * <p>
 * 抖音来客直播数据记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface AilikeTiktokLiveDataDAO extends IService<AilikeTiktokLiveDataDO> {

    /**
     * 直播数据汇总
     *
     * @param dto
     * @return
     */
    LiveDataSummaryResultDTO liveDataSummary(LiveDataParamDTO dto);

    /**
     * 分页查询直播列表数据
     *
     * @param pageParam
     * @return
     */
    Page<LiveDataInfoResultDTO> pageLiveData(PageParam<LiveDataParamDTO> pageParam);

}
