package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.param.PagePaymentListParamDTO;
import com.fshows.gosh.dao.domain.result.PagePaymentListResultDTO;
import com.fshows.gosh.dao.domain.result.SettleFormResultDTO;
import com.fshows.gosh.dao.entity.GoshSettleFormDO;
import com.huike.nova.common.metadata.PageParam;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/**
 * <p>
 * 结算单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface GoshSettleFormDAO extends IService<GoshSettleFormDO> {
    /**
     * 查询出款单列表
     *
     * @param pageDTO
     * @return
     */
    Page<PagePaymentListResultDTO> pagePaymentList(PageParam<PagePaymentListParamDTO> pageDTO);

    /**
     * 导出出款单列表
     *
     * @return
     */
    void paymentListExport(PagePaymentListParamDTO dto, ResultHandler<PagePaymentListResultDTO> resultHandler);


    /**
     * 根据集团id和时间获取结算单
     *
     * @param settlementDay
     * @param blocId
     * @param channelType
     * @return
     */
    GoshSettleFormDO getSettleForm(int settlementDay, String blocId, String channelType, Integer meiTuanType);

    /**
     * 保存结算单
     *
     * @param settleFormDO
     * @return
     */
    boolean saveSettleForm(GoshSettleFormDO settleFormDO);

    /**
     * 根据serialNo删除结算单
     *
     * @param serialNo
     * @return
     */
    boolean removeBySerialNo(String serialNo);


    /**
     * 根据集团id、平台类型、平台日期获取结算单
     *
     * @param blocId
     * @param platformType
     * @param settleDate
     * @return {@link List}<{@link GoshSettleFormDO}>
     */
    List<GoshSettleFormDO> getSettleFormList(String blocId, String platformType, String settleDate);

    /**
     * 更新结算单状态
     *
     * @param serialNo
     * @param settleFormStatus
     * @return boolean
     */
    boolean updateSettleFormStatus(String serialNo, Integer settleFormStatus);

    /**
     * 更新转账状态
     *
     * @param serialNo
     * @param transferStatus
     * @param settleFormStatus
     * @return boolean
     */
    boolean updateTransferStatus(String serialNo, Integer transferStatus, Integer settleFormStatus);

    /**
     * 更新转账状态
     *
     * @param serialNo   结算单
     * @param billStatus 对账状态
     * @param billSource 对账来源
     * @param operator   操作人
     * @return boolean
     */
    boolean updateBillStatus(String serialNo, Integer billStatus, String billSource, String operator);

    /**
     * 根据结算号查询结算单
     *
     * @param serialNo
     * @return {@link GoshSettleFormDO}
     */
    GoshSettleFormDO getSettleFormBySerialNo(String serialNo);

    /**
     * 根据结算关系获取结算单
     *
     * @param settleDate
     * @param relationBalanceId
     * @return
     */
    List<SettleFormResultDTO> getSettleFormResultList(String settleDate, String relationBalanceId);

    /**
     * 根据结算关系获取结算单
     *
     * @param settleDate
     * @param relationBalanceId
     * @return
     */
    List<SettleFormResultDTO> getMergeTransferSuccessSettleFormResultList(String settleDate, String relationBalanceId);

    /**
     * 更新合并转账单号
     *
     * @param mergeTransferNo
     * @param serialNoList
     * @return boolean
     */
    boolean updateMergeTransferNo(String mergeTransferNo, Integer mergeTransferStatus, List<String> serialNoList);

    /**
     * 更新合并转账状态
     *
     * @param mergeTransferNo
     * @param mergeTransferStatus
     * @return boolean
     */
    boolean updateMergeTransferStatus(String mergeTransferNo, Integer mergeTransferStatus);

    boolean updateTransferStatusAndMergeTransferStatus(String serialNo, Integer transferStatus, Integer mergeTransferStatus);

    /**
     * 修改结算数据
     *
     * @param formDO 参数
     */
    void modifyPaymentData(GoshSettleFormDO formDO);
}
