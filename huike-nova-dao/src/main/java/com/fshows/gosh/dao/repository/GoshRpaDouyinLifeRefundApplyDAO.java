package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.refund.PageRefundParamDTO;
import com.fshows.gosh.dao.domain.param.refund.PageSubVoucherParamDTO;
import com.fshows.gosh.dao.domain.result.refund.PageRefundResultDTO;
import com.fshows.gosh.dao.entity.GoshRpaDouyinLifeRefundApplyDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.dao.domain.param.OrderDataSummaryParamDTO;
import com.huike.nova.dao.domain.result.ProductOrderDataResultDTO;
import com.huike.nova.dao.entity.AilikeServiceProviderCouponVerifyDO;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/**
 * <p>
 * 抖音RPA退款订单申请 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
public interface GoshRpaDouyinLifeRefundApplyDAO extends IService<GoshRpaDouyinLifeRefundApplyDO> {
    /**
     * 分页查询线上退款列表
     *
     * @param paramDTO 参数
     * @return 结果
     */
    Page<PageRefundResultDTO> pageRefundList(PageParam<PageRefundParamDTO> paramDTO);

    /**
     * 导出线上退款申请数据
     *
     * @param dto           参数
     * @param resultHandler 数据
     */
    void exportRefundData(PageRefundParamDTO dto, ResultHandler<PageRefundResultDTO> resultHandler);


    /**
     * 根据售后id查询数据
     *
     * @param afterSaleId 售后id
     * @return 数据
     */
    GoshRpaDouyinLifeRefundApplyDO findByAfterSaleId(String afterSaleId);

    /**
     * 根据订单号查询线上售后订单
     *
     * @param outOrderSn 平台订单号
     * @return 数据
     */
    List<GoshRpaDouyinLifeRefundApplyDO> findListByOutOrderSn(String outOrderSn);
}
