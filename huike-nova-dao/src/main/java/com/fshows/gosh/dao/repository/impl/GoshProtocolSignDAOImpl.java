package com.fshows.gosh.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshProtocolSignDO;
import com.fshows.gosh.dao.mapper.GoshProtocolSignMapper;
import com.fshows.gosh.dao.repository.GoshProtocolSignDAO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 来逛呗协议签署表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Service
public class GoshProtocolSignDAOImpl extends ServiceImpl<GoshProtocolSignMapper, GoshProtocolSignDO> implements GoshProtocolSignDAO {

    /**
     * 批量保存商户协议
     *
     * @param list
     * @return
     */
    @Override
    public boolean batchProtocolSign(List<GoshProtocolSignDO> list) {
        return saveBatch(list);
    }
}
