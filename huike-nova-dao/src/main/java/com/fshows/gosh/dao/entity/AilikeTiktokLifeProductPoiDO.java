package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 抖音来客商品适用门店列表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
@TableName("ailike_tiktok_life_product_poi")
public class AilikeTiktokLifeProductPoiDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务商品Id
     */
    @TableField("business_product_id")
    private String businessProductId;

    /**
     * 地址Id
     */
    @TableField("poi_id")
    private String poiId;

    /**
     * 名称
     */
    @TableField("poi_name")
    private String poiName;

    /**
     * 省份名称
     */
    @TableField("province")
    private String province;

    /**
     * 市名称
     */
    @TableField("city")
    private String city;

    /**
     * 区名称
     */
    @TableField("district")
    private String district;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除标记  1未删除   2已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * POI对应的总库存（宝龙场景）
     */
    @TableField("stock_num")
    private Integer stockNum;

    /**
     * POI对应的剩余库存
     */
    @TableField("remain_stock_num")
    private Integer remainStockNum;

    /**
     * 支付宝的shopId
     */
    @TableField("alipay_poi")
    private String alipayPoi;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String BUSINESS_PRODUCT_ID = "business_product_id";

    public static final String POI_ID = "poi_id";

    public static final String POI_NAME = "poi_name";

    public static final String PROVINCE = "province";

    public static final String CITY = "city";

    public static final String DISTRICT = "district";

    public static final String ADDRESS = "address";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String DEL_FLAG = "del_flag";

    public static final String STOCK_NUM = "stock_num";

    public static final String REMAIN_STOCK_NUM = "remain_stock_num";

    public static final String ALIPAY_POI = "alipay_poi";

}
