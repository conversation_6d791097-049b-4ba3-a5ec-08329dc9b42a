package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 商铺结算单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Data
@TableName("gosh_shop_settle_form")
public class GoshShopSettleFormDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商铺id
     */
    @TableField("shop_id")
    private String shopId;

    /**
     * 付呗侧account_id
     */
    @TableField("account_id")
    private String accountId;

    /**
     * 结算批次号（对应gosh_settle_form表同字段）
     */
    @TableField("serial_no")
    private String serialNo;

    /**
     * 商铺结算单单号
     */
    @TableField("shop_serial_no")
    private String shopSerialNo;

    /**
     * 结算日期（yyyyMMdd）
     */
    @TableField("settle_date")
    private String settleDate;

    public static final String ID = "id";

    /**
     * 结算单状态 0=未打款 1=打款中 2=打款成功 3=打款失败 4=未开户待结算
     */
    @TableField("settle_form_status")
    private Integer settleFormStatus;

    /**
     * 异常原因
     */
    @TableField("rejected_reason")
    private String rejectedReason;

    /**
     * 平台类型 TIKTOK-抖音 ALIPAY-支付宝 MEITUAN-美团
     */
    @TableField("platform_type")
    private String platformType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 普通券（非商圈卡）金额，单位：元
     */
    @TableField("non_business_amount")
    private BigDecimal nonBusinessAmount;

    /**
     * 商家券（子券）金额，单位：元
     */
    @TableField("business_child_card_amount")
    private BigDecimal businessChildCardAmount;

    /**
     * 外部商品的结算金额，单位：元
     */
    @TableField("out_product_settle_amount")
    private BigDecimal outProductSettleAmount;

    /**
     * 权益包子券金额（参与对账）
     */
    @TableField("pkg_child_amount")
    private BigDecimal pkgChildAmount;

    /**
     * 补单任务id
     */
    @TableField("task_id")
    private String taskId;


    /**
     * 自动提现 0:否 1:是
     */
    @TableField("auto_withdraw")
    private Integer autoWithdraw;
    public static final String SHOP_ID = "shop_id";
    public static final String ACCOUNT_ID = "account_id";
    public static final String SERIAL_NO = "serial_no";
    public static final String SHOP_SERIAL_NO = "shop_serial_no";
    public static final String AUTO_WITHDRAW = "auto_withdraw";
    /**
     * 总结算单金额，单位：元
     */
    @TableField("settle_form_amount")
    private BigDecimal settleFormAmount;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public static final String SETTLE_DATE = "settle_date";

    public static final String SETTLE_FORM_AMOUNT = "settle_form_amount";

    public static final String SETTLE_FORM_STATUS = "settle_form_status";

    public static final String REJECTED_REASON = "rejected_reason";

    public static final String PLATFORM_TYPE = "platform_type";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String NON_BUSINESS_AMOUNT = "non_business_amount";

    public static final String BUSINESS_CHILD_CARD_AMOUNT = "business_child_card_amount";
    public static final String TASK_ID = "task_id";

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

}
