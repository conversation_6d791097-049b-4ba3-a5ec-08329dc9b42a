package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * <p>
 * 抖音RPA退款订单申请
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-12
 */
@Data
@TableName("gosh_rpa_douyin_life_refund_apply")
public class GoshRpaDouyinLifeRefundApplyDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 售后单Id（退款订单Id）
     */
    @TableField("after_sale_id")
    private String afterSaleId;

    /**
     * 订单号
     */
    @TableField("order_sn")
    private String orderSn;

    /**
     * 外部订单号
     */
    @TableField("out_order_sn")
    private String outOrderSn;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 申请单状态
     */
    @TableField("audit_status")
    private Integer auditStatus;

    /**
     * 审核链接
     */
    @TableField("audit_link")
    private String auditLink;

    /**
     * 是否删除： 0-未删除, 1-已删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 抖音商品单号
     */
    @TableField("item_order_id")
    private String itemOrderId;

    /**
     * 集团应用Id
     */
    @TableField("app_id")
    private String appId;

    /**
     * 申请时间
     */
    @TableField("apply_time")
    private Date applyTime;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private Date auditTime;

    /**
     * 券码
     */
    @TableField("coupon_code")
    private String couponCode;

    /**
     * 核销广场POI_ID
     */
    @TableField("verification_store_id")
    private String verificationStoreId;

    /**
     * 核销广场名称
     */
    @TableField("verification_store_name")
    private String verificationStoreName;

    /**
     * 是否撤销 1-未撤销 2-已撤销
     */
    @TableField("coupon_status")
    private Integer couponStatus;

    /**
     * 商品Id
     */
    @TableField("tiktok_product_id")
    private String tiktokProductId;

    /**
     * 商品名称
     */
    @TableField("tiktok_product_name")
    private String tiktokProductName;

    /**
     * 顾客手机号
     */
    @TableField("customer_phone")
    private String customerPhone;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 退款数量
     */
    @TableField("refund_count")
    private Integer refundCount;

    /**
     * 退款原因
     */
    @TableField("refund_reason")
    private String refundReason;

    /**
     * 退款状态:1-待处理 ；2-已处理
     */
    @TableField("refund_status")
    private Integer refundStatus;

    /**
     * 退款操作时间
     */
    @TableField("refund_time")
    private Date refundTime;

    /**
     * 退款操作人
     */
    @TableField("refund_name")
    private String refundName;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

    public Date getApplyTime() {
        if (this.applyTime != null) {
            return new Date(this.applyTime.getTime());
        } else {
            return null;
        }
    }

    public void setApplyTime(Date applyTime) {
        if (applyTime != null) {
            this.applyTime = new Date(applyTime.getTime());
        } else {
            this.applyTime = null;
        }
    }

    public Date getAuditTime() {
        if (this.auditTime != null) {
            return new Date(this.auditTime.getTime());
        } else {
            return null;
        }
    }

    public void setAuditTime(Date auditTime) {
        if (auditTime != null) {
            this.auditTime = new Date(auditTime.getTime());
        } else {
            this.auditTime = null;
        }
    }


    public static final String ID = "id";

    public static final String AFTER_SALE_ID = "after_sale_id";

    public static final String ORDER_SN = "order_sn";

    public static final String OUT_ORDER_SN = "out_order_sn";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String AUDIT_STATUS = "audit_status";

    public static final String AUDIT_LINK = "audit_link";

    public static final String IS_DEL = "is_del";

    public static final String ITEM_ORDER_ID = "item_order_id";

    public static final String APP_ID = "app_id";

    public static final String APPLY_TIME = "apply_time";

    public static final String AUDIT_TIME = "audit_time";

    public static final String COUPON_CODE = "coupon_code";

    public static final String VERIFICATION_STORE_ID = "verification_store_id";

    public static final String VERIFICATION_STORE_NAME = "verification_store_name";

    public static final String COUPON_STATUS = "coupon_status";

    public static final String TIKTOK_PRODUCT_ID = "tiktok_product_id";

    public static final String TIKTOK_PRODUCT_NAME = "tiktok_product_name";

    public static final String CUSTOMER_PHONE = "customer_phone";

    public static final String REFUND_AMOUNT = "refund_amount";

    public static final String REFUND_COUNT = "refund_count";

    public static final String REFUND_REASON = "refund_reason";

    public static final String REFUND_STATUS = "refund_status";

}
