package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 来逛呗用户商铺表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13
 */
@Data
@TableName("gosh_user_shop")
public class GoshUserShopDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 商铺 id
     */
    @TableField("shop_id")
    private String shopId;

    /**
     * 角色 id
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 身份id
     */
    @TableField("identity_id")
    private String identityId;

    public static final String IDENTITY_ID = "identity_id";
    public static final String IS_ADMIN = "is_admin";
    public static final String NAME = "name";
    public static final String BLOC_ID = "bloc_id";

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String USER_ID = "user_id";

    public static final String SHOP_ID = "shop_id";

    public static final String ROLE_ID = "role_id";

    public static final String IS_DEL = "is_del";
    /**
     * 是否为管理员 1-是 2-否
     */
    @TableField("is_admin")
    private Integer isAdmin;
    /**
     * 姓名
     */
    @TableField("name")
    private String name;
    /**
     * 集团ID
     */
    @TableField("bloc_id")
    private String blocId;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

}
