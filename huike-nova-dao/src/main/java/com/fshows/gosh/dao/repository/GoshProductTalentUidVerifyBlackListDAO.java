package com.fshows.gosh.dao.repository;

import com.fshows.gosh.dao.entity.GoshProductTalentUidVerifyBlackListDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 来逛呗商品-达人uid核销黑名单 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
public interface GoshProductTalentUidVerifyBlackListDAO extends IService<GoshProductTalentUidVerifyBlackListDO> {
    /**
     * 根据商品和达人查询查询黑名单记录
     *
     * @param productId 商品Id
     * @param uid 达人Uid
     * @return 黑名单记录
     */
    GoshProductTalentUidVerifyBlackListDO getRecord(String productId, String uid);
}
