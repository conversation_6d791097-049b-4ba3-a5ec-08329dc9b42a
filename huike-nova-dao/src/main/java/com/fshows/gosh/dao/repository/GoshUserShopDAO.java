package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.domain.param.UserShopParamDTO;
import com.fshows.gosh.dao.entity.GoshUserShopDO;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.dao.domain.param.ConfirmShopParamDTO;

import java.util.List;

/**
 * <p>
 * 来逛呗用户商铺表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface GoshUserShopDAO extends IService<GoshUserShopDO> {

    /**
     * 根据userId查询店铺列表
     *
     * @param userId
     * @return
     */
    List<UserShopParamDTO> findByUserId(String userId);

    /**
     * 查询用户未确认的商铺列表
     *
     * @param userId
     * @return
     */
    List<UserShopParamDTO> findUnconfirmedShopList(String userId);

    /**
     * 查询已确认商铺选择列表
     *
     * @param userId
     * @return
     */
    List<UserShopParamDTO> findConfirmShopList(String userId);

    /**
     * 分页查询已确认商铺选择列表
     *
     * @param pageParam 参数
     * @return return 返回
     */
    Page<UserShopParamDTO> pageConfirmShopList(PageParam<ConfirmShopParamDTO> pageParam);

    /**
     * 获取选择商铺信息
     *
     * @param shopId
     * @param userId
     * @param confirmStatus
     * @return
     */
    UserShopParamDTO getChooseShopDetail(String shopId, String userId, Integer confirmStatus);

    /**
     * 门店列表查询
     *
     * @param userId
     * @param shopName
     * @param confirmStatus
     * @return
     */
    List<UserShopParamDTO> findShopList(String userId, String shopName, Integer confirmStatus);

    /**
     * 根据商铺id和用户id查询商铺信息
     *
     * @param shopId
     * @param userId
     * @return
     */
    GoshUserShopDO getByShopIdAndUserId(String shopId, String userId);


    /**
     * 根据角色id查询关联信息
     *
     * @param roleId
     * @return
     */
    List<GoshUserShopDO> findRoleByRoleId(String roleId);

    /**
     * 保存关联信息
     *
     * @param goshUserShopDO
     * @return
     */
    boolean saveUserShop(GoshUserShopDO goshUserShopDO);

    /**
     * 更新关联信息
     *
     * @param identityId
     * @param name
     * @param roleId
     * @return
     */
    boolean updateAccount(String identityId, String name, String roleId);

    /**
     * 删除关联信息
     *
     * @param identityId
     * @return
     */
    boolean deleteAccount(String identityId);

    /**
     * 根据身份id查询关联信息
     *
     * @param identityId
     * @return
     */
    GoshUserShopDO getUserShopByIdentityId(String identityId);

    /**
     * 查询商铺管理员身份信息
     *
     * @param shopId 商铺id
     * @return
     */
    GoshUserShopDO getAdminByShopId(String shopId);
}
