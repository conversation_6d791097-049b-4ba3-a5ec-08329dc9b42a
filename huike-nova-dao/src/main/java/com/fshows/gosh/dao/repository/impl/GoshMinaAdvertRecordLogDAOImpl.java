package com.fshows.gosh.dao.repository.impl;

import com.fshows.gosh.dao.entity.GoshMinaAdvertRecordLogDO;
import com.fshows.gosh.dao.mapper.GoshMinaAdvertRecordLogMapper;
import com.fshows.gosh.dao.repository.GoshMinaAdvertRecordLogDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 来逛呗商家版小程序广告商户日访问记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Service
public class GoshMinaAdvertRecordLogDAOImpl extends ServiceImpl<GoshMinaAdvertRecordLogMapper, GoshMinaAdvertRecordLogDO> implements GoshMinaAdvertRecordLogDAO {

    /**
     * 根据日期、广告id、openId、类型查询记录
     *
     * @param visitDay 日期
     * @param advertId 广告id
     * @param openId   微信用户openId
     * @param type     类型
     * @return 返回类型
     */
    @Override
    public GoshMinaAdvertRecordLogDO findByDayAndAdvertId(Integer visitDay, String advertId, String openId, Integer type) {
        return query().eq(GoshMinaAdvertRecordLogDO.VISIT_DAY, visitDay)
                .eq(GoshMinaAdvertRecordLogDO.ADVERT_ID, advertId)
                .eq(GoshMinaAdvertRecordLogDO.OPEN_ID, openId)
                .eq(GoshMinaAdvertRecordLogDO.TYPE, type)
                .last("limit 1")
                .one();
    }
}
