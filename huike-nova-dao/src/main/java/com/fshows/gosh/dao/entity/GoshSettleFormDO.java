package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 结算单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
@Data
@TableName("gosh_settle_form")
public class GoshSettleFormDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 结算单号
     */
    @TableField("serial_no")
    private String serialNo;

    /**
     * 结算日期（yyyyMMdd）
     */
    @TableField("settle_date")
    private String settleDate;

    /**
     * 集团ID
     */
    @TableField("bloc_id")
    private String blocId;

    /**
     * 总结算单金额，单位：元
     */
    @TableField("settle_form_amount")
    private BigDecimal settleFormAmount;

    /**
     * 结算单状态 0=初始化 1=打款确认 2=出款中 3=出款成功
     */
    @TableField("settle_form_status")
    private Integer settleFormStatus;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    /**
     * 来源：BATCH-脚本自动生成   MANUAL-人工手动插入
     */
    @TableField("source")
    private String source;

    /**
     * 平台类型 TIKTOK-抖音 ALIPAY-支付宝 MEITUAN-美团
     */
    @TableField("platform_type")
    private String platformType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 普通券（非商圈卡）金额，单位：元
     */
    @TableField("non_business_amount")
    private BigDecimal nonBusinessAmount;

    /**
     * 商家券（子券）金额，单位：元
     */
    @TableField("business_child_card_amount")
    private BigDecimal businessChildCardAmount;

    /**
     * 商圈卡（母券）金额，单位：元（和总结算金额没有关系）
     */
    @TableField("business_mother_card_amount")
    private BigDecimal businessMotherCardAmount;

    /**
     * 转账状态 0-待发起 1-转账已确认 2-无需转账 3-转账成功 4-转账失败
     */
    @TableField("transfer_status")
    private Integer transferStatus;

    /**
     * 母券正向差异
     */
    @TableField("mother_income_diff_amount")
    private BigDecimal motherIncomeDiffAmount;

    /**
     * 母券正向差异
     */
    @TableField("mother_deduct_diff_amount")
    private BigDecimal motherDeductDiffAmount;

    /**
     * 对账单状态：0-初始化状态； 1-对账成功；2-对账失败
     */
    @TableField("bill_status")
    private Integer billStatus;

    /**
     * 对账来源: SYSTEM-系统对账; HAND - 手动对账
     */
    @TableField("bill_source")
    private String billSource;

    /**
     * 操作人: SYSTEM - 系统操作
     */
    @TableField("operator")
    private String operator;

    /**
     * 结算通道; FUYOU-富友；INTERNET_BANK -网商银行
     */
    @TableField("settle_channel")
    private String settleChannel;

    /**
     * 美团类型  1 点评（老美团）2 美团到餐 3 美团到综
     */
    @TableField("mei_tuan_type")
    private Integer meiTuanType;

    /**
     * 权益包子券金额（参与对账）
     */
    @TableField("pkg_child_amount")
    private BigDecimal pkgChildAmount;

    public static final String MERGE_TRANSFER_NO = "merge_transfer_no";
    public static final String MERGE_TRANSFER_STATUS = "merge_transfer_status";
    public static final String DIFF_AMOUNT = "diff_amount";
    public static final String DIFF_COMMISSION = "diff_commission";
    public static final String INCOME_DIFF_AMOUNT = "income_diff_amount";
    public static final String DEDUCT_DIFF_AMOUNT = "deduct_diff_amount";
    public static final String MOTHER_INCOME_DIFF_AMOUNT = "mother_income_diff_amount";
    public static final String MOTHER_DEDUCT_DIFF_AMOUNT = "mother_deduct_diff_amount";
    public static final String OUT_PRODUCT_SETTLE_AMOUNT = "out_product_settle_amount";
    public static final String BILL_STATUS = "bill_status";
    public static final String BILL_SOURCE = "bill_source";
    public static final String OPERATOR = "operator";
    public static final String MEI_TUAN_TYPE = "mei_tuan_type";

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String SERIAL_NO = "serial_no";

    public static final String SETTLE_DATE = "settle_date";

    public static final String BLOC_ID = "bloc_id";

    public static final String SETTLE_FORM_AMOUNT = "settle_form_amount";

    public static final String SETTLE_FORM_STATUS = "settle_form_status";

    public static final String REMARK = "remark";

    public static final String SOURCE = "source";

    public static final String PLATFORM_TYPE = "platform_type";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String NON_BUSINESS_AMOUNT = "non_business_amount";

    public static final String BUSINESS_CHILD_CARD_AMOUNT = "business_child_card_amount";

    public static final String BUSINESS_MOTHER_CARD_AMOUNT = "business_mother_card_amount";

    public static final String TRANSFER_STATUS = "transfer_status";
    /**
     * 合并转账单号
     */
    @TableField("merge_transfer_no")
    private String mergeTransferNo;
    /**
     * 合并转账状态 0-待发起 1-转账已确认 2-无需转账 3-转账成功 4-转账失败
     */
    @TableField("merge_transfer_status")
    private Integer mergeTransferStatus;
    /**
     * 差异金额
     */
    @TableField("diff_amount")
    private BigDecimal diffAmount;
    /**
     * 差异佣金
     */
    @TableField("diff_commission")
    private BigDecimal diffCommission;
    /**
     * 正向差异金额
     */
    @TableField("income_diff_amount")
    private BigDecimal incomeDiffAmount;
    /**
     * 逆向差异金额
     */
    @TableField("deduct_diff_amount")
    private BigDecimal deductDiffAmount;

    /**
     * 外部商品出款金额
     */
    @TableField("out_product_settle_amount")
    private BigDecimal outProductSettleAmount;
}
