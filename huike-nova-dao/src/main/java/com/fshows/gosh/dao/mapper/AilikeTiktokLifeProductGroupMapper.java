package com.fshows.gosh.dao.mapper;

import com.fshows.gosh.dao.entity.AilikeTiktokLifeProductGroupDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 抖音来客商品组表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
public interface AilikeTiktokLifeProductGroupMapper extends BaseMapper<AilikeTiktokLifeProductGroupDO> {

    /**
     * 根据商家券的卡号查询商品组的记录
     *
     * @param merchantCardId 商家券卡券
     * @return 商品组记录
     */
    AilikeTiktokLifeProductGroupDO getProductGroupByQykMerchantCardId(@Param("merchantCardId") String merchantCardId);
}
