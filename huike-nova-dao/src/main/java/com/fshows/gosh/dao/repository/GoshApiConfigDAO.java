package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.gosh.dao.entity.GoshApiConfigDO;

/**
 * <p>
 * 来逛呗-接口调用配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
public interface GoshApiConfigDAO extends IService<GoshApiConfigDO> {

    /**
     * 查询appid
     *
     * @param userId
     * @param userType
     * @return
     */
    GoshApiConfigDO getByUerId(String userId, String userType);
}
