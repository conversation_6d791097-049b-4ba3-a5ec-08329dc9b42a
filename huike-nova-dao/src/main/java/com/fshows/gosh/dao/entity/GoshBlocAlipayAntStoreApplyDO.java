package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 来逛呗蚂蚁门店申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-07
 */
@Data
@TableName("gosh_bloc_alipay_ant_store_apply")
public class GoshBlocAlipayAntStoreApplyDO implements Serializable {

    public static final String ID = "id";
    public static final String BLOC_ID = "bloc_id";
    public static final String ORG_ID = "org_id";
    public static final String SHOP_ID = "shop_id";
    public static final String STORE_ID = "store_id";
    public static final String IP_ROLE_ID = "ip_role_id";
    public static final String SHOP_TYPE = "shop_type";
    public static final String STORE_NAME = "store_name";
    public static final String APPLY_STORE_ID = "apply_store_id";
    public static final String APPLY_STORE_NAME = "apply_store_name";
    public static final String PROVINCE_CODE = "province_code";
    public static final String CITY_CODE = "city_code";
    public static final String AREA_CODE = "area_code";
    public static final String FULL_ADDRESS = "full_address";
    public static final String ADDRESS = "address";
    public static final String LONGITUDE = "longitude";
    public static final String LATITUDE = "latitude";
    public static final String TEL = "tel";
    public static final String CATEGORY_NAME = "category_name";
    public static final String SHOP_CATEGORY = "shop_category";
    public static final String BIZ_NO = "biz_no";
    public static final String SHOP_STATUS = "shop_status";
    public static final String ORDER_ID = "order_id";
    public static final String FAIL_TYPE = "fail_type";
    public static final String FAIL_REASON = "fail_reason";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 申请蚂蚁门店时的集团id
     */
    @TableField("bloc_id")
    private String blocId;
    /**
     * 申请蚂蚁门店时的组织id
     */
    @TableField("org_id")
    private String orgId;
    /**
     * 支付宝侧蚂蚁店铺ID
     */
    @TableField("shop_id")
    private String shopId;
    /**
     * 门店ID
     */
    @TableField("store_id")
    private String storeId;
    /**
     * 直连的pid:来团呗2088341081135659
     */
    @TableField("ip_role_id")
    private String ipRoleId;
    /**
     * 店铺经营类型，01表示直营，02表示加盟
     */
    @TableField("shop_type")
    private String shopType;
    /**
     * 店铺名称
     */
    @TableField("store_name")
    private String storeName;
    /**
     * 申请蚂蚁门店时使用的门店ID
     */
    @TableField("apply_store_id")
    private String applyStoreId;
    /**
     * 申请蚂蚁门店时使用的门店名称
     */
    @TableField("apply_store_name")
    private String applyStoreName;
    /**
     * 省级行政区域代码
     */
    @TableField("province_code")
    private String provinceCode;
    /**
     * 市级行政区域代码
     */
    @TableField("city_code")
    private String cityCode;
    /**
     * 区级行政区域代码
     */
    @TableField("area_code")
    private String areaCode;
    /**
     * 完整地址
     */
    @TableField("full_address")
    private String fullAddress;
    /**
     * 详细地址
     */
    @TableField("address")
    private String address;
    /**
     * 门店经度
     */
    @TableField("longitude")
    private String longitude;
    /**
     * 门店纬度
     */
    @TableField("latitude")
    private String latitude;
    /**
     * 门店电话
     */
    @TableField("tel")
    private String tel;
    /**
     * 蚂蚁门店类目名，格式：一级类目名-二级类目名-三级类目名
     */
    @TableField("category_name")
    private String categoryName;
    /**
     * 蚂蚁门店类目三级类目编码
     */
    @TableField("shop_category")
    private String shopCategory;
    /**
     * 业务唯一编号UUID
     */
    @TableField("biz_no")
    private String bizNo;
    /**
     * 开通状态：1-未开通;2-开通中;3-已开通;4-驳回
     */
    @TableField("shop_status")
    private Integer shopStatus;
    /**
     * 蚂蚁门店创建申请的ID
     */
    @TableField("order_id")
    private String orderId;
    /**
     * 失败类型
     */
    @TableField("fail_type")
    private String failType;
    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;
    /**
     * 删除标记：0-正常 1-删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

}
