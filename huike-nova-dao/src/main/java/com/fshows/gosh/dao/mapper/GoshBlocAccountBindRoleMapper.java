package com.fshows.gosh.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fshows.gosh.dao.domain.result.RoleAccountCountResultDTO;
import com.fshows.gosh.dao.entity.GoshBlocAccountBindRoleDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 账号和角色关系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface GoshBlocAccountBindRoleMapper extends BaseMapper<GoshBlocAccountBindRoleDO> {

    /**
     * 根据组织id和账号查询角色列表
     *
     * @param orgId
     * @param accountId
     * @return
     */
    List<String> getRoleListByAccountAndOrgId(@Param("orgId") String orgId, @Param("accountId") String accountId);

    /**
     * 根据角色id列表查询角色数量
     *
     * @param roleIdList
     * @return
     */
    List<RoleAccountCountResultDTO> countByRoleIdList(@Param("roleIdList") List<String> roleIdList);

    /**
     * 根据角色id查询角色关联账号数量
     *
     * @param roleId 角色id
     * @return
     */
    List<String> getByRoleId(@Param("roleId") String roleId);
}
