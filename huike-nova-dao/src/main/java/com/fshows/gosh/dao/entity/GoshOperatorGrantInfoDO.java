/**
 * <AUTHOR>
 * @date 2024/10/31 15:08
 * @version 1.0 GoshOperatorGrantInfo
 */
package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 运营后台权限表
 *
 * <AUTHOR>
 * @version GoshOperatorGrantInfo.java, v 0.1 2024-10-31 15:08 tuyuwei
 */
@Data
@TableName("gosh_operator_grant_info")
public class GoshOperatorGrantInfoDO implements Serializable {

    public static final String ID = "id";
    public static final String GRANT_ID = "grant_id";
    public static final String GRANT_CODE = "grant_code";
    public static final String GRANT_NAME = "grant_name";
    public static final String PARENT_GRANT_ID = "parent_grant_id";
    public static final String GRANT_TYPE = "grant_type";
    public static final String ACCOUNT_TYPE = "account_type";
    public static final String SORT = "sort";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 权限id
     */
    @TableField("grant_id")
    private String grantId;

    /**
     * 权限编码
     */
    @TableField("grant_code")
    private String grantCode;

    /**
     * 权限名称
     */
    @TableField("grant_name")
    private String grantName;

    /**
     * 上级权限id
     */
    @TableField("parent_grant_id")
    private String parentGrantId;

    /**
     * 权限类型(1菜单,2功能)
     */
    @TableField("grant_type")
    private Integer grantType;

    /**
     * 账号类型:1-全集团账号；2-部分集团权限账号
     */
    @TableField("account_type")
    private Integer accountType;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
}