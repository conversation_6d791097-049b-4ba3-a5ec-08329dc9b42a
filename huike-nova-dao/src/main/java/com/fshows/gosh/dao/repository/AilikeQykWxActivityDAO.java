package com.fshows.gosh.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.gosh.dao.domain.param.ActivityListParamDTO;
import com.fshows.gosh.dao.domain.param.ProductStatisticsDTO;
import com.fshows.gosh.dao.domain.result.ActivityListResultDTO;
import com.fshows.gosh.dao.domain.result.ActivityStatusStatisticsResultDTO;
import com.fshows.gosh.dao.domain.result.QueryProductListResultDTO;
import com.fshows.gosh.dao.entity.AilikeQykWxActivityDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import org.apache.ibatis.session.ResultHandler;

import java.util.List;

/**
 * <p>
 * 微信营销活动 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface AilikeQykWxActivityDAO extends IService<AilikeQykWxActivityDO> {
    /**
     * 根据商品Id查询活动明细
     *
     * @param productId 商品id
     * @return
     */
    AilikeQykWxActivityDO findByProductId(String productId);

    /**
     * 根据商品Id查询活动列表
     *
     * @param productIdList 商品id列表
     * @return
     */
    List<AilikeQykWxActivityDO> findListByProductdIdList(List<String> productIdList);

    /**
     * 根据活动Id查询活动明细
     *
     * @param activityId 活动Id
     * @return
     */
    AilikeQykWxActivityDO findByActivityId(String activityId);


    /**
     * 营销活动列表
     *
     * @param pageParam 参数
     * @return 活动列表
     */
    Page<ActivityListResultDTO> pageActivityList(PageParam<ActivityListParamDTO> pageParam);

    /**
     * 营销活动列表
     *
     * @param pageParam 参数
     * @return 活动列表
     */
    List<ActivityStatusStatisticsResultDTO> activityListStatistics(ActivityListParamDTO pageParam);

    /**
     * 导出商品信息
     *
     * @param dto           参数
     * @param resultHandler 结果数据
     */
    void exportActivityList(ActivityListParamDTO dto, ResultHandler<ActivityListResultDTO> resultHandler);

    /**
     * 营销活动上线
     */
    void activityOnline();


    /**
     * 营销活动下线
     */
    void activityOffline();

    /**
     * 查询线上活动列表
     *
     * @param dataId 数据Id
     * @return
     */
    List<AilikeQykWxActivityDO> findOnlineActivityList(Long dataId);
}
