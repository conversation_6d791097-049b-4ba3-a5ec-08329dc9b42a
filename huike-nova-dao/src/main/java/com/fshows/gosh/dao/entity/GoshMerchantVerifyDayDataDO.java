package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 来逛呗-商家核销数据汇总
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Data
@TableName("gosh_merchant_verify_day_data")
public class GoshMerchantVerifyDayDataDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 集团AppId
     */
    @TableField("app_id")
    private String appId;

    /**
     * 付呗uid
     */
    @TableField("user_id")
    private String userId;

    /**
     * 门店id
     */
    @TableField("store_id")
    private String storeId;

    /**
     * 平台
     */
    @TableField("channel_type")
    private String channelType;

    /**
     * 商品类型:1-团购券;11-代金券;111-商圈卡
     */
    @TableField("product_type")
    private Integer productType;

    /**
     * 核销金额-券原价
     */
    @TableField("verify_amount")
    private BigDecimal verifyAmount;

    /**
     * 核销券数
     */
    @TableField("verify_num")
    private Integer verifyNum;

    /**
     * 核销消费者数量
     */
    @TableField("verify_customer_num")
    private Integer verifyCustomerNum;

    /**
     * 核销日期
     */
    @TableField("verify_day")
    private Integer verifyDay;

    /**
     * 0正常 1删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String APP_ID = "app_id";

    public static final String USER_ID = "user_id";

    public static final String STORE_ID = "store_id";

    public static final String CHANNEL_TYPE = "channel_type";

    public static final String PRODUCT_TYPE = "product_type";

    public static final String VERIFY_AMOUNT = "verify_amount";

    public static final String VERIFY_NUM = "verify_num";

    public static final String VERIFY_CUSTOMER_NUM = "verify_customer_num";

    public static final String VERIFY_DAY = "verify_day";

    public static final String IS_DEL = "is_del";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}
