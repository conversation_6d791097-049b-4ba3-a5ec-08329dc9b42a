package com.fshows.gosh.dao.repository.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.fshows.gosh.dao.entity.GoshShopTransferDO;
import com.fshows.gosh.dao.mapper.GoshShopTransferMapper;
import com.fshows.gosh.dao.repository.GoshShopTransferDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.enums.acct.TransferStatusEnum;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 商户转账表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Service
public class GoshShopTransferDAOImpl extends ServiceImpl<GoshShopTransferMapper, GoshShopTransferDO> implements GoshShopTransferDAO {

    @Override
    public boolean updateByTransferNo(String transferNo, String transferStatus, String reason, Date finishTime, String platformTransferNo) {
        return update()
                .set(GoshShopTransferDO.TRANSFER_STATUS, transferStatus)
                .set(StrUtil.isNotBlank(reason), GoshShopTransferDO.REJECTED_REASON, reason)
                .set(ObjectUtil.isNotNull(finishTime), GoshShopTransferDO.FINISH_TIME, finishTime)
                .set(StrUtil.isNotBlank(platformTransferNo), GoshShopTransferDO.PLATFORM_TRANSFER_NO, platformTransferNo)
                .eq(GoshShopTransferDO.TRANSFER_NO, transferNo).update();
    }

    @Override
    public List<GoshShopTransferDO> getListByShopSerialNo(String shopSerialNo) {
        QueryChainWrapper<GoshShopTransferDO> eq = query().eq(GoshShopTransferDO.SHOP_SERIAL_NO, shopSerialNo);
        return eq.list();
    }

    @Override
    public GoshShopTransferDO getByTransferNo(String transferNo) {
        return query().eq(GoshShopTransferDO.TRANSFER_NO, transferNo).one();
    }

    @Override
    public GoshShopTransferDO getBySerialNoAndTransferStatus(String serialNo, String transferStatus) {
        return query().eq(GoshShopTransferDO.SHOP_SERIAL_NO, serialNo)
                .eq(GoshShopTransferDO.TRANSFER_STATUS, transferStatus).one();
    }

    @Override
    public boolean updateWithdrawNoByTransferNo(String transferNo, String withdrawNo) {
        return update()
                .set(GoshShopTransferDO.WITHDRAW_NO, withdrawNo)
                .eq(GoshShopTransferDO.TRANSFER_NO, transferNo).update();
    }

    @Override
    public List<GoshShopTransferDO> getListByStatusAndTimeRange(String transferStatus, Date startTime, Date endTime) {
        return query().eq(GoshShopTransferDO.TRANSFER_STATUS, transferStatus)
                .between(GoshShopTransferDO.CREATE_TIME, startTime, endTime).list();

    }

    @Override
    public GoshShopTransferDO getLastOneByShopSerialNo(String shopSerialNo) {
        return query().eq(GoshShopTransferDO.SHOP_SERIAL_NO, shopSerialNo)
                .orderByDesc(GoshShopTransferDO.CREATE_TIME).last("limit 1").one();

    }

    /**
     * 获取门店维度转账记录明细
     *
     * @param platformTransferNo 平台转账id
     * @return
     */
    @Override
    public List<GoshShopTransferDO> getListByPlatformTransferNo(String platformTransferNo) {
        return query().eq(GoshShopTransferDO.PLATFORM_TRANSFER_NO, platformTransferNo).list();
    }
}
