package com.fshows.gosh.dao.repository.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.gosh.dao.entity.GoshBlocGrantInfoDO;
import com.fshows.gosh.dao.mapper.GoshBlocGrantInfoMapper;
import com.fshows.gosh.dao.repository.GoshBlocGrantInfoDAO;
import com.google.common.collect.Lists;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 后台权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Service
public class GoshBlocGrantInfoDAOImpl extends ServiceImpl<GoshBlocGrantInfoMapper, GoshBlocGrantInfoDO> implements GoshBlocGrantInfoDAO {

    /**
     * 获取集团所有权限
     *
     * @param blocId
     * @return
     */
    @Override
    public List<GoshBlocGrantInfoDO> findByBlocId(String blocId) {
        return getBaseMapper().findByBlocId(blocId);
    }

    /**
     * 获取角色所有权限
     *
     * @param roleIds
     * @return
     */
    @Override
    public List<GoshBlocGrantInfoDO> findByRoleIds(List<String> roleIds) {
        if (CollectionUtil.isEmpty(roleIds)) {
            return Lists.newArrayList();
        }
        return getBaseMapper().findByRoleIds(roleIds);
    }

    /**
     * 获取权限列表
     *
     * @param grantIdList
     * @return
     */
    @Override
    public List<GoshBlocGrantInfoDO> findByGrantIdList(List<String> grantIdList) {
        return query().in(GoshBlocGrantInfoDO.GRANT_ID, grantIdList).eq(GoshBlocGrantInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }

    /**
     * 获取集团后台所有权限列表
     *
     * @return 集团后台所有权限列表
     */
    @Override
    public List<GoshBlocGrantInfoDO> findAll() {
        return query().eq(GoshBlocGrantInfoDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue()).list();
    }
}
