package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 商户提现表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
@Data
@TableName("gosh_shop_withdraw")
public class GoshShopWithdrawDO implements Serializable {

    public static final String TRANSFER_NO = "transfer_no";
    public static final String CURRENT_BALANCE = "current_balance";
    public static final String SETTLE_ACCOUNT_NO = "settle_account_no";
    public static final String BANK_NAME = "bank_name";
    public static final String WITHDRAW_MODE = "withdraw_mode";
    public static final String IS_REWITHDRAW = "is_rewithdraw";
    public static final String ELECTRONIC_RECEIPT_URL = "electronic_receipt_url";
    public static final String ID = "id";
    public static final String SHOP_ID = "shop_id";
    public static final String ACCOUNT_ID = "account_id";
    public static final String SHOP_SERIAL_NO = "shop_serial_no";
    public static final String WITHDRAW_NO = "withdraw_no";
    public static final String ORIGINAL_WITHDRAW_NO = "original_withdraw_no";
    public static final String PLATFORM_WITHDRAW_NO = "platform_withdraw_no";
    public static final String WITHDRAW_DATE = "withdraw_date";
    public static final String WITHDRAW_AMOUNT = "withdraw_amount";
    public static final String WITHDRAW_FEE = "withdraw_fee";
    public static final String WITHDRAW_STATUS = "withdraw_status";
    public static final String GLOBAL_TRACK_STATUS = "global_track_status";
    public static final String REJECTED_REASON = "rejected_reason";
    public static final String SOURCE = "source";
    public static final String PLATFORM_TYPE = "platform_type";
    public static final String FINISH_TIME = "finish_time";
    public static final String IS_REFUNDED = "is_refunded";
    public static final String IS_DEL = "is_del";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_TIME = "update_time";
    public static final String ACCOUNT_NAME = "account_name";
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 商铺id
     */
    @TableField("shop_id")
    private String shopId;
    /**
     * 账户id
     */
    @TableField("account_id")
    private String accountId;
    /**
     * 商铺结算单号 gosh_shop_settle_form shop_serial_no
     */
    @TableField("shop_serial_no")
    private String shopSerialNo;
    /**
     * 提现订单号
     */
    @TableField("withdraw_no")
    private String withdrawNo;
    /**
     * 原始提现订单号
     */
    @TableField("original_withdraw_no")
    private String originalWithdrawNo;
    /**
     * 平台方提现订单号
     */
    @TableField("platform_withdraw_no")
    private String platformWithdrawNo;
    /**
     * 提现日期(yyyymmdd)
     */
    @TableField("withdraw_date")
    private String withdrawDate;
    /**
     * 提现金额
     */
    @TableField("withdraw_amount")
    private BigDecimal withdrawAmount;
    /**
     * 提现手续费，单位：元
     */
    @TableField("withdraw_fee")
    private BigDecimal withdrawFee;
    /**
     * 提现状态  WITHDRAW_INIT-初始化;WITHDRAW_PROCESSING-提现中;WITHDRAW_SUCCESS-提现成功;WITHDRAW_FAIL-提现失败
     */
    @TableField("withdraw_status")
    private String withdrawStatus;
    /**
     * 全局跟踪状态，定位实际在哪个节点: TRANSFER_INIT-转账初始化;TRANSFER_PROCESSING-转账中;TRANSFER_SUCCESS_WAIT_WITHDRAW-转账成功待提现;TRANSFER_FAIL-转账失败;WITHDRAW_PROCESSING-提现中;WITHDRAW_SUCCESS-提现成功;WITHDRAW_FAIL-提现失败;
     */
    @TableField("global_track_status")
    private String globalTrackStatus;
    /**
     * 失败原因
     */
    @TableField("rejected_reason")
    private String rejectedReason;
    /**
     * 来源 BATCH-脚本
     */
    @TableField("source")
    private String source;
    /**
     * 平台类型 TIKTOK-抖音 ALIPAY-支付宝 MEITUAN-美团
     */
    @TableField("platform_type")
    private String platformType;
    /**
     * 提现完成时间
     */
    @TableField("finish_time")
    private Date finishTime;
    /**
     * 是否退票 0：否 1：是
     */
    @TableField("is_refunded")
    private Integer isRefunded;
    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 关联转账单号
     */
    @TableField("transfer_no")
    private String transferNo;
    /**
     * 银行卡账号
     */
    @TableField("settle_account_no")
    private String settleAccountNo;
    /**
     * 可用余额，单位：分
     */
    @TableField("current_balance")
    private Long currentBalance;
    /**
     * 银行名称
     */
    @TableField("bank_name")
    private String bankName;
    /**
     * 银行卡户名
     */
    @TableField("account_name")
    private String accountName;
    /**
     * 提现方式  1-自动 2-手动
     */
    @TableField("withdraw_mode")
    private Integer withdrawMode;
    /**
     * 是否可以重新提现 1-是 2-否
     */
    @TableField("is_rewithdraw")
    private Integer isRewithdraw;
    /**
     * 电子回单链接
     */
    @TableField("electronic_receipt_url")
    private String electronicReceiptUrl;

    public Date getCreateTime() {
        if (this.createTime != null) {
            return new Date(this.createTime.getTime());
        } else {
            return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }

    public Date getUpdateTime() {
        if (this.updateTime != null) {
            return new Date(this.updateTime.getTime());
        } else {
            return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }

    public Date getFinishTime() {
        if (this.finishTime != null) {
            return new Date(this.finishTime.getTime());
        } else {
            return null;
        }
    }

    public void setFinishTime(Date finishTime) {
        if (finishTime != null) {
            this.finishTime = new Date(finishTime.getTime());
        } else {
            this.finishTime = null;
        }
    }

}
