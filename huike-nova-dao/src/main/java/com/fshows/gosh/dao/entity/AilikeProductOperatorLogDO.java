package com.fshows.gosh.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 商品操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Data
@TableName("ailike_product_operator_log")
public class AilikeProductOperatorLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 操作业务日志Id
     */
    @TableField("log_id")
    private String logId;

    /**
     * 业务商品Id
     */
    @TableField("business_product_id")
    private String businessProductId;

    /**
     * 操作类型
     */
    @TableField("operator_type")
    private String operatorType;

    /**
     * 操作类型
     */
    @TableField("modify_before")
    private String modifyBefore;

    /**
     * 操作类型
     */
    @TableField("modify_after")
    private String modifyAfter;

    /**
     * 操作描述
     */
    @TableField("remark")
    private String remark;

    /**
     * 删除标记 1 未删除  2已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * ip地址
     */
    @TableField("ip")
    private String ip;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String LOG_ID = "log_id";

    public static final String BUSINESS_PRODUCT_ID = "business_product_id";

    public static final String OPERATOR_TYPE = "operator_type";

    public static final String MODIFY_BEFORE = "modify_before";

    public static final String MODIFY_AFTER = "modify_after";

    public static final String REMARK = "remark";

    public static final String DEL_FLAG = "del_flag";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String IP = "ip";

}
