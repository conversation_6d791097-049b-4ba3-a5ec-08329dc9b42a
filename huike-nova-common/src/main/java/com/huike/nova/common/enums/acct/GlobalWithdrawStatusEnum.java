/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved
 */
package com.huike.nova.common.enums.acct;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version GlobalWithdrawStatusEnum.java, v 0.1 2024-07-18 2:56 下午 liubo
 */
public enum GlobalWithdrawStatusEnum {
    TRANSFER_INIT("转账初始化","TRANSFER_INIT"),
    TRANSFER_PROCESSING("转账中","TRANSFER_PROCESSING"),
    TRANSFER_SUCCESS_WAIT_WITHDRAW("转账成功待提现","TRANSFER_SUCCESS_WAIT_WITHDRAW"),
    TRANSFER_FAIL("转账失败","TRANSFER_FAIL"),
    WITHDRAW_PROCESSING("提现中","WITHDRAW_PROCESSING"),
    WITHDRAW_SUCCESS("提现成功","WITHDRA<PERSON>_SUCCESS"),
    WITHDRAW_FAIL("提现失败","WITHDRAW_FAIL"),
    ;

    private String name;
    private String value;

    GlobalWithdrawStatusEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public String getValue() {
        return value;
    }

    public static GlobalWithdrawStatusEnum getByValue(String value) {
        GlobalWithdrawStatusEnum[] valueList = GlobalWithdrawStatusEnum.values();
        for (GlobalWithdrawStatusEnum v : valueList) {
            if (StringUtils.equalsIgnoreCase(v.getValue(), value)) {
                return v;
            }
        }
        return null;
    }
}