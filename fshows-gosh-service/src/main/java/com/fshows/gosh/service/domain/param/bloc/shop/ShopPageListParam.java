package com.fshows.gosh.service.domain.param.bloc.shop;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年06月05日 18:09
 */
@Data
public class ShopPageListParam {

    /**
     * 搜索内容 门店名称or门店id
     */
    private String searchContent;

    /**
     * 广场id列表
     */
    private List<String> orgIdList;

    /**
     * 确认状态 -1全部  1未确认 2已确认
     */
    private Integer confirmStatus;

    /**
     * 类别id
     */
    private String categoryId;

    /**
     * 组织路径
     */
    private String orgPath;

    /**
     * 集团id
     */
    private String blocId;

    /**
     * 开户状态
     * 枚举值：-1全部、0未开户、1审核失败、2审核中、3审核通过
     */
    private Integer applyStatus;

    /**
     * 门店隐藏状态
     * 枚举值：-1 全部  0-正常  1-隐藏
     */
    private Integer hideStatus;
}
