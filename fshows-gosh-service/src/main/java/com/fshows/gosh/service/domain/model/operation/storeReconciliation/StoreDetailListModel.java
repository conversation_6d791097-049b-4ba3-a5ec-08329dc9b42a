package com.fshows.gosh.service.domain.model.operation.storeReconciliation;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class StoreDetailListModel {

    /**
     * 订单编号
     */
    private String orderId;
    /**
     * 售卖平台
     */
    private String channelType;
    /**
     * 手机号
     */
    private String phoneNumber;
    /**
     * 券码
     */
    private String couponCode;
    /**
     * 核销人（手机号）
     */
    private String thirdOperatorName;
    /**
     * 核销人昵称
     */
    private String thirdOperatorNickname;
    /**
     * 门店
     */
    private String verificationStoreId;
    /**
     * 门店名称
     */
    private String verificationStoreName;
    /**
     * 计算日期
     */
    private String settledDay;
    /**
     * 业务商品
     */
    private String businessProductId;
    /**
     * 商品id
     */
    private String productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品类型
     */
    private String productType;
    /**
     * 门店应得
     */
    private BigDecimal shopNetAmount;

    /**
     * 商圈卡商品类型0非商圈卡  1商圈母卡 2商圈子卡
     */
    private Integer orgProductType;

    /**
     * 订单号
     */
    private String itemOrderId;

}
