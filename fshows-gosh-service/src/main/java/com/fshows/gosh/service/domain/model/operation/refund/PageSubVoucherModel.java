package com.fshows.gosh.service.domain.model.operation.refund;

import lombok.Data;

import java.math.BigDecimal;

/**
 * PageSubVoucherModel
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/9
 */
@Data
public class PageSubVoucherModel {

    /**
     * 商家券id
     */
    private String productId;

    /**
     * 商家券名称
     */
    private String productName;

    /**
     * 商家券类目
     */
    private String categoryName;

    /**
     * 原价
     */
    private BigDecimal originalAmount;

    /**
     * 结算价
     */
    private BigDecimal settledAmount;

    /**
     * 卡状态 1-未激活 2-未使用 3-已使用 4-已失效
     */
    private Integer merchantCouponStatus;

    /**
     * 核销时间
     */
    private String verifyTime;

    /**
     * 核销门店
     */
    private String verifyStoreName;
}