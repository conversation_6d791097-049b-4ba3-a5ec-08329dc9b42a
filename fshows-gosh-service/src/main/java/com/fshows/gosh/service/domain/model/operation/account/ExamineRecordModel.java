/**
 * <AUTHOR>
 * @date 2024/12/26 18:27
 * @version 1.0 ExamineRecordModel
 */
package com.fshows.gosh.service.domain.model.operation.account;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 *
 *
 * <AUTHOR>
 * @version ExamineRecordModel.java, v 0.1 2024-12-26 18:27 tuyuwei
 */
@Data
public class ExamineRecordModel {

    /**
     * 操作名称
     */
    private String operationName;

    /**
     * 记录id
     */
    private String recordId;

    /**
     * 申请单id
     */
    private String applyId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}