/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.service.domain.model.acctbiz;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @version MerchantBalanceQueryModel.java, v 0.1 2024-08-30 10:38 AM ruanzy
 */
@Data
public class MerchantBalanceQueryModel {

    /**
     * 账户Id
     */
    @JSONField(name = "account_idnce")
    private String accountIdnce;

    /**
     * 总余额，单位分
     */
    @JSONField(name = "total_balance")
    private String totalBalance;

    /**
     * 可用余额，单位：分
     */
    @JSONField(name = "current_balance")
    private String currentBalance;

    /**
     * 提现在途金额，单位：分
     */
    @JSONField(name = "withdrawal_transit_balance")
    private String withdrawalTransitBalance;

    /**
     * 冻结金额，单位：分
     */
    @JSONField(name = "frozen_balance")
    private String frozenBalance;
}