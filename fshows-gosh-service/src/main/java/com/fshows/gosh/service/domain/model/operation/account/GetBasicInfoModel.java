/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.service.domain.model.operation.account;

import lombok.Data;

/**
 * <AUTHOR>
 * @version GetBasicInfoModel.java, v 0.1 2024-08-27 10:04 AM ruanzy
 */
@Data
public class GetBasicInfoModel {

    /**
     * 商铺id
     */
    private String shopId;

    /**
     * 商铺名称
     */
    private String shopName;

    /**
     * 结算状态：1-可结算；2-冻结结算
     */
    private int settleStatus;

    /**
     * 最后一次操作原因
     */
    private String freezeReason;

    /**
     * 集团名称
     */
    private String blocName;

    /**
     * 商家手机号
     */
    private String adminPhone;

    /**
     * 账户id
     */
    private String accountId;


    /**
     * 商户类型  1：企业 2：个体工商户  3：小微
     */
    private Integer merchantType;

    /**
     * 营业执照图片
     */
    private String licenseRegImageUrl;

    /**
     * legalCertFrontPic 法人证件人像面照片.
     */
    private String legalCertFrontPic;

    /**
     * legalCertBackPic 法人证件国徽面照片.
     */
    private String legalCertBackPic;

    /**
     * 执照名称
     */
    private String companyName;

    /**
     * 统一社会信用代码
     */
    private String licenseNo;

    /**
     * 营业执照起始日
     */
    private String licenseBeginDate;

    /**
     * 营业执照结束日
     */
    private String licenseEndDate;

    /**
     * 经营类目
     */
    private String mccCode;

    /**
     * 经营类目名称
     */
    private String mccName;

    /**
     * 经营地区
     */
    private String operatingArea;

    /**
     * 省
     */
    private String provinceCode;

    /**
     * 市
     */
    private String cityCode;

    /**
     * 区
     */
    private String areaCode;

    /**
     * 详细地址
     */
    private String bizAddress;

    /**
     * 证件类型  法人证件类型
     */
    private Integer legalCertType;

    /**
     * 姓名
     */
    private String legalName;

    /**
     * 证件号
     */
    private String legalCertNo;

    /**
     * 有效期
     */
    private String legalCertBeginDate;

    /**
     * 证件有效期结束日，长期填写：2099-12-31
     */
    private String legalCertEndDate;

    /**
     * 手机号
     */
    private String legalPhone;

    /**
     * 账户状态
     */
    private String accountStatus;

    /**
     * 结算账户类型：1-对私；2-对公
     */
    private Integer settleAccountType;

    /**
     * 结算人
     */
    private String settleAccountName;

    /**
     * 开户许可证
     */
    private String settleBankCardPic;

    /**
     * 银行卡号
     */
    private String settleAccountNo;

    /**
     * 所属银行
     */
    private String settleBankName;

    /**
     * 所属支行code
     */
    private String settleBankBranchCode;

    /**
     * 是否生效: 1-生效 2-失效
     */
    private Integer isEffect;

    /**
     * 是否有生效的结算信息: 1-有 2-无
     */
    private Integer hasEffectInfo;

}