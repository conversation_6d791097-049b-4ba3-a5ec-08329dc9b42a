package com.fshows.gosh.service.domain.param.red;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * ProductQueryParam
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RedProductQueryParam extends RedBaseParam {

    private static final long serialVersionUID = 6031724179388872609L;
    /**
     * 外部商品id
     */
    @JSONField(name = "out_product_id")
    private String outProductId;
}