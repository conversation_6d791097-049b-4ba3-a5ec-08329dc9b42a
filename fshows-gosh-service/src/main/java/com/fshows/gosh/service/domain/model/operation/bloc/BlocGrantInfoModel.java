package com.fshows.gosh.service.domain.model.operation.bloc;

import com.fshows.gosh.service.domain.model.bloc.grant.GrantInfoTreeModel;
import com.fshows.gosh.service.domain.model.mina.merchant.role.GrantModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BlocGrantInfoModel {

    /**
     * 集团后台绑定的grantId列表
     */
    private List<String> blocBindGrantIdList;

    /**
     * 小程序后台绑定的grantId列表
     */
    private List<String> blocMinaBindGrantIdList;

    /**
     * 集团后台grantId树
     */
    private List<GrantInfoTreeModel> blocGrantTree;

    /**
     * 商家版小程序grantId树
     */
    private List<GrantModel> blocMinaGrantList;

}
