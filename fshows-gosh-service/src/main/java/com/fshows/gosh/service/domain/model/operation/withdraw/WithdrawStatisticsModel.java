package com.fshows.gosh.service.domain.model.operation.withdraw;

import com.huike.nova.common.constant.CommonConstant;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class WithdrawStatisticsModel {

    /**
     * 提现金额
     */
    private BigDecimal withdrawAmount;

    /**
     * 提现笔数
     */
    private Integer withdrawCount;

    /**
     * 初始化数据
     *
     * @return
     */
    public static WithdrawStatisticsModel init() {
        WithdrawStatisticsModel model = new WithdrawStatisticsModel();
        model.setWithdrawAmount(BigDecimal.ZERO);
        model.setWithdrawCount(CommonConstant.ZERO);
        return model;
    }

}
