package com.fshows.gosh.service.domain.model.operation.productOrder;

import com.huike.nova.common.constant.StringPool;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GetOrderOrgInfoModel {

    /**
     * 组织名称列表
     */
    private List<String> orgNameList;

    /**
     * 组织全路径名称列表
     */
    private List<String> orgNamePathList;

    /**
     * 集团名称
     */
    private String blocName;

    public static GetOrderOrgInfoModel init() {
        GetOrderOrgInfoModel model = new GetOrderOrgInfoModel();
        model.setOrgNameList(new ArrayList<>());
        model.setOrgNamePathList(new ArrayList<>());
        model.setBlocName(StringPool.EMPTY);
        return model;
    }


}
