/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.service.domain.model.operation.bloc;

import lombok.Data;

/**
 * <AUTHOR>
 * @version GetBasicInfoParam.java, v 0.1 2024-08-27 10:04 AM ruanzy
 */
@Data
public class BlocQueryModel {

    /**
     * 集团id
     */
    private String blocId;

    /**
     * 集团名称
     */
    private String blocName;

    /**
     * 集团管理员账号
     */
    private String accountName;

    /**
     * 创建结束时间
     */
    private String createTime;

    /**
     * 跳转Url
     */
    private String loginUrl;

    /**
     * 顶级组织 id
     */
    private String orgId;

    /**
     * 集团状态  1 正常 2 禁用
     */
    private Integer blocStatus;

    /**
     * appId
     */
    private String appId;

    /**
     * 绑定集团账号的accountId
     */
    private String bindAccountId;

    /**
     * 绑定集团账号的account
     */
    private String bindAccount;

}