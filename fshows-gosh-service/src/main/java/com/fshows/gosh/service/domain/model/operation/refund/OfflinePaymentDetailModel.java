package com.fshows.gosh.service.domain.model.operation.refund;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * OfflinePaymentDetailModel
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/9
 */
@Data
public class OfflinePaymentDetailModel {
    /**
     * 发布平台
     */
    private String channelType;

    /**
     * 打款id
     */
    private String paymentId;

    /**
     * 订单编号
     */
    private String orderId;


    /**
     * 用户实付
     */
    private BigDecimal cashFee;

    /**
     * 退款数量
     */
    private Integer refundCount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 剩余金额
     */
    private BigDecimal remainAmount;

    /**
     * 顾客手机号
     */
    private String customerPhone;

    /**
     * 支付宝账号
     */
    private String alipayAccount;

    /**
     * 支付宝姓名
     */
    private String alipayName;


    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 提交人
     */
    private String submit;

    /**
     * 提交时间
     */
    private String submitTime;

    /**
     * 打款人
     */
    private String paymentName;

    /**
     * 打款时间
     */
    private String paymentTime;

    /**
     * 打款失败时间
     */
    private String failTime;

    /**
     * 打款失败原因
     */
    private String failReason;

    /**
     * 打款状态 1-未打款; 2-已打款; 3-不打款
     */
    private Integer paymentStatus;

    /**
     * 退款截图说明
     */
    private List<String> refundImgList;

    /**
     * 备注说明
     */
    private String remark;


    /**
     * 母券列表
     */
    private List<Coupon> couponList;

    /**
     * 母券列表
     */
    @Data
    public static class Coupon {

        /**
         * 集团id
         */
        private String appId;

        /**
         * 集团名称
         */
        private String orgName;

        /**
         * 核销广场Id
         */
        private String poiId;

        /**
         * 核销广场名称
         */
        private String poiName;

        /**
         * 商圈卡id
         */
        private String productId;

        /**
         * 商圈卡名称
         */
        private String productName;

        /**
         * 商圈卡券码
         */
        private String couponCode;

        /**
         * 售卖金额
         */
        private BigDecimal couponSaleAmount;

        /**
         * 用户实付
         */
        private BigDecimal cash;

        /**
         * 订单核销状态1-未核销；2-已核销（有一张券撤销核销都是未核销）
         */
        private Integer couponStatus;

        /**
         * 核销时间
         */
        private String verifyTime;

        /**
         * 顾客手机号
         */
        private String customerPhone;

        /**
         * 商家券状态:1-已使用;2-未使用
         */
        private Integer merchantCouponStatus;

        /**
         * 有效期
         */
        private String validPeriod;
    }
}