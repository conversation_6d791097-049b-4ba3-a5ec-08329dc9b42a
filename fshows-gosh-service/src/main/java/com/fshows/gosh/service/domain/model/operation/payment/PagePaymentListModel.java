/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.service.domain.model.operation.payment;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version PageShopAccountListModel.java, v 0.1 2024-08-26 3:09 PM ruanzy
 */
@Data
public class PagePaymentListModel {
    /**
     * 结算单号
     */
    private String serialNo;

    /**
     * 结算日期（yyyyMMdd）
     */
    private String settleDate;

    /**
     * 集团应用Id
     */
    private String appId;

    /**
     * 集团ID
     */
    private String blocId;

    /**
     * 集团名称
     */
    private String blocName;

    /**
     * 预估入金
     */
    private BigDecimal incomeAmount;

    /**
     * 总结算单金额 = 普通券+子券金额，单位：元
     */
    private BigDecimal settleFormAmount;

    /**
     * 结算单状态 0=初始化 1=打款确认 2=出款中 3=出款成功
     */
    private Integer settleFormStatus;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 来源：BATCH-脚本自动生成   MANUAL-人工手动插入
     */
    private String source;

    /**
     * 平台类型 TIKTOK-抖音 ALIPAY-支付宝 MEITUAN-美团
     */
    private String platformType;

    /**
     * 普通券（非商圈卡）金额，单位：元
     */
    private BigDecimal nonBusinessAmount;

    /**
     * 商家券（子券）金额，单位：元
     */
    private BigDecimal businessChildCardAmount;

    /**
     * 商圈卡（母券）金额，单位：元（和总结算金额没有关系）
     */
    private BigDecimal businessMotherCardAmount;

    /**
     * 转账状态 0-待发起 1-转账已确认 2-无需转账 3-转账成功 4-转账失败
     */
    private Integer transferStatus;

    /**
     * 合并转账单号
     */
    private String mergeTransferNo;

    /**
     * 合并转账状态 0-待发起 1-转账已确认 2-无需转账 3-转账成功 4-转账失败
     */
    private Integer mergeTransferStatus;

    /**
     * 差异金额(查询差异结算金额，ailike_service_provider_coupon_verify_diff)
     */
    private BigDecimal diffAmount;

    /**
     * 差异佣金(查询佣金差异统计，ailike_service_provider_coupon_verify)
     */
    private BigDecimal diffCommission;

    /**
     * 普通券正向差异金额(我们这边是已退款的状态，但是抖音把这笔券打给我们了，导致出款得金额小于入金)
     */
    private BigDecimal incomeDiffAmount;

    /**
     * 普通券逆向差异金额( 跨日的券被退款了，然后从我们当日的结算款中扣除了，导致出款得金额大于入金)
     */
    private BigDecimal deductDiffAmount;

    /**
     * 母券正向差异金额
     */
    private BigDecimal motherIncomeDiffAmount;

    /**
     * 母券逆向差异金额
     */
    private BigDecimal motherDeductDiffAmount;

    /**
     * 外部商品出款金额
     */
    private BigDecimal outProductSettleAmount;

    /**
     * 美团类型  1 点评（老美团）2 美团到餐 3 美团到综
     */
    private Integer meiTuanType;

    /**
     * 权益包子券金额
     */
    private BigDecimal pkgChildAmount;
}