package com.fshows.gosh.service.domain.model.meituan;

import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.CouponVerifyResultsModel;
import com.huike.nova.dao.entity.AilikeBGoodsDetailDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.annotation.Nonnull;
import java.util.Date;

/**
 * 美团核券结果，扩展类
 *
 * <AUTHOR> (<EMAIL>)
 * @version MeiTuanCouponVerifyResultsModel.java, v1.0 06/06/2024 13:00 John Exp$
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MeiTuanCouponVerifyResultsModel extends CouponVerifyResultsModel {
    /**
     * 商品单详情
     */
    @Nonnull
    private transient AilikeBGoodsDetailDO goodsDetailDO;

    /**
     * 核销时间
     */
    private Date verifyTime;

    /**
     * 核券时间：字符串格式
     */
    private String verifyTimeStr;

    public String getItemOrderId() {
        return goodsDetailDO.getItemOrderId();
    }
}
