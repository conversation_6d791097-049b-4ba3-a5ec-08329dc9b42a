package com.fshows.gosh.service.domain.param.red;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RedMinaOrderQueryParam extends RedBaseParam{

    private static final long serialVersionUID = -1277208761999088209L;
    /**
     * 外部订单id
     */
    @JSONField(name = "out_order_id")
    private String outOrderId;

    /**
     * 用户 openId
     */
    @JSONField(name = "open_id")
    private String openId;

    /**
     * 1: 主单（预售券/团购券） 2：预约单（预售券/日历订单
     */
    @JSONField(name = "order_type")
    private Integer orderType;
}
