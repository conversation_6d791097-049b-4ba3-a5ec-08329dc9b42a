/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.service.domain.model.operation.account;

import lombok.Data;

/**
 * <AUTHOR>
 * @version PageShopAccountListModel.java, v 0.1 2024-08-26 3:09 PM ruanzy
 */
@Data
public class PageShopAccountListModel {

    /**
     * 商铺id
     */
    private String shopId;

    /**
     * 商铺名称
     */
    private String shopName;

    /**
     * 集团名称
     */
    private String blocName;

    /**
     * 管理员手机号
     */
    private String adminPhone;

    /**
     * 账户状态 0-未开户 1-生效 2-失效 3-开户中
     */
    private Integer  accountStatus;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 修改时间
     */
    private String updateTime;

    /**
     * 付呗侧account_id
     */
    private String accountId;

    /**
     * 结算状态：1-可结算；2-冻结结算
     */
    private int settleStatus;

    /**
     * 冻结、解冻原因（最后一次）
     */
    private String freezeReason;

    /**
     * 抖音场内户的POI
     */
    private String mallPoi;
}