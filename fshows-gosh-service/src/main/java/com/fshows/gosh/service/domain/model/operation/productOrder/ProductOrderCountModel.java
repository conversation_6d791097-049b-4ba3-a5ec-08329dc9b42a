package com.fshows.gosh.service.domain.model.operation.productOrder;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProductOrderCountModel {

    /**
     * 全部订单数
     */
    private Integer allOrderCount;

    /**
     * 未核销订单数
     */
    private Integer notVerifyOrderCount;

    /**
     * 已核销订单数
     */
    private Integer verifyOrderCount;

    /**
     * 已退款订单数
     */
    private Integer refundOrderCount;

    public static ProductOrderCountModel init() {
        ProductOrderCountModel result = new ProductOrderCountModel();
        result.setAllOrderCount(0);
        result.setNotVerifyOrderCount(0);
        result.setVerifyOrderCount(0);
        result.setRefundOrderCount(0);
        return result;
    }

}
