/**
 * <AUTHOR>
 * @date 2024/12/26 18:56
 * @version 1.0 ExamineRecredDetailModel
 */
package com.fshows.gosh.service.domain.model.operation.account;

import lombok.Data;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @version ExamineRecordDetailModel.java, v 0.1 2024-12-26 18:56 tuyuwei
 */
@Data
public class ExamineRecordDetailModel {

    /**
     * 商铺id
     */
    private String shopId;

    /**
     * 商铺名称
     */
    private String shopName;

    /**
     * 集团名称
     */
    private String blocName;

    /**
     * 商家手机号
     */
    private String adminPhone;

    /**
     * 账户id
     */
    private String accountId;


    /**
     * 商户类型  1：企业 2：个体工商户  3：小微
     */
    private Integer merchantType;

    /**
     * 营业执照图片
     */
    private String licenseRegImageUrl;

    /**
     * legalCertFrontPic 法人证件人像面照片.
     */
    private String legalCertFrontPic;

    /**
     * legalCertBackPic 法人证件国徽面照片.
     */
    private String legalCertBackPic;

    /**
     * 执照名称
     */
    private String companyName;

    /**
     * 统一社会信用代码
     */
    private String licenseNo;

    /**
     * 营业执照起始日
     */
    private String licenseBeginDate;

    /**
     * 营业执照结束日
     */
    private String licenseEndDate;

    /**
     * 经营类目
     */
    private String mccCode;

    /**
     * 经营类目名称
     */
    private String mccName;

    /**
     * 经营地区
     */
    private String operatingArea;

    /**
     * 省
     */
    private String provinceCode;

    /**
     * 市
     */
    private String cityCode;

    /**
     * 区
     */
    private String areaCode;

    /**
     * 详细地址
     */
    private String bizAddress;

    /**
     * 证件类型  法人证件类型
     */
    private Integer legalCertType;

    /**
     * 姓名
     */
    private String legalName;

    /**
     * 证件号
     */
    private String legalCertNo;

    /**
     * 有效期
     */
    private String legalCertBeginDate;

    /**
     * 证件有效期结束日，长期填写：2099-12-31
     */
    private String legalCertEndDate;

    /**
     * 手机号
     */
    private String legalPhone;

    /**
     * 账户状态
     */
    private String accountStatus;


    /**
     * 结算账户类型：1-对私；2-对公
     */
    private Integer settleAccountType;

    /**
     * 结算人
     */
    private String settleAccountName;

    /**
     * 开户许可证
     */
    private String settleBankCardPic;

    /**
     * 银行卡号
     */
    private String settleAccountNo;

    /**
     * 所属银行
     */
    private String settleBankName;

    /**
     * 所属支行code
     */
    private String settleBankBranchCode;

    /**
     * 驳回原因
     */
    private String rejectedReason;

    /**
     * 截图说明
     */
    private List<String> rejectedDescribePicture;
}