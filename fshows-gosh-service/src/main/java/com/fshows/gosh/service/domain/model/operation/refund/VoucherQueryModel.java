package com.fshows.gosh.service.domain.model.operation.refund;

import lombok.Data;

import java.math.BigDecimal;

/**
 * VoucherQueryModel
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/5/9
 */
@Data
public class VoucherQueryModel {

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 订单itemId
     */
    private String itemOrderId;

    /**
     * 商圈卡券码
     */
    private String couponCode;

    /**
     * 集团id
     */
    private String appId;

    /**
     * 平台
     */
    private String channelType;

    /**
     * 集团名称
     */
    private String orgName;

    /**
     * 核销广场Id
     */
    private String poiId;

    /**
     * 核销广场名称
     */
    private String poiName;

    /**
     * 商圈卡id
     */
    private String productId;

    /**
     * 商圈卡名称
     */
    private String productName;

    /**
     * 售卖金额
     */
    private BigDecimal couponSaleAmount;

    /**
     * 用户实付
     */
    private BigDecimal cash;

    /**
     * 订单核销状态1-未核销；2-已核销（有一张券撤销核销都是未核销） 3-已退款
     */
    private Integer couponStatus;

    /**
     * 核销时间
     */
    private String verifyTime;

    /**
     * 顾客电话
     */
    private String customerPhone;

    /**
     * 商家券状态:1-已使用;2-未使用
     */
    private Integer merchantCouponStatus;

    /**
     * 子券结算金额
     */
    private BigDecimal subSettledAmount;

    /**
     * 可退金额
     */
    private BigDecimal refundableAmount;

    /**
     * 退款状态:0-未申请 1-待处理 ；2-已处理 3- 已驳回
     */
    private Integer refundStatus;

    /**
     * 是否可申请线下退款
     */
    private Boolean canApplyOfflineRefund;

    /**
     * 是否可申请线上退款
     */
    private Boolean canApplyOnlineRefund;

    /**
     * 是否可退款
     */
    private Boolean canRefund;

    /**
     * 有效期
     */
    private String validPeriod;
}