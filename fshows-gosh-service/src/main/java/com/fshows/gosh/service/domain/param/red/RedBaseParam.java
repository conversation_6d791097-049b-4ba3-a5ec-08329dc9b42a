package com.fshows.gosh.service.domain.param.red;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huike.nova.dao.entity.AilikeTiktokServiceProviderChannelConfigDO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * RedProductSaveParam
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/11
 */
@Data
public class RedBaseParam implements Serializable {
    private static final long serialVersionUID = -4762859718304240357L;
    /**
     * appId
     */
    @JSONField(serialize = false)
    private String appId;

    @JSONField(serialize = false)
    private AilikeTiktokServiceProviderChannelConfigDO configDO;
}