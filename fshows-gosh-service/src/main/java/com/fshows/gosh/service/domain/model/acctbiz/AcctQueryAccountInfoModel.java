/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.service.domain.model.acctbiz;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @version AcctQueryAccountInfoModel.java, v 0.1 2024-08-27 2:07 PM ruanzy
 */
@Data
public class AcctQueryAccountInfoModel {

    /**
     * 基本信息
     */
    @JSONField(name = "base_info")
    private AccountBaseInfo baseInfo;

    /**
     * 联系人信息
     */
    @JSONField(name = "contact_info")
    private AccountContactInfo contactInfo;

    /**
     * 商户地址信息
     */
    @JSONField(name = "address_info")
    private AccountAddressInfo addressInfo;

    /**
     * 法人信息
     */
    @JSONField(name = "legal_info")
    private AccountLegalInfo legalInfo;

    /**
     * 营业资质信息
     */
    @JSONField(name = "license_info")
    private AccountLicenseInfo licenseInfo;

    /**
     * 结算信息
     */
    @JSONField(name = "account_info")
    private AccountInfo accountInfo;


    /**
     * 基本信息
     */
    @Data
    public static class AccountBaseInfo {

        /**
         * 外部商编
         */
        @JSONField(name = "out_merchant_no")
        private String outMerchantNo;

        /**
         * 账户ID
         */
        @JSONField(name = "account_id")
        private String accountId;

        /**
         * 商户类型
         * 1：企业
         * 2：个体工商户
         * 3：小微
         */
        @JSONField(name = "merchant_type")
        private Integer merchantType;

        /**
         * 企业类型
         * 1：企业商户
         * 2：个体工商户
         * 3：政府机关事业单位
         * 4：其他组织
         */
        @JSONField(name = "enterprise_type")
        private Integer enterpriseType;

        /**
         * 商户全称
         */
        @JSONField(name = "merchant_name")
        private String merchantName;

        /**
         * 商户简称
         */
        @JSONField(name = "short_name")
        private String shortName;

        /**
         * 类目编码
         */
        @JSONField(name = "mcc_code")
        private String mccCode;
    }

    /**
     * 联系人信息
     */
    @Data
    public static class AccountContactInfo {

        /**
         * 联系人姓名
         */
        @JSONField(name = "contact_name")
        private String contactName;

        /**
         * 联系人邮箱
         */
        @JSONField(name = "contact_email")
        private String contactEmail;
    }

    /**
     * 商户地址信息
     */
    @Data
    public static class AccountAddressInfo {

        /**
         * 省
         */
        @JSONField(name = "biz_province_code")
        private String bizProvinceCode;

        /**
         * 市
         */
        @JSONField(name = "biz_city_code")
        private String bizCityCode;

        /**
         * 区/县
         */
        @JSONField(name = "biz_area_code")
        private String bizAreaCode;

        /**
         * 商户详细地址
         */
        @JSONField(name = "biz_address")
        private String bizAddress;
    }

    /**
     * 法人信息
     */
    @Data
    public static class AccountLegalInfo {

        /**
         * 法人姓名
         */
        @JSONField(name = "legal_name")
        private String legalName;

        /**
         * 法人手机号码
         */
        @JSONField(name = "legal_phone")
        private String legalPhone;

        /**
         * 法人证件类型
         * 1：身份证
         * 2:  护照（国内护照）
         */
        @JSONField(name = "legal_cert_type")
        private Integer legalCertType;

        /**
         * 法人证件号码
         */
        @JSONField(name = "legal_cert_no")
        private String legalCertNo;

        /**
         * 证件有效期起始日
         */
        @JSONField(name = "legal_cert_begin_date")
        private String legalCertBeginDate;

        /**
         * 证件有效期结束日
         */
        @JSONField(name = "legal_cert_end_date")
        private String legalCertEndDate;
    }

    /**
     * 营业资质信息
     */
    @Data
    public static class AccountLicenseInfo {

        /**
         * 营业执照注册号码
         */
        @JSONField(name = "license_no")
        private String licenseNo;

        /**
         * 营业执照名称
         */
        @JSONField(name = "company_name")
        private String companyName;

        /**
         * 营业执照地址
         */
        @JSONField(name = "license_reg_address")
        private String licenseRegAddress;

        /**
         * 营业执照起始日
         */
        @JSONField(name = "license_begin_date")
        private String licenseBeginDate;

        /**
         * 营业执照结束日
         */
        @JSONField(name = "license_end_date")
        private String licenseEndDate;
    }

    /**
     * 结算信息
     */
    @Data
    public static class AccountInfo {

        /**
         * 银行账号类型
         * <p>
         * 1 法人对私卡
         * 2 企业对公户
         */
        @JSONField(name = "settle_account_type")
        private Integer settleAccountType;

        /**
         * 银行总行名称
         */
        @JSONField(name = "settle_bank_name")
        private String settleBankName;

        /**
         * 结算银行卡号
         */
        @JSONField(name = "settle_account_no")
        private String settleAccountNo;

        /**
         * 开户支行联行号
         */
        @JSONField(name = "settle_bank_branch_code")
        private String settleBankBranchCode;
    }
}

