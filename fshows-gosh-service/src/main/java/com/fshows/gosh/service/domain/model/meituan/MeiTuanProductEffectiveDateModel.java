/*
 *
 *  * Fshows Technology
 *  * Copyright (C) 2022-2024 All Rights Reserved.
 *
 */

package com.fshows.gosh.service.domain.model.meituan;

import lombok.Data;

/**
 * 美团团单团购有效期
 *
 * <AUTHOR> (<EMAIL>)
 * @version MeiTuanProductEffectiveDateModel.java, v1.0 2024/6/12 14:02 John Exp$
 */
@Data
public class MeiTuanProductEffectiveDateModel {
    /**
     * 有效期类型<br/>
     * 0 - 绝对时间，begin_date到end_date内有效<br/>
     * 1 - 相对时间，下单后number天内有效
     */
    private Integer type;

    /**
     * 有效期开始时间 yyyy-MM-dd
     */
    private String beginDate;

    /**
     * 有效期结束时间 yyyy-MM-dd
     */
    private String endDate;

    /**
     * 天数
     */
    private Integer number;
}
