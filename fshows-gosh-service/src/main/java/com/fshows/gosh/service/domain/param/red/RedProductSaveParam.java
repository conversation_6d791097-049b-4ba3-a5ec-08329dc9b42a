package com.fshows.gosh.service.domain.param.red;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * RedProductSaveParam
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RedProductSaveParam extends RedBaseParam {
    private static final long serialVersionUID = -9195584616455840808L;
    /**
     * 外部商品ID，不超过64字符
     */
    @JSONField(name = "out_product_id")
    private String outProductId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 短标题，限制64个字
     */
    @JSONField(name = "short_title")
    private String shortTitle;

    /**
     * 商品描述，限制1000个字符
     */
    private String desc;

    /**
     * 商品小程序打开路径，/开头
     */
    private String path;

    /**
     * 商品头图，单张; 仅支持jpg、jpeg、png格式
     */
    @JSONField(name = "top_image")
    private String topImage;

    /**
     * 类目id，通过类目接口获取，最后一级的id
     */
    @JSONField(name = "category_id")
    private String categoryId;

    /**
     * 商品创建时间，时间戳精确到秒
     */
    @JSONField(name = "biz_create_time")
    private long bizCreateTime;

    /**
     * 商品修改时间，时间戳精确到秒
     */
    @JSONField(name = "biz_update_time")
    private long bizUpdateTime;

    /**
     * 套餐内容明细，仅在特定类目时为必填
     */
    @JSONField(name = "package_detail")
    private PackageDetail packageDetail;

    /**
     * 子商品集合
     */
    private List<Sku> skus;

    /**
     * 自定义字段
     */
    private Map<String, String> ext;

    /**
     * 商品类型，1-团购，2-酒旅预售券，3-日历商品
     */
    @JSONField(name = "product_type")
    private Integer productType;

    /**
     * 结算方式，1-总店结算，2-门店结算，3-区域结算
     */
    @JSONField(name = "settle_type")
    private Integer settleType;

    /**
     * POI_ID列表，部分类目必传
     */
    @JSONField(name = "poi_id_list")
    private List<String> poiIdList;

    @Data
    public static class PackageDetail {
        private List<PackageItem> items;
    }

    @Data
    public static class PackageItem {
        /**
         * 名称
         */
        private String name;
        /**
         * 数量
         */
        private Integer count;
        /**
         * 单位
         */
        private String unit;
    }

    @Data
    public static class Sku {
        /**
         * 外部skuId
         */
        @JSONField(name = "out_sku_id")
        private String outSkuId;

        /**
         * SKU名称
         */
        private String name;

        /**
         * sku商品图片，单张; 仅支持jpg、jpeg、png格式
         */
        @JSONField(name = "sku_image")
        private String skuImage;

        /**
         * 商品原始价格，划线价格，单位（分）
         */
        @JSONField(name = "origin_price")
        private Integer originPrice;

        /**
         * 商品售卖价格，单位（分）
         */
        @JSONField(name = "sale_price")
        private Integer salePrice;

        /**
         * sku状态：1：上架，0：下架，2：系统下架，如果不填，默认：1
         */
        private Integer status;
    }
}