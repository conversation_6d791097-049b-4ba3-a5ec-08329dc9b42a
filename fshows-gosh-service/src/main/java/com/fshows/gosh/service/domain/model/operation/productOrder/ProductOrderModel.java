package com.fshows.gosh.service.domain.model.operation.productOrder;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ProductOrderModel {

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 广场名称
     */
    private String squareName;

    /**
     * 订单编号（展示）
     */
    private String outOrderSn;

    /**
     * 商品单号（查询详情使用）
     */
    private String itemOrderId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 售卖金额（原价）
     */
    private BigDecimal originPrice;

    /**
     * 优惠金额
     */
    private BigDecimal discountPrice;

    /**
     * 订单实收
     */
    private BigDecimal price;

    /**
     * 订单状态
     * 1 未核销
     * 2 已核销
     * 3 已退款
     */
    private Integer orderStatus;

    /**
     * 订单状态描述
     */
    private String orderStatusDesc;

    /**
     * 支付类型
     */
    private Integer payChannel;

    /**
     * 支付类型描述
     */
    private String payChannelDesc;

    /**
     * 顾客id
     */
    private String customerId;

    /**
     * 记录时间
     * 全部/未核销tab栏表示支付时间
     * 已核销tab栏表示核销时间
     * 已退款tab栏表示退款时间
     */
    private String recordTime;

    /**
     * 顾客手机号
     */
    private String purchasePhone;

    /**
     * 退款来源
     */
    private Integer refundSource;

    /**
     * 退款来源说明
     */
    private String refundSourceDesc;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 所属组织列表
     */
    private List<String> orgNameList;

    /**
     * 集团名称
     */
    private String blocName;

    /**
     * 带货角色
     */
    private String goodsRole;

    /**
     * 成交渠道
     */
    private String tradeChannel;

    /**
     * 达人昵称
     */
    private String starNickname;

    /**
     * 达人code
     */
    private String starTiktokCode;

    /**
     * 订单归属人昵称
     */
    private String belongNickname;

    /**
     * 核销时间（全部中显示）
     */
    private String verifyTime;

    /**
     * 退款时间（全部中显示）
     */
    private String refundTime;

    /**
     * 库存限制类型：1-按券限制；2-按照POI限制
     */
    private Integer stockLimitType;

    /**
     * 内容地址
     */
    private String videoUrl;

    /**
     * 发布平台
     */
    private String channelType;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 发布平台名称
     */
    private String channelTypeName;

    /**
     * 商品类型名称
     */
    private String productTypeName;
    /**
     * 核销门店
     */
    private String verificationStore;
    /**
     * 商品创建时间
     */
    private String productCreateTime;

}
