package com.fshows.gosh.service.domain.model.operation.storeReconciliation;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class PageStoreListModel {

    /**
     * 门店名称
     */
    private String shopId;
    /**
     * 门店名称
     */
    private String shopName;
    /**
     * 售卖平台
     */
    private String channelType;
    /**
     * 广场 id
     */
    private String orgId;
    /**
     * 广场名称
     */
    private String orgName;
    /**
     * 核销券数
     */
    private Integer verifyCoupons;
    /**
     * 门店应得
     */
    private BigDecimal shopNetAmount;

    /**
     * 集团名称
     */
    private String blocName;

}
