/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.service.domain.model.acctbiz;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @version AcctWalletTransferQueryModel.java, v 0.1 2024-07-22 10:49 zhaoxumin
 */
@Data
public class AcctWalletTransferQueryModel {

    /**
     * 外部转账单号
     */
    @JSONField(name = "out_transfer_no")
    private String outTransferNo;

    /**
     * 付呗转账单号
     */
    @JSONField(name = "transfer_no")
    private String transferNo;

    /**
     * 出账钱包id
     */
    @JSONField(name = "out_wallet_id")
    private String outWalletId;

    /**
     * 入账钱包id
     */
    @JSONField(name = "in_wallet_id")
    private String inWalletId;

    /**
     * 转账金额. 分
     */
    @JSONField(name = "amout")
    private Long amount;

    /**
     * 转账状态
     */
    @JSONField(name = "transfer_status")
    private String transferStatus;

    /**
     * 转账完成时间. yyyy-MM-dd HH:mm:ss
     */
    @JSONField(name = "finish_time")
    private String finishTime;

    /**
     * 附言
     */
    @JSONField(name = "remark")
    private String remark;

    /**
     * 转账失败原因
     */
    @JSONField(name = "reason")
    private String reason;
}