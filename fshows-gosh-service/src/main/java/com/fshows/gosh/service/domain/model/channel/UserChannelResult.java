/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.fshows.gosh.service.domain.model.channel;

import com.huike.nova.common.enums.ChannelTypeEnum;
import com.huike.nova.common.enums.ChannelUserTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @version UserChannelResult.java, v 0.1 2022-11-10 15:25 wangyi
 */
@Data
public class UserChannelResult {

    /**
     * 	用户id，根据user_type进行区分
     * 	SYSTEM-空字符串
     * 	OEM-ailike_oem.oem_id
     * 	MCN-ailike_mcn.mcn_id
     * 	AGENT-ailike_agent_mcn.agent_mcn_id
     * 	MERCHANT_AGENT-ailike_merchant_agent.merchant_agent_id
     */
    private String userId;

    /**
     * 用户类型
     */
    private ChannelUserTypeEnum channelUserTypeEnum;

    /**
     * 渠道类型
     */
    private ChannelTypeEnum channelTypeEnum;

    /**
     * 渠道唯一标识
     */
    private String channelCode;
}