package com.fshows.gosh.service.domain.param.red;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * RedProductSaveParam
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RedDeleteProductParam extends RedBaseParam {

    private static final long serialVersionUID = 2500733080701419247L;
    /**
     * 外部sku商品id集合,sku 商品全部下架，对应的 product 自动下架
     */
    @JSONField(name = "out_sku_ids")
    private List<String> outSkuIds;
}