package com.fshows.gosh.service.domain.param.red;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RedMinaVoucherVerifyParam extends RedBaseParam{
    /**
     * 外部订单id
     */
    @JSONField(name = "out_order_id")
    private String outOrderId;

    /**
     * 门店id，订单的商品是分账到门店的商品，必传
     */
    @JSONField(name = "poi_id")
    private String poiId;

    /**
     * 券信息，该接口券金额忽略
     */
    @JSONField(name = "voucher_infos")
    private List<VoucherInfo> voucherInfos;

    /**
     * 券信息
     */
    @Data
    public static class VoucherInfo {
        /**
         * 凭证code
         */
        @JSONField(name = "voucher_code")
        private String voucherCode;
    }
}
