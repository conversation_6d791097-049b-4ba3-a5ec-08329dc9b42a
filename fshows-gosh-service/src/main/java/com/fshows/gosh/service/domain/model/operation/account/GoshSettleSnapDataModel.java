/**
 * <AUTHOR>
 * @date 2025/1/3 14:47
 * @version 1.0 GoshSettleRowDataModel
 */
package com.fshows.gosh.service.domain.model.operation.account;

import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 * @version GoshSettleRowDataModel.java, v 0.1 2025-01-03 14:47 tuyuwei
 */
@Data
public class GoshSettleSnapDataModel {


    /**
     * 结算账户类型：1-对私；2-对公
     */
    private Integer settleAccountType;

    /**
     * 结算人
     */
    private String settleAccountName;

    /**
     * 开户许可证
     */
    private String settleBankCardPic;

    /**
     * 银行卡号
     */
    private String settleAccountNo;

    /**
     * 所属银行
     */
    private String settleBankName;

    /**
     * 所属支行code
     */
    private String settleBankBranchCode;

    /**
     * 银行号
     */
    private String settleBankCode;
}