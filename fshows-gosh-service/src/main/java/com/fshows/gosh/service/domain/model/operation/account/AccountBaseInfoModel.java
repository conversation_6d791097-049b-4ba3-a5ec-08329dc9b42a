/**
 * <AUTHOR>
 * @date 2025/1/7 11:04
 * @version 1.0 AccountBaseInfoModel
 */
package com.fshows.gosh.service.domain.model.operation.account;

import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 * @version AccountBaseInfoModel.java, v 0.1 2025-01-07 11:04 tuyuwei
 */
@Data
public class AccountBaseInfoModel {


    private AccountInfo accountInfo;

    private AddressInfo addressInfo;

    private BaseInfo baseInfo;

    private ContactInfo contactInfo;

    private LegalInfo legalInfo;

    private LicenseInfo licenseInfo;


    @Data
    public static class AccountInfo {

        /**
         * 结算账户名称
         */
        private String settleAccountName;

        /**
         * 结算账户号码
         */
        private String settleAccountNo;

        /**
         * 结算账户类型：1-对私；2-对公
         */
        private Integer settleAccountType;

        /**
         * 结算银行卡照片
         */
        private String settleBankCardPic;

        /**
         * 银行code
         */
        private String settleBankCode;

        /**
         * 银行总行名称
         */
        private String settleBankName;

        /**
         * 开户支行联行号
         * 银行账号为企业对公户时开户行行号必
         * 填
         */
        private String settleBankBranchCode;
    }

    @Data
    public static class AddressInfo {

        /**
         * 商户详细地址
         */
        private String bizAddress;

        /**
         * 区级区域代码
         */
        private String bizAreaCode;

        /**
         * 市级区域编号
         */
        private String bizCityCode;

        /**
         * 省级区域编号
         */
        private String bizProvinceCode;
    }

    @Data
    public static class BaseInfo {

        /**
         * 外部商编
         */
        private String outMerchantNo;

        /**
         * 账户编号
         */
        private String accountId;

        /**
         * 企业类型 1-企业商户 2-个体工商户 3-政府机关事业单位 4-其他组织
         */
        private Integer enterpriseType;

        /**
         * 类目编码
         */
        private String mccCode;

        /**
         * 商户名称
         */
        private String merchantName;

        /**
         * 商户类型 1-企业  2-个体工商户 3-小微
         */
        private Integer merchantType;

        /**
         * 商户简称
         */
        private String shortName;

        /**
         * 组织ID
         */
        private String orgId;

        private String accountType;
    }

    @Data
    public static class ContactInfo {
        /**
         * 联系人姓名
         */
        private String contactName;

        /**
         * 联系人邮箱
         */
        private String contactEmail;
    }

    @Data
    public static class LegalInfo {
        /**
         * 法人证件类型 1-身份证 2-护照
         */
        private String legalCertBackPic;

        /**
         * 法人证件人像面照片
         */
        private String legalCertFrontPic;
        /**
         * 法人证件有效期起始日期：yyyy-MM-dd
         */
        private String legalCertBeginDate;
        /**
         * 法人证件有效期结束日期：yyyy-MM-dd
         */
        private String legalCertEndDate;
        /**
         * 法人证件号码
         */
        private String legalCertNo;
        /**
         * 法人证件类型 1-身份证 2-护照
         */
        private Integer legalCertType;
        /**
         * 法人姓名
         */
        private String legalName;
        /**
         * 法人手机号码
         */
        private String legalPhone;
    }

    @Data
    public static class LicenseInfo {
        /**
         * 营业执照公司名称
         */
        private String companyName;
        /**
         * 商户营业执照有效期-起始日期（yyyy-MM-dd）
         */
        private String licenseBeginDate;
        /**
         * 商户营业执照有效期-结束日期（yyyy-MM-dd）
         */
        private String licenseEndDate;
        /**
         * 营业执照编号
         */
        private String licenseNo;
        /**
         * 营业执照照片
         */
        private String licensePic;
        /**
         * 营业执照注册地址
         */
        private String licenseRegAddress;
    }
}