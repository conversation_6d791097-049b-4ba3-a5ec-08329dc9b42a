package com.fshows.gosh.service.domain.model.operation.bloc;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BlocDetailModel {

    /* ---------------------------------基本信息------------------------------  */

    /**
     * 集团id
     */
    private String blocId;

    /**
     * 集团名称
     */
    private String blocName;

    /**
     * 集团应用appId
     */
    private String ailikeAppId;

    /**
     * 集团主账号
     */
    private String accountName;

    /**
     * 集团联系人
     */
    private String accountContact;

    /**
     * 集团电话
     */
    private String blocPhoneNumber;


    /* ---------------------------------付呗信息------------------------------  */

    /**
     * 付呗集团ID
     */
    private String fsBlocId;

    /**
     * 付呗代理商ID
     */
    private String fsAgentId;

    /**
     * 付呗组织名称
     */
    private String fsTopOrgName;

    /**
     * 付呗组织id
     */
    private String fsTopOrgId;

    /* ---------------------------------中台信息------------------------------  */

    /**
     * 中台钱包ID
     */
    private String acctAppid;

    /**
     * 平台钱包列表
     */
    private List<BlocDetailModel.PlatformWalletModel> platformWalletList;

    /**
     * 平台钱包信息
     */
    @Data
    public static class PlatformWalletModel {

        /**
         * 平台类型
         */
        private String channelType;

        /**
         * 平台钱包ID
         */
        private String platformWalletId;

        /**
         * 关联平台ID
         */
        private String relationBalanceId;

        /**
         * 签约业务id
         */
        private String businessSignId;

        /**
         * 签约状态
         */
        private String signStatus;

        /**
         * 签约失败原因
         */
        private String reason;

        /**
         * 签约URL
         */
        private String signUrl;
    }
}
