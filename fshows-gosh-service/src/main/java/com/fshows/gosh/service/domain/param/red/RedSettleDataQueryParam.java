package com.fshows.gosh.service.domain.param.red;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ProductQueryParam
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RedSettleDataQueryParam extends RedBaseParam {

    private static final long serialVersionUID = -6624304537602150999L;
    /**
     * 指定订单ID
     */
    @JSONField(name = "order_id")
    private String orderId;

    /**
     * 商家ID
     */
    @JSONField(name = "seller_id")
    private String sellerId;

    /**
     * 结算开始时间(时间戳)
     */
    @JSONField(name = "settle_start_time")
    private Long settleStartTime;

    /**
     * 结算结束时间(时间戳)
     */
    @JSONField(name = "settle_start_to")
    private Long settleStartTo;

    /**
     * 当前页码
     */
    @JSONField(name = "page_num")
    private Integer pageNum;

    /**
     * 每页记录数
     */
    @J<PERSON>NField(name = "page_size")
    private Integer pageSize;

    /**
     * 结算业务类型
     * 0:正向结算 1:逆向结算
     */
    @JSONField(name = "settle_biz_type")
    private Integer settleBizType;

    /**
     * 结算状态
     * 0:未结算 1:已结算
     */
    @JSONField(name = "common_settle_status")
    private Integer commonSettleStatus;
}