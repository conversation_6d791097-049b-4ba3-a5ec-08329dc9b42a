package com.fshows.gosh.service.domain.model.operation.productOrder;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OperationProductOrderDetailModel {

    /**
     * 订单编号（展示）
     */
    private String outOrderSn;

    /**
     * 商品单号（查询详情使用）
     */
    private String itemOrderId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品类型
     * 1 团购券
     * 11 代金券
     * 12 预售券
     * 15 次卡
     */
    private Integer productType;

    /**
     * 售卖金额（原价）
     */
    private BigDecimal originPrice;

    /**
     * 优惠金额
     */
    private BigDecimal discountPrice;

    /**
     * 订单实收
     */
    private BigDecimal price;

    /**
     * 订单状态
     * 1 未核销
     * 2 已核销
     * 3 已退款
     */
    private Integer orderStatus;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 顾客手机号
     */
    private String purchasePhone;

    /**
     * 所属组织列表
     */
    private List<String> orgNameList;

    /**
     * 商品券码
     */
    private String couponCode;

    /**
     * 核销组织名称
     */
    private String verifyOrgName;

    /**
     * 核销门店名称
     */
    private String verifyStoreName;

    /**
     * 核销时间
     */
    private String verifyTime;

    /**
     * 核销人名称
     */
    private String verifyOperatorName;

    /**
     * 退款申请时间
     */
    private String refundApplyTime;

    /**
     * 退款完成时间
     */
    private String refundFinishTime;

    /**
     * 发布平台
     */
    private String channelType;
}
