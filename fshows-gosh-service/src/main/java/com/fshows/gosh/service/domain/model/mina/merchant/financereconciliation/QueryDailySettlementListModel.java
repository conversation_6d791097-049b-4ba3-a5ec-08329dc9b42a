/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.service.domain.model.mina.merchant.financereconciliation;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version QueryDailySettlementListModel.java, v 0.1 2024-07-18 2:49 PM ruanzy
 */
@Data
public class QueryDailySettlementListModel {

    /**
     * 核销时间（例:2024-05-21）
     */
    private String verifyDay;

    /**
     * 结算金额
     */
    private BigDecimal settledAmount;

    /**
     * 券售卖金额
     */
    private BigDecimal couponSaleAmount;

    /**
     * 结算状态 1：待结算 2：结算中 3：已结算
     */
    private Integer settledStatus;

    /**
     * 结算时间展示文案（例:预计05-23结算）
     */
    private String settledTimeDesc;
}