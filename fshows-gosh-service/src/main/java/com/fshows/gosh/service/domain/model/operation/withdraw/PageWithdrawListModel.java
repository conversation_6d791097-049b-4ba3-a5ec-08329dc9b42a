/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.service.domain.model.operation.withdraw;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version PageWithdrawListModel.java, v 0.1 2024-08-28 11:09 AM ruanzy
 */
@Data
public class PageWithdrawListModel {

    /**
     * 提现时间
     */
    private String createTime;

    /**
     * 平台类型 TIKTOK-抖音 ALIPAY-支付宝 MEITUAN-美团
     */
    private String platformType;

    /**
     * 提现方式 1-自动提现 2-手动提现
     */
    private Integer withdrawMode;

    /**
     * 提现完成时间
     */
    private String finishTime;

    /**
     * 提现订单号
     */
    private String withdrawNo;

    /**
     * 店铺id
     */
    private String shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 集团名称
     */
    private String blocName;

    /**
     * 提现金额
     */
    private BigDecimal withdrawAmount;

    /**
     * 提现状态 1-提现中 2-提现成功 3-提现失败 4-退票
     */
    private Integer status;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 开户名称
     */
    private String accountName;

    /**
     * 银行卡号
     */
    private String settleAccountNo;

    /**
     * 失败原因
     */
    private String rejectedReason;

    /**
     * 是否可以重新提现 1-是 2-否
     */
    private Integer isRewithdraw;

    /**
     * 可用余额，单位：分
     */
    private Long currentBalance;
}