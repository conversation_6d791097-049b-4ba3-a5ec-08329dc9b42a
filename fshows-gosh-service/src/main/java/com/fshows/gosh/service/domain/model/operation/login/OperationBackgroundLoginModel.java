/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.service.domain.model.operation.login;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version OperationBackgroundLoginModel.java, v 0.1 2024-08-26 1:55 PM ruanzy
 */
@Data
public class OperationBackgroundLoginModel {

    /**
     * 授权token
     */
    private String accessToken;

    /**
     * 运营用户id
     */
    private String operatorId;

    /**
     * 账号
     */
    private String account;

    /**
     * 是否为管理员 1-是 2-否
     */
    private Integer isAdmin;

    /**
     * 账号类型:1-全集团账号；2-部分集团权限账号
     */
    private Integer accountType;

    /**
     * 按钮列表
     */
    private List<String> funcList;

    /**
     * 菜单列表
     */
    private List<String> menuList;
}