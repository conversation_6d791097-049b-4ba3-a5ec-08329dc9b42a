/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.gosh.service.domain.param.mina.merchant.verifycoupon;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version VerifyCouponParam.java, v 0.1 2024-06-07 11:30 AM ruanzy
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VerifyCouponParam extends BaseVerifyCodeParam {

    /**
     * 验券的标识
     */
    private String verifyToken;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 子商品 id
     */
    private String subOrderId;

    /**
     * 可用券列表
     */
    private List<VerifyCouponCertificatesParam> certificates;

    /**
     * 核销门店名称
     */
    private String thirdStoreName;

    /**
     * 核销人id
     */
    private String thirdOperatorId;

    /**
     * 核销人名称
     */
    private String thirdOperatorName;

    /**
     * 核销来源 1-来团呗 2-收银台 3-saas接口核销 4-支付宝小程序自主激活 5-来逛呗
     */
    private Integer verifySource;
}