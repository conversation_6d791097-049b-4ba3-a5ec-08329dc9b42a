package com.fshows.gosh.service.domain.param.bloc.shop;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年06月05日 20:30
 */
@Data
public class EditShopParam {

    /**
     * 商铺id（无则新增有则修改）
     */
    private String shopId;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 广场id
     */
    private String orgId;

    /**
     * 一级类目Id
     */
    private String oneCategoryId;

    /**
     * 二级类目Id
     */
    private String twoCategoryId;

    /**
     * 三级类目id
     */
    private String threeCategoryId;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 运营人员姓名
     */
    private String adminName;

    /**
     * 运营人员手机号
     */
    private String adminPhone;

    /**
     * 集团id 当前登录的集团id
     */
    private String blocId;

    /**
     * 门店类目
     */
    private String categoryName;

    /**
     * 店铺所属广场名称
     */
    private String orgName;

    /**
     * 广场下商铺poiId
     */
    private String mallPoiStr;



}
