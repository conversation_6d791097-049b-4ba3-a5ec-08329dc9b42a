package com.fshows.gosh.service.domain.model.operation.refund;

import lombok.Data;

import java.util.Date;

/**
 * OperationRecordModel
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/14
 */
@Data
public class OperationRecordModel {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 记录 id
     */
    private String recordId;

    /**
     * 打款 id
     */
    private String paymentId;

    /**
     * 操作类型 1 发起团款申请 2 重新发起退款申请 3 打款完成 4 打款失败
     */
    private Integer operationType;

    /**
     * 操作类型名称
     */
    private String operationTypeName;

    /**
     * 详情内容（json）
     */
    private String detailContent;
}
