package com.fshows.gosh.service.domain.model.operation.storeReconciliation;

import com.huike.nova.common.constant.CommonConstant;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class StoreStatisticsModel {

    /**
     * 核销券数
     */
    private Integer verifyCouponCount;

    /**
     * 门店应得
     */
    private BigDecimal shopNetAmount;

    /**
     * 初始化数据
     *
     * @return
     */
    public static StoreStatisticsModel init() {
        StoreStatisticsModel model = new StoreStatisticsModel();
        model.setVerifyCouponCount(CommonConstant.ZERO);
        model.setShopNetAmount(BigDecimal.ZERO);
        return model;
    }
}
