package com.fshows.gosh.service.business.mina.merchant.common.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.annimon.stream.function.Consumer;
import com.fshows.gosh.service.business.common.ChannelApiConfigService;
import com.fshows.gosh.service.business.mina.merchant.GrouponCouponVerifyRuleService;
import com.fshows.gosh.service.business.mina.merchant.common.VerifyCouponCommonService;
import com.fshows.gosh.service.business.openapi.TiktokService;
import com.fshows.gosh.service.business.thirdparty.NovaApiService;
import com.fshows.gosh.service.domain.mapper.mina.merchant.VerifyCouponServiceObjMapper;
import com.fshows.gosh.service.domain.model.bloc.product.ImageListModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.CertificatesModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.CloseOrderQueryModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.CouponVerifyResultsModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.ScanCodeCouponModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.ScanCodeGoodsModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.ScanCodeModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.ScanCodeStoreModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.ServiceCertificatesDataModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.ServiceCertificatesModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.ServiceCertificatesSkuModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.ServiceCertificatesVerifyResultModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.ServiceProviderSyncOrderExtraModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.SettlementQykCommissionModel;
import com.fshows.gosh.service.domain.model.mina.merchant.verifycoupon.VerifyCouponModel;
import com.fshows.gosh.service.domain.param.common.tiktokopen.CloseOrderCertificateVerifyParam;
import com.fshows.gosh.service.domain.param.common.tiktokopen.CloseOrderQueryParam;
import com.fshows.gosh.service.domain.param.mina.merchant.verifycoupon.BaseVerifyCodeParam;
import com.fshows.gosh.service.domain.param.mina.merchant.verifycoupon.InputCodeParam;
import com.fshows.gosh.service.domain.param.mina.merchant.verifycoupon.PrepareDeliveryParam;
import com.fshows.gosh.service.domain.param.mina.merchant.verifycoupon.ScanCodeParam;
import com.fshows.gosh.service.domain.param.mina.merchant.verifycoupon.SettlementQykCommissionParam;
import com.fshows.gosh.service.domain.param.mina.merchant.verifycoupon.VerifyCouponCertificatesParam;
import com.fshows.gosh.service.domain.param.mina.merchant.verifycoupon.VerifyCouponParam;
import com.fshows.gosh.service.domain.param.thirdparty.RepairClosedLoopOrderParam;
import com.fshows.gosh.service.domain.result.channel.TiktokOpenConfig;
import com.huike.nova.common.config.SysConfig;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.enums.CancelStatusEnum;
import com.huike.nova.common.enums.ChannelCodeEnum;
import com.huike.nova.common.enums.ChannelTypeEnum;
import com.huike.nova.common.enums.CouponVerifySourceEnum;
import com.huike.nova.common.enums.DouyinOpenApiErrorCodeEnum;
import com.huike.nova.common.enums.DyProductTypeEnum;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.OrderGoodsVerifyStatusEnum;
import com.huike.nova.common.enums.OrgProductTypeEnum;
import com.huike.nova.common.enums.ProductTypeEnum;
import com.huike.nova.common.enums.ServiceProviderCouponStatusEnum;
import com.huike.nova.common.enums.VerifyCouponRecordStatusEnum;
import com.huike.nova.common.enums.VerifyGoodsModeEnum;
import com.huike.nova.common.enums.takeout.CloseGroupBuyingCertificateStatusEnum;
import com.huike.nova.common.enums.takeout.CloseOrderStatusEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.AliyunOssUtil;
import com.huike.nova.common.util.BizNoBuildUtil;
import com.huike.nova.common.util.ExceptionUtil;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.RedisLockHelper;
import com.huike.nova.dao.entity.AilikeBGoodsDetailDO;
import com.huike.nova.dao.entity.AilikeBOrderDO;
import com.huike.nova.dao.entity.AilikeMerchantStoreDO;
import com.huike.nova.dao.entity.AilikeServiceProviderCouponVerifyDO;
import com.huike.nova.dao.entity.AilikeTiktokLifeProductDO;
import com.huike.nova.dao.entity.AilikeTiktokServiceProviderDO;
import com.huike.nova.dao.entity.VerifyCouponRecordDO;
import com.huike.nova.dao.repository.AilikeBGoodsDetailDAO;
import com.huike.nova.dao.repository.AilikeBOrderDAO;
import com.huike.nova.dao.repository.AilikeMerchantStoreDAO;
import com.huike.nova.dao.repository.AilikeServiceProviderCouponVerifyDAO;
import com.huike.nova.dao.repository.AilikeTiktokLifeProductDAO;
import com.huike.nova.dao.repository.AilikeTiktokLifeProductStoreDAO;
import com.huike.nova.dao.repository.AilikeTiktokServiceProviderDAO;
import com.huike.nova.dao.repository.VerifyCouponRecordDAO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Nonnull;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 闭环订单核销服务
 *
 * <AUTHOR> (<EMAIL>)
 * @version TiktokCouponCloseServiceImpl.java, v1.0 11/26/2024 6:28 PM John Exp$
 */
@Slf4j
@Service
@AllArgsConstructor
public class TiktokCouponCloseServiceImpl implements VerifyCouponCommonService {

    private final SysConfig sysConfig;

    private TiktokService tiktokOpenService;

    private ChannelApiConfigService channelApiConfigService;

    private GrouponCouponVerifyRuleService grouponCouponVerifyRuleService;

    private NovaApiService novaApiService;

    private RedissonClient redissonClient;

    private final AilikeBOrderDAO ailikeBOrderDAO;

    private final AilikeMerchantStoreDAO ailikeMerchantStoreDAO;

    private final AilikeTiktokLifeProductDAO ailikeTiktokLifeProductDAO;

    private final AilikeTiktokLifeProductStoreDAO ailikeTiktokLifeProductStoreDAO;

    private final AilikeBGoodsDetailDAO ailikeBGoodsDetailDAO;

    private final AilikeServiceProviderCouponVerifyDAO ailikeServiceProviderCouponVerifyDAO;

    private final VerifyCouponRecordDAO verifyCouponRecordDAO;

    private final AilikeTiktokServiceProviderDAO ailikeTiktokServiceProviderDAO;

    private final VerifyCouponServiceObjMapper verifyCouponServiceObjMapper;

    private final TransactionTemplate transactionTemplate;

    @Override
    public VerifyCouponModel verifyCoupon(VerifyCouponParam param) {
        LogUtil.info(log, "TiktokCouponCloseServiceImpl.verifyCoupon >> 闭环核销 >> {}", JSONObject.toJSONString(param));
        String orderId = param.getOrderId();
        String subOrderId = param.getSubOrderId();
        if (StringUtils.isBlank(orderId)) {
            throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("订单号不能为空");
        }
        try (RedisLockHelper locker = RedisLockHelper.create(redissonClient, RedisPrefixConstant.LOCK_VERIFY_CALL_BACK, orderId)) {
            locker.tryLock();
            // 查询通道配置
            AilikeBOrderDO orderDO = ailikeBOrderDAO.getOrderByOutOrderSn(orderId);
            if (Objects.isNull(orderDO)) {
                throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("支付订单信息查询失败");
            }
            // 检查是否在核销白名单内
            grouponCouponVerifyRuleService.checkVerifyPermission(orderDO);

            // 获取集团信息
            final AilikeTiktokServiceProviderDO serviceProviderDO = ailikeTiktokServiceProviderDAO.getByAppId(param.getAppId());
            if (Objects.isNull(serviceProviderDO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("抖音服务商信息查询失败");
            }

            if (StringUtils.isNotBlank(subOrderId)){

            }

            // 抖音来客Id
            String lifeAccountId = getDouyinLifeAccountId(serviceProviderDO);
            CloseOrderQueryParam closeOrderQueryParam = new CloseOrderQueryParam();
            closeOrderQueryParam.setOrderId(StringUtils.isNotBlank(subOrderId)? subOrderId : orderId);
            closeOrderQueryParam.setAccountId(lifeAccountId);
            CloseOrderQueryModel result = tiktokOpenService.queryOrderCL(closeOrderQueryParam, getTiktokConfig());
            List<CloseOrderQueryModel.OrdersDTO> ordersDTO = result.getOrders();
            if (CollectionUtil.isEmpty(ordersDTO)) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("订单信息不存在");
            }
            // 与校验订单信息
            CloseOrderQueryModel.OrdersDTO orderDTO = CollectionUtil.getFirst(ordersDTO);
            this.verifyPrepareCheck(orderDTO, param);
            CloseOrderQueryModel.OrdersDTO.ProductDTO productDTO = CollectionUtil.getFirst(orderDTO.getProducts());
            // 核销订单
            List<String> encryptedCodesList = param.getCertificates().stream()
                    .map(VerifyCouponCertificatesParam::getEncryptedCode)
                    .collect(Collectors.toList());
            CloseOrderCertificateVerifyParam reqParam = new CloseOrderCertificateVerifyParam();
            reqParam.setVerifyToken(param.getVerifyToken());
            reqParam.setPoiId(param.getPoiId());
            reqParam.setEncryptedCodes(encryptedCodesList);

            VerifyCouponModel model = new VerifyCouponModel();
            ServiceCertificatesVerifyResultModel response = null;
            // 核销验券
            try {
                response = tiktokOpenService.certificateVerifyCL(reqParam, getTiktokConfig());
            } catch (CommonException ex) {
                // 抖音接口返回的实际错误，在SubCode中
                DouyinOpenApiErrorCodeEnum errorCodeEnum = DouyinOpenApiErrorCodeEnum.getByValue(ex.getSubCode());
                // 如果错误是 系统繁忙请稍后再试 或者是 请求太过频繁，则需要进行重试
                if (errorCodeEnum == DouyinOpenApiErrorCodeEnum.SYSTEM_BUSY || errorCodeEnum == DouyinOpenApiErrorCodeEnum.REQUEST_TOO_FREQUENT) {
                    LogUtil.warn(log, "TiktokCouponCloseServiceImpl.verifyCoupon >> 核销异常, 参数: {}, 错误: {}", JSONObject.toJSONString(param), ex.getMessage());

                    // 否则，直接抛出错误来
                } else {
                    throw ex;
                }
            }
            // 如果未获取到数据，并且还是到了这里，检查返回是否正常
            int retryTimes = 3;
            while (response == null && retryTimes > 0) {
                // 等待1秒后重试
                Consumer.Util.safe(o -> Thread.sleep(1000)).accept(null);
                --retryTimes;
                // 这里按照正常的情况来判断，不再做异常兜底
                try {
                    response = tiktokOpenService.certificateVerifyCL(reqParam, getTiktokConfig());
                } catch (CommonException ex) {
                    // 抖音接口返回的实际错误，在SubCode中
                    DouyinOpenApiErrorCodeEnum errorCodeEnum = DouyinOpenApiErrorCodeEnum.getByValue(ex.getSubCode());
                    // 判断是否出现系统繁忙 Or 请求频繁的报错
                    if (errorCodeEnum == DouyinOpenApiErrorCodeEnum.SYSTEM_BUSY || errorCodeEnum == DouyinOpenApiErrorCodeEnum.REQUEST_TOO_FREQUENT) {
                        // 还有重试次数，进行重试
                        if (retryTimes > 0) {
                            continue;
                        }
                    }
                    // 否则直接抛出异常
                    throw ex;
                }
            }
            // 无验券数据
            if (response == null) {
                model.setVerifyNumber(0);
                model.setVerifyResults(new ArrayList<>(0));
                return model;
            }
            List<CouponVerifyResultsModel> verifyResults = new ArrayList<>();
            int verifyNumber = 0;
            for (ServiceCertificatesVerifyResultModel.VerifyResult vr : response.getVerifyResults()) {
                CouponVerifyResultsModel r = new CouponVerifyResultsModel();
                r.setCertificateCode(vr.getOriginCode());
                r.setOrderId(param.getOrderId());
                r.setResultCode(vr.getResult());
                r.setResultMsg(vr.getMsg());
                r.setCertificateId(vr.getCertificateId());
                r.setVerifyId(vr.getVerifyId());
                r.setCertificateId(vr.getCertificateId());
                verifyResults.add(r);
                if (Objects.equals(vr.getResult(), CommonConstant.SUCCESS_CODE)) {
                    ++verifyNumber;
                }
            }
            model.setVerifyNumber(verifyNumber);
            model.setVerifyResults(verifyResults);
            // 保存核券数据
            this.saveVerifyCouponRecord(param, orderDO, productDTO, verifyResults);
            return model;
        } catch (CommonException ex) {
            LogUtil.warn(log, "TiktokCouponCloseServiceImpl.verifyCoupon >> 闭环核销 >> 业务异常:", ex);
            throw ex;
        } catch (Exception ex) {
            LogUtil.warn(log, "TiktokCouponCloseServiceImpl.verifyCoupon >> 闭环核销 >> 通用异常:", ex);
            throw ExceptionUtil.toCommonException(ex);
        }
    }

    @Override
    public SettlementQykCommissionModel settlementQykCommission(SettlementQykCommissionParam param) {
        return null;
    }

    @Override
    public ScanCodeModel scanCode(ScanCodeParam param) {
        LogUtil.info(log, "TiktokCouponCloseServiceImpl.scanCode >> 闭环扫码验券 >> {}", JSONObject.toJSONString(param));
        return prepareDelivery(param);
    }

    @Override
    public ScanCodeModel inputCode(InputCodeParam param) {
        LogUtil.info(log, "TiktokCouponCloseServiceImpl.inputCode >> 闭环输入券码 >> {}", JSONObject.toJSONString(param));
        // 获得CODE
        String couponCode = param.getCode();
        try (RedisLockHelper locker = RedisLockHelper.create(redissonClient, RedisPrefixConstant.LOCK_INPUT_CODE, couponCode)) {
            locker.tryLock();
            // 验证券码信息, 查询是否有核销成功的记录
            AilikeServiceProviderCouponVerifyDO couponVerifyDO = ailikeServiceProviderCouponVerifyDAO.findByCouponCode(couponCode);
            if (null != couponVerifyDO) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("该券码已核销");
            }
            return prepareDelivery(verifyCouponServiceObjMapper.toScanCodeParam(param));
        } catch (CommonException ex) {
            throw ex;
        } catch (Exception ex) {
            throw ExceptionUtil.toCommonException(ex);
        }
    }

    /**
     * 保存核销记录
     *
     * @param verifyCouponParam 验券参数
     * @param orderDO 订单对象
     * @param productDTO 商品记录
     * @param verifyResults 核销结果
     */
    private void saveVerifyCouponRecord(VerifyCouponParam verifyCouponParam, AilikeBOrderDO orderDO, CloseOrderQueryModel.OrdersDTO.ProductDTO productDTO, List<CouponVerifyResultsModel> verifyResults) {
        AilikeMerchantStoreDO merchantStoreDO = ailikeMerchantStoreDAO.getStoreInfo(verifyCouponParam.getStoreId());
        AilikeTiktokServiceProviderDO serviceProviderDO = ailikeTiktokServiceProviderDAO.getByAppId(verifyCouponParam.getAppId());
        AilikeTiktokLifeProductDO productDO = ailikeTiktokLifeProductDAO.getSnapshotProductByProductId(productDTO.getProductId());
        transactionTemplate.execute(o -> {
            for (CouponVerifyResultsModel verifyRecord : verifyResults) {
                // 券Id
                String certificateId = verifyRecord.getCertificateId();
                // 商品单号
                String itemOrderId = null;
                AilikeBGoodsDetailDO goodsDetailDO = ailikeBGoodsDetailDAO.getGoodsDetailByCertificateId(certificateId);
                if (Objects.nonNull(goodsDetailDO)) {
                    itemOrderId = goodsDetailDO.getItemOrderId();

                    AilikeBGoodsDetailDO updateGoodsDetailDO = new AilikeBGoodsDetailDO();
                    updateGoodsDetailDO.setId(goodsDetailDO.getId());
                    updateGoodsDetailDO.setVerifyStatus(OrderGoodsVerifyStatusEnum.FINISH_VERIFY.getValue());
                    updateGoodsDetailDO.setCouponCode(verifyRecord.getCertificateCode());
                    ailikeBGoodsDetailDAO.updateById(updateGoodsDetailDO);
                }

                Date verifyTime = new Date();
                VerifyCouponRecordDO couponRecordDO = new VerifyCouponRecordDO();
                AilikeServiceProviderCouponVerifyDO svcProviderCouponVerifyDO = new AilikeServiceProviderCouponVerifyDO();
                VerifyCouponRecordStatusEnum verifySuccessState =
                        Objects.equals(verifyRecord.getResultCode(), CommonConstant.SUCCESS_CODE) ? VerifyCouponRecordStatusEnum.SUCCESS : VerifyCouponRecordStatusEnum.FAIL;
                // 来团呗核销结果
                {
                    couponRecordDO.setOrderSn(orderDO.getOrderSn());
                    couponRecordDO.setOutOrderSn(orderDO.getOutOrderSn());
                    couponRecordDO.setCertificateCode(verifyRecord.getCertificateCode());
                    couponRecordDO.setCertificateId(verifyRecord.getCertificateId());
                    couponRecordDO.setVerifyToken(verifyCouponParam.getVerifyToken());
                    couponRecordDO.setGoodsMode(VerifyGoodsModeEnum.CLOSE.getValue());
                    couponRecordDO.setVerifyTime(String.valueOf(verifyTime.getTime()));
                    // 核销成功或者失败
                    couponRecordDO.setVerifyStatus(verifySuccessState.getValue());
                    // 核销结果
                    couponRecordDO.setResultCode(verifySuccessState.toString());
                    couponRecordDO.setStoreId(verifyCouponParam.getStoreId());
                    couponRecordDO.setItemOrderId(itemOrderId);
                    if (Objects.nonNull(merchantStoreDO)) {
                        couponRecordDO.setMerchantId(merchantStoreDO.getMerchantId());
                    }
                }
                // 来逛呗核销结果
                {
                    svcProviderCouponVerifyDO.setBusinessOrderSn(BizNoBuildUtil.build());
                    svcProviderCouponVerifyDO.setItemOrderId(itemOrderId);
                    svcProviderCouponVerifyDO.setCouponCode(verifyRecord.getCertificateCode());
                    svcProviderCouponVerifyDO.setOrderId(verifyRecord.getOrderId());
                    svcProviderCouponVerifyDO.setVerifyId(verifyRecord.getVerifyId());
                    svcProviderCouponVerifyDO.setCertificateId(verifyRecord.getCertificateId());
                    svcProviderCouponVerifyDO.setAppId(verifyCouponParam.getAppId());
                    if (Objects.nonNull(serviceProviderDO)) {
                        svcProviderCouponVerifyDO.setMerchantAgentId(serviceProviderDO.getMerchantAgentId());
                        svcProviderCouponVerifyDO.setTiktokLifeId(serviceProviderDO.getTiktokLifeId());
                    }
                    svcProviderCouponVerifyDO.setVerificationStoreId(verifyCouponParam.getPoiId());
                    if (Objects.nonNull(merchantStoreDO)) {
                        svcProviderCouponVerifyDO.setVerificationStoreName(merchantStoreDO.getMerchantStoreName());
                    }
                    svcProviderCouponVerifyDO.setThirdUid(verifyCouponParam.getThirdUid());
                    svcProviderCouponVerifyDO.setThirdStoreId(verifyCouponParam.getThirdStoreId());
                    svcProviderCouponVerifyDO.setThirdStoreId(verifyCouponParam.getThirdStoreId());
                    svcProviderCouponVerifyDO.setThirdStoreName(verifyCouponParam.getThirdStoreName());
                    svcProviderCouponVerifyDO.setThirdOperatorId(verifyCouponParam.getThirdOperatorId());
                    svcProviderCouponVerifyDO.setThirdOperatorName(verifyCouponParam.getThirdOperatorName());
                    // 已核销
                    svcProviderCouponVerifyDO.setCouponStatus(ServiceProviderCouponStatusEnum.USED.getValue());
                    svcProviderCouponVerifyDO.setCancelStatus(CancelStatusEnum.NO.getValue());
                    svcProviderCouponVerifyDO.setVerificationTime(verifyTime);

                    svcProviderCouponVerifyDO.setTiktokProductId(productDTO.getProductId());
                    svcProviderCouponVerifyDO.setTiktokProductName(productDTO.getProductName());
                    if (Objects.nonNull(productDO)) {
                        svcProviderCouponVerifyDO.setBusinessProductId(productDO.getBusinessProductId());
                        svcProviderCouponVerifyDO.setAppId(productDO.getAppId());
                        svcProviderCouponVerifyDO.setMerchantAgentId(productDO.getMerchantAgentId());
                        svcProviderCouponVerifyDO.setTiktokLifeId(productDO.getTiktokLifeId());
                        svcProviderCouponVerifyDO.setProductType(productDO.getProductType());
                        svcProviderCouponVerifyDO.setOrgProductType(productDO.getOrgProductType());
                        svcProviderCouponVerifyDO.setChannelCode(productDO.getChannelCode());
                        // 商品活动批次Id
                        svcProviderCouponVerifyDO.setBizActivityId(productDO.getBizActivityId());
                    }
                    svcProviderCouponVerifyDO.setChannelType(ChannelTypeEnum.TIKTOK.getValue());
                    svcProviderCouponVerifyDO.setVerifySource(CouponVerifySourceEnum.GOSH.getValue());
                    svcProviderCouponVerifyDO.setPhoneNumber(orderDO.getPurchasePhone());
                    svcProviderCouponVerifyDO.setPayTime(new Date(Long.parseLong(orderDO.getPayTime())));

                    svcProviderCouponVerifyDO.setCouponOriginPrice(goodsDetailDO.getOriginPrice());
                    svcProviderCouponVerifyDO.setCouponSaleAmount(goodsDetailDO.getPrice());
                }
                verifyCouponRecordDAO.save(couponRecordDO);
                ailikeServiceProviderCouponVerifyDAO.save(svcProviderCouponVerifyDO);
            }
            return Boolean.TRUE;
        });
    }

    /**
     * 预检查核销
     *
     * @param certificates 预校验参数
     * @param verifyCodeParam 验券基本参数
     * @return 存在则返回券码信息
     */
    private AilikeTiktokLifeProductDO prepareCheck(ServiceCertificatesDataModel certificates, BaseVerifyCodeParam verifyCodeParam) {
        LogUtil.info(log, "TiktokCouponCloseServiceImpl.prepareCheck >> certificates:{}, baseVerifyCodeParam:{}", JSONObject.toJSONString(certificates), JSONObject.toJSONString(verifyCodeParam));
        // 1、券码检查
        if (CollectionUtil.isEmpty(certificates.getCertificates())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("券码已核销，请勿重复核销");
        }
        // 2、获取首条券码，检查券类型
        ServiceCertificatesModel certificate = CollectionUtil.getFirst(certificates.getCertificates());
        ServiceCertificatesSkuModel sku = certificate.getSku();
        if (Objects.isNull(sku)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("订单中未包含商品信息，请重新验券");
        }
        Integer groupType = sku.getGrouponType();
        DyProductTypeEnum groupTypeEnum = DyProductTypeEnum.getByCode(groupType);
        // 仅支持团购券和代金券核销
        // TODO YXR 2025/7/30 增加权益包类型   
        if (groupTypeEnum != DyProductTypeEnum.GROUPON && groupTypeEnum!= DyProductTypeEnum.VOUCHER) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("仅支持团购券和代金券核销，请使用来客验券");
        }
        String productId = sku.getSkuId();
        AilikeTiktokLifeProductDO productDO = ailikeTiktokLifeProductDAO.getSnapshotProductByProductId(productId);
        if (Objects.isNull(productDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("商品信息未录入，请录入后重试");
        }
        // 如果是商圈母卡，则进行拦截
        // * 非开发环境禁用商圈卡的扫码核销
        if (!sysConfig.isDev()) {
            if (Objects.equals(productDO.getOrgProductType(), OrgProductTypeEnum.BUSINESS_MOTHER_CARD.getValue())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("不支持商圈卡的扫码核销");
            }
        }
        Set<String> storeIdList = new HashSet<>(ailikeMerchantStoreDAO.findStoreIdList(productDO.getBusinessProductId()));
       if (!storeIdList.contains(verifyCodeParam.getStoreId())) {
           throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("该券码在本店不可用");
        }
        // 服务商门店关联记录查询
        // 如果根据根据POI限制类型的商品，核销也要限制
        if (!ailikeTiktokLifeProductStoreDAO.existRecord(productDO.getBusinessProductId(),null, verifyCodeParam.getThirdStoreId())) {
            throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("该券码在本店不可用");
        }
        return productDO;
    }

    /**
     * 核销准备检查
     * 仅检查券维度的状态，不检查商品维度的状态
     *
     * @param orderDTO 查询到的订单信息
     * @param param 核销参数
     */
    private void verifyPrepareCheck(@Nonnull CloseOrderQueryModel.OrdersDTO orderDTO, VerifyCouponParam param) {
        if (CollectionUtil.isEmpty(param.getCertificates())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("券码信息不存在");
        }
        CloseOrderStatusEnum orderStatusEnum = CloseOrderStatusEnum.getByValue(orderDTO.getOrderStatus());
        // 订单状态必须为待使用
        if (orderStatusEnum != CloseOrderStatusEnum.TO_BE_USED) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("订单状态错误")
                    .extra("核销验证,订单状态不为\"待使用\",请检查订单,订单号:{},订单状态:{}", orderDTO.getOrderId(),
                            ObjectUtil.defaultIfNull(orderStatusEnum, CloseOrderStatusEnum.INITIALISATION).getName());
        }

        // 获得订单的首条记录
        Map<String, CloseOrderQueryModel.OrdersDTO.CertificateDTO> itemOrderId2CertificateDTOMap = orderDTO.getCertificate().stream().collect(Collectors.toMap(
                CloseOrderQueryModel.OrdersDTO.CertificateDTO::getOrderItemId, Function.identity(), (o1, o2) -> o1)
        );
        // 券码遍历
        for (VerifyCouponCertificatesParam item: param.getCertificates()) {
            // 根据商品单Id查询卡券状态
            if (!itemOrderId2CertificateDTOMap.containsKey(item.getItemOrderId())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("参数错误，子订单({})不属于该订单", item.getItemOrderId());
            }
            CloseOrderQueryModel.OrdersDTO.CertificateDTO certificateDTO = itemOrderId2CertificateDTOMap.get(item.getItemOrderId());
            // 获得订单状态
            CloseGroupBuyingCertificateStatusEnum statusEnum = CloseGroupBuyingCertificateStatusEnum.getByValue(certificateDTO.getItemStatus());
            if (statusEnum != CloseGroupBuyingCertificateStatusEnum.TO_BE_USED) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("券状态错误，子订单状态不可使用", item.getItemOrderId());
            }
        }

    }

    /**
     * 获得核销的抖音来客Id
     *
     * @param serviceProviderDO 抖音服务配置表
     * @return 抖音来客Id
     */
    private String getDouyinLifeAccountId(AilikeTiktokServiceProviderDO serviceProviderDO) {
        if (Objects.isNull(serviceProviderDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("服务商配置表不能为空");
        }
        String lifeAccountId = null;
        // 如果SyncOrderExtra参数不为空,优先获取
        if (StringUtils.isNotBlank(serviceProviderDO.getSyncOrdersExtra())) {
            // 转JSON转换
            try {
                ServiceProviderSyncOrderExtraModel model = JSONObject.parseObject(serviceProviderDO.getSyncOrdersExtra(), ServiceProviderSyncOrderExtraModel.class);
                lifeAccountId = model.getLifeAccountId();
            } catch (Exception ignored) {}
        }
        // 如果获取到的抖音来客Id为空
        if (StringUtils.isBlank(lifeAccountId)) {
            // 获取主号来客Id,如果为空则获取抖音来客Id
            lifeAccountId = StringUtils.defaultIfBlank(serviceProviderDO.getLifeAccountId(),
                    serviceProviderDO.getTiktokLifeId());
        }
        return lifeAccountId;
    }

    /**
     * 验券准备
     *
     * @param param 参数
     * @return 验券参数
     */
    private ScanCodeModel prepareDelivery(ScanCodeParam param) {
        LogUtil.info(log, "TiktokCouponCloseServiceImpl.prepareDelivery >> 闭环验券准备, 参数:{}", JSONObject.toJSONString(param));
        ScanCodeModel result = new ScanCodeModel();
        PrepareDeliveryParam prepareDeliveryParam = new PrepareDeliveryParam();
        prepareDeliveryParam.setCode(param.getCode());
        prepareDeliveryParam.setEncryptedData(param.getEncryptedData());
        // TODO YXR 2025/7/30 查看是否要入参poi_id  （poi 取值问题）
        ServiceCertificatesDataModel prepareDeliveryModel = tiktokOpenService.prepareDeliveryCL(prepareDeliveryParam, getTiktokConfig());
        if (Objects.isNull(prepareDeliveryModel) || CollectionUtil.isEmpty(prepareDeliveryModel.getCertificates())) {
            throw new CommonException(ErrorCodeEnum.TIKTOK_OPEN_ERROR).detailMessage("平台方返回错误")
                    .extra("[验券] 闭环验券数据错误, 券码/加密码:{}, 错误码:{}, 错误信息:{}",
                            StringUtils.defaultIfEmpty(param.getCode(), param.getEncryptedData()), prepareDeliveryModel.getErrorCode(), prepareDeliveryModel.getDescription());
        }
        // 预校验订单
        AilikeTiktokLifeProductDO productDO = prepareCheck(prepareDeliveryModel, param);
        String orderId = prepareDeliveryModel.getOrderId();
        String subOrderId = prepareDeliveryModel.getOrderId();
        List<ServiceCertificatesModel> prepareDeliveryCertificates = prepareDeliveryModel.getCertificates();
        ServiceCertificatesModel certificate = CollectionUtil.getFirst(prepareDeliveryCertificates);
        ServiceCertificatesSkuModel sku = certificate.getSku();
        String accountId = sku.getAccountId();
        // TODO YXR  验券准备返回的订单 id 若是主单的订单 id   此处不用改   若是子单的订单 id 则根据子单订单号做闭环订单主查接口 查询主单订单号  给  orderId重新赋值 （根据闭环订单主查接口返回的source_order_id)
        if (OrgProductTypeEnum.BUSINESS_CHILD_CARD.getValue().equals(productDO.getOrgProductType()) && !CommonConstant.ZERO.equals(productDO.getWalletAccountType())){
            // 认定为是权益包的子单
            CloseOrderQueryParam closeOrderQueryParam = new CloseOrderQueryParam();
            closeOrderQueryParam.setOrderId(orderId);
            closeOrderQueryParam.setAccountId(accountId);
            CloseOrderQueryModel orderResult = tiktokOpenService.queryOrderCL(closeOrderQueryParam, getTiktokConfig());
            List<CloseOrderQueryModel.OrdersDTO> ordersDTO = orderResult.getOrders();
            if (CollectionUtil.isEmpty(ordersDTO)) {
                LogUtil.error(log, "TiktokCouponCloseServiceImpl.prepareDelivery >> 权益包子商品订单信息不存在（平台方查询） >> orderId = {}", orderId);
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("权益包子商品订单信息不存在（平台方查询）");
            }
            String sourceOrderId = orderResult.getOrders().get(0).getSourceOrderId();
            if (StringUtils.isBlank(sourceOrderId)) {
                LogUtil.error(log, "TiktokCouponCloseServiceImpl.prepareDelivery >> 权益包子商品订单未关联主品订单 >> sourceOrderId = {}", sourceOrderId);
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("权益包子商品订单未关联主品订单");
            }
            orderId = sourceOrderId;
        }

        // 查询订单是否在库里存在
        AilikeBOrderDO orderDO = ailikeBOrderDAO.getOrderByOutOrderSn(orderId);
        if (null == orderDO) {
            // 补单操作
            RepairClosedLoopOrderParam closeLoopOrderParam = new RepairClosedLoopOrderParam();
            closeLoopOrderParam.setDouyinOrderId(orderId);
            closeLoopOrderParam.setDouyinLifeAccountId(accountId);
            JSONObject repairResult = novaApiService.repairCloseOrder(closeLoopOrderParam);
            LogUtil.info(log, "TiktokCouponCloseServiceImpl.repairCloseOrder >> 补单结果, {}", JSONObject.toJSONString(repairResult));
            orderDO = ailikeBOrderDAO.getOrderByOutOrderSn(orderId);
            if (null == orderDO) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("未找到指定的订单信息");
            }
        }
        // 检查是否在核销白名单内
        grouponCouponVerifyRuleService.checkVerifyPermission(orderDO);

        ScanCodeGoodsModel goods = new ScanCodeGoodsModel();
        // 商品记录是否存在
        goods.setProductId(productDO.getProductId());
        goods.setActualAmount(productDO.getActualAmount());
        goods.setProductName(productDO.getProductName());
        goods.setOriginAmount(productDO.getOriginAmount());
        ProductTypeEnum productTypeEnum = ProductTypeEnum.getByValue(productDO.getProductType());
        goods.setProductTypeDesc(productTypeEnum.getName());
        goods.setVerifyNumber(CollectionUtil.size(prepareDeliveryCertificates));
        String productAttrs = productDO.getProductAttrs();
        if (StringUtils.isNotBlank(productAttrs)) {
            Consumer.Util.<String>safe(o -> {
                // 处理商品属性JSON
                JSONObject productAttrsJson = JSONObject.parseObject(o);
                String imageUrlList = productAttrsJson.getString("image_list");
                List<ImageListModel> imageList = JSONObject.parseArray(imageUrlList, ImageListModel.class);
                if (CollectionUtil.isNotEmpty(imageList)) {
                    // 获得第一张图片
                    ImageListModel imageListModel = imageList.get(0);
                    if (StringUtils.isNotBlank(imageListModel.getUrl())) {
                        if (StringUtils.isNotBlank(sysConfig.getOssImageThumbnailSmall())) {
                            if (StringUtils.contains(imageListModel.getUrl(), StringPool.QUESTION_MARK)) {
                                goods.setImageUrl(imageListModel.getUrl() + sysConfig.getOssImageThumbnail());
                            }
                        }
                        if (StringUtils.isBlank(goods.getImageUrl())) {
                            goods.setImageUrl(imageListModel.getUrl());
                        }
                    }
                }
            }).accept(productAttrs);
        }


        if (StringUtils.isNotBlank(productDO.getOssImgUrlList())) {
            Consumer.Util.<String>safe(json -> {
                String ossUrl = CollectionUtil.getFirst(JSONArray.parseArray(json, String.class));
                String url = AliyunOssUtil.getOssThumbnailUrl(ossUrl, sysConfig.getOssImageThumbnail());
                goods.setImageUrl(url);
            }).accept(productDO.getOssImgUrlList());
        }
        result.setChannelType(productDO.getChannelType());
        result.setOrgProductType(productDO.getOrgProductType());

        ScanCodeCouponModel coupon = new ScanCodeCouponModel();
        coupon.setOrderId(orderId);
        coupon.setSubOrderId(subOrderId);
        coupon.setCouponType(sku.getGrouponType());
        coupon.setVerifyToken(prepareDeliveryModel.getVerifyToken());
        List<CertificatesModel> certificates = new ArrayList<>();
        if (OrgProductTypeEnum.BUSINESS_CHILD_CARD.getValue().equals(productDO.getOrgProductType()) && !CommonConstant.ZERO.equals(productDO.getWalletAccountType())){
        //     获得子单的券信息
            CertificatesModel certificatesModel = new CertificatesModel();
            certificatesModel.setEncryptedCode(certificate.getEncryptedCode());
            certificatesModel.setCertificateId(certificate.getCertificateId());
            // TODO YXR 2025/7/30 此处没有设置  ItemOrderId
            // certificatesModel.setItemOrderId(certificate);
            certificates.add(certificatesModel);
        }else {
            // 获得券Id
            List<String> certificateIdList = prepareDeliveryCertificates.stream().map(ServiceCertificatesModel::getCertificateId).collect(Collectors.toList());
            List<AilikeBGoodsDetailDO> goodsDetailDOList = ailikeBGoodsDetailDAO.getOrderDetailByCertificateIdList(certificateIdList);
            // 查询列表，生成券Id 和 商品单记录映射
            Map<String, AilikeBGoodsDetailDO> certificateId2GoodsDetailMap = goodsDetailDOList.stream().collect(
                    Collectors.toMap(AilikeBGoodsDetailDO::getCertificateId, Function.identity(), (l, r) -> r));

            for (ServiceCertificatesModel item : prepareDeliveryModel.getCertificates()) {
                CertificatesModel certificatesModel = new CertificatesModel();
                certificatesModel.setCertificateId(item.getCertificateId());
                certificatesModel.setEncryptedCode(item.getEncryptedCode());
                if (certificateId2GoodsDetailMap.containsKey(item.getCertificateId())) {
                    AilikeBGoodsDetailDO goodsDetailDO = certificateId2GoodsDetailMap.get(item.getCertificateId());
                    certificatesModel.setItemOrderId(goodsDetailDO.getItemOrderId());
                }
                certificates.add(certificatesModel);
            }
        }
        coupon.setCertificates(certificates);


        ScanCodeStoreModel store = new ScanCodeStoreModel();
        if (StringUtils.isNotBlank(param.getStoreId())) {
            AilikeMerchantStoreDO merchantStoreDO = ailikeMerchantStoreDAO.getStoreInfo(param.getStoreId());
            if (null != merchantStoreDO) {
                store.setStoreId(merchantStoreDO.getMerchantStoreId());
                store.setStoreName(merchantStoreDO.getMerchantStoreName());
            }
        }
        result.setGoods(goods);
        result.setStoreInfo(store);
        result.setCoupon(coupon);
        result.setChannelType(ChannelTypeEnum.TIKTOK.getValue());
        result.setStoreInfo(store);
        // 获得订单号
        return result.initExtra();
    }

    /**
     * 抖音开放平台账号，闭环默认只有一个返回
     *
     * @return 开放平台配置
     */
    private TiktokOpenConfig getTiktokConfig() {
        return channelApiConfigService.getByChannelCode(ChannelCodeEnum.TIKTOK_SAAS_CLOSE.getValue());
    }
}
