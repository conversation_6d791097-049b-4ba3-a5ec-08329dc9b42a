/**
 * <AUTHOR>
 * @date 2024/12/26 18:25
 * @version 1.0 ExamineRecordResponse
 */
package com.fshows.gosh.service.domain.model.operation.account;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version ShopOperatorRecordModel.java, v 0.1 2024-12-26 18:25 tuyuwei
 */
@Data
public class ShopOperatorRecordModel {

    /**
     * 操作日志
     */
    private List<ExamineRecordModel> examineRecordResponseList;

    /**
     * 冻结解冻操作日志
     */
    private List<ShopFreezeRecordModel> freezeRecordResponseList;

    @Data
    public static class ShopFreezeRecordModel {
        /**
         * 操作类型
         */
        private int operatorType;

        /**
         * 原因
         */
        private String reason;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 创建时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date createTime;
    }
}